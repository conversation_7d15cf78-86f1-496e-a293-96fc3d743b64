import{u as w,r,l as b,a,c,j as l,D as f,E as v,L as C}from"./index.7dafa16d.js";import{R as S,T as I}from"./index.503eee4e.js";import{C as T}from"./index.519f9d90.js";import{i as L}from"./index.9464998a.js";import"./b-tween.es.d368a2a1.js";const j="_symbol_coctx_1";var D={symbol:j};function F(){const t=w(L),[s,p]=r.exports.useState(0),[u,d]=r.exports.useState([]),[m,n]=r.exports.useState(!0),[o,g]=r.exports.useState(1),[y,x]=r.exports.useState(0),i=r.exports.useCallback(()=>{n(!0),b.get(`/api/workplace/popular-contents?page=${o}&pageSize=5&category=${s}`).then(e=>{d(e.data.list),x(e.data.total)}).finally(()=>{n(!1)})},[o,s]);r.exports.useEffect(()=>{i()},[o,i]);const h=[{title:t["workplace.column.rank"],dataIndex:"rank",width:65},{title:t["workplace.column.title"],dataIndex:"title",render:e=>a(c.Paragraph,{style:{margin:0},ellipsis:!0,children:e})},{title:t["workplace.column.pv"],dataIndex:"pv",width:100,render:e=>`${e/1e3}k`},{title:t["workplace.column.increase"],dataIndex:"increase",sorter:(e,k)=>e.increase-k.increase,width:110,render:e=>l("span",{children:[`${(e*100).toFixed(2)}%`,a("span",{className:D.symbol,children:e<0?a(f,{style:{color:"rgb(var(--green-6))"}}):a(v,{style:{color:"rgb(var(--red-6))"}})})]})}];return l(T,{children:[l("div",{style:{display:"flex",justifyContent:"space-between"},children:[a(c.Title,{heading:6,children:t["workplace.popularContents"]}),a(C,{children:t["workplace.seeMore"]})]}),a(S.Group,{type:"button",value:s,onChange:p,options:[{label:t["workplace.text"],value:0},{label:t["workplace.image"],value:1},{label:t["workplace.video"],value:2}],style:{marginBottom:16}}),a(I,{rowKey:"rank",columns:h,data:u,loading:m,tableLayoutFixed:!0,onChange:e=>{g(e.current)},pagination:{total:y,current:o,pageSize:5,simple:!0}})]})}export{F as default};
