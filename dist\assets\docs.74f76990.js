import{u as n,j as t,a as e,c as o,L as r}from"./index.7dafa16d.js";import{C as d}from"./index.519f9d90.js";import{i as l}from"./index.9464998a.js";const p="_docs_dimkm_1",m="_link_dimkm_5";var i={docs:p,link:m};const h={react:"https://arco.design/react/docs/start",vue:"https://arco.design/vue/docs/start",designLab:"https://arco.design/themes",materialMarket:"https://arco.design/material/"};function g(){const s=n(l);return t(d,{children:[t("div",{style:{display:"flex",justifyContent:"space-between"},children:[e(o.Title,{heading:6,children:s["workplace.docs"]}),e(r,{children:s["workplace.seeMore"]})]}),e("div",{className:i.docs,children:Object.entries(h).map(([a,c])=>e(r,{className:i.link,href:c,target:"_blank",children:s[`workplace.${a}`]},a))})]})}export{g as default};
