import{u as A,r as i,a as s,a5 as b,l as h}from"./index.7dafa16d.js";import m from"./card.cbed5c25.js";const B={"en-US":{"menu.visualization":"Data Visualization","menu.visualization.analysis":"Analysis","dataAnalysis.yesterday":"Yesterday","dataAnalysis.title.publicOpinion":"Public Opinion Analysis","dataAnalysis.publicOpinion.visitor":"Total visitors","dataAnalysis.publicOpinion.content":"Total content publishing","dataAnalysis.publicOpinion.comment":"Total comments","dataAnalysis.publicOpinion.share":"Total share","dataAnalysis.title.publishingRate":"Content publishing rate","dataAnalysis.title.publishingTiming":"Content period analysis","dataAnalysis.title.authorsList":"Top authors list","dataAnalysis.authorTable.rank":"Rank    ","dataAnalysis.authorTable.author":"Author","dataAnalysis.authorTable.content":"Interval volume","dataAnalysis.authorTable.click":"Click volume"},"zh-CN":{"menu.visualization":"\u6570\u636E\u53EF\u89C6\u5316","menu.visualization.analysis":"\u5206\u6790\u9875","dataAnalysis.yesterday":"\u8F83\u6628\u65E5","dataAnalysis.title.publicOpinion":"\u8206\u60C5\u5206\u6790","dataAnalysis.publicOpinion.visitor":"\u8BBF\u95EE\u603B\u4EBA\u6570","dataAnalysis.publicOpinion.content":"\u5185\u5BB9\u53D1\u5E03\u91CF","dataAnalysis.publicOpinion.comment":"\u8BC4\u8BBA\u603B\u91CF","dataAnalysis.publicOpinion.share":"\u5206\u4EAB\u603B\u91CF","dataAnalysis.title.publishingRate":"\u5185\u5BB9\u53D1\u5E03\u6BD4\u4F8B","dataAnalysis.title.publishingTiming":"\u5185\u5BB9\u65F6\u6BB5\u5206\u6790","dataAnalysis.title.authorsList":"\u70ED\u95E8\u4F5C\u8005\u699C\u5355","dataAnalysis.authorTable.rank":"\u6392\u540D","dataAnalysis.authorTable.author":"\u4F5C\u8005","dataAnalysis.authorTable.content":"\u5185\u5BB9\u91CF","dataAnalysis.authorTable.click":"\u70B9\u51FB\u91CF"}},{Row:T,Col:v}=b,l=[{key:"visitor",type:"line"},{key:"content",type:"interval"},{key:"comment",type:"line"},{key:"share",type:"pie"}];function C(){const t=A(B),[o,r]=i.exports.useState(!0),[e,y]=i.exports.useState(l.map(a=>({...a,chartType:a.type,title:t[`dataAnalysis.publicOpinion.${a.key}`]}))),p=async()=>{const a=l.map(async u=>{const{data:d}=await h.get(`/api/data-analysis/overview?type=${u.type}`).catch(()=>({data:{}}));return{...d,key:u.key,chartType:u.type}}),n=await Promise.all(a).finally(()=>r(!1));y(n)};i.exports.useEffect(()=>{p()},[]);const c=i.exports.useMemo(()=>e.map(a=>({...a,title:t[`dataAnalysis.publicOpinion.${a.key}`]})),[t,e]);return s("div",{children:s(T,{gutter:20,children:c.map((a,n)=>s(v,{span:6,children:s(m,{...a,compareTime:t["dataAnalysis.yesterday"],loading:o})},n))})})}var k=Object.freeze(Object.defineProperty({__proto__:null,default:C},Symbol.toStringTag,{value:"Module"}));export{C as P,k as a,B as i};
