import { SetupWorker<PERSON><PERSON>, RequestHandler } from 'msw/browser';

type SetupMswOptions = {
  mock?: boolean;
  handlers: RequestHandler[];
  timeoutMs?: number; // 添加超时选项
};

let worker: SetupWorkerApi | null = null;

export const setupMsw = async (options: SetupMswOptions) => {
  const {
    mock = process.env.NODE_ENV === 'development',
    handlers,
    timeoutMs = 5000, // 默认 5 秒超时
  } = options;

  if (mock === false) return;

  try {
    // 添加超时控制
    const workerPromise = setupWorkerInternal(handlers);
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(new Error('MSW initialization timed out')),
        timeoutMs
      );
    });

    return await Promise.race([workerPromise, timeoutPromise]);
  } catch (error) {
    console.error('[MSW] Failed to start service worker:', error);
    // 设置全局标志，表示 MSW 初始化失败但应用仍可继续
    window['mswInitialized'] = false;
    window['mswError'] = error;
  }
};

// 内部初始化函数
async function setupWorkerInternal(handlers: RequestHandler[]) {
  if (!worker) {
    const { setupWorker } = await import('msw/browser');
    worker = setupWorker(...handlers);
    await worker.start({
      onUnhandledRequest: 'bypass',
    });

    // 设置全局标志，表示 MSW 已初始化
    window['mswInitialized'] = true;

    console.log('[MSW] Mock server started');
  } else {
    worker.resetHandlers(...handlers);
  }

  return worker;
}

export default setupMsw;
