import{R as ie,J as Ie,r as N,b8 as Ce,s as _e,v as q,H as ne,b9 as Se,av as ke,b1 as Qe,b2 as er,b0 as je,a$ as de,b3 as Be,X as oe,$ as He,a0 as me,a as g,F as fe,b5 as rr,aZ as nr,e as Y,j as te,W as $e,t as le,b4 as tr,ba as ar,C as ir,f as lr,a_ as ur,aD as or,aB as sr,aA as cr,o as Oe,aR as dr,bb as fr,az as hr,G as vr,u as gr,a8 as K,aK as pr,k as se,i as br,h as mr,B as xe,M as yr}from"./index.7dafa16d.js";import{S as Cr}from"./index.43d26da3.js";import{l as _r}from"./index.69444b8c.js";var Sr=function(e){var t=e.prefixCls,i=e.multiple,u=e.option,n=e.renderOption,a=e.selected,l=e.icons,r=u.disabled||i&&u.disableCheckbox;return ie.createElement(ie.Fragment,null,i?ie.createElement(Ie,{disabled:r,checked:u._checked,indeterminate:u._halfChecked,onChange:e.onMultipleChecked,value:u.value}):"",ie.createElement("div",{className:t+"-list-item-label",onClick:u.disabled?void 0:e.onClickOption,onMouseEnter:e.onMouseEnter,onDoubleClick:r?void 0:e.onDoubleClickOption},n?n():u.label,u.isLeaf?a&&l.checked:u.loading?l.loading:l.next))},kr=Sr;function wr(e){e===void 0&&(e=[]);var t=N.exports.useRef(e),i=function(u,n){n!==void 0?t.current[n]=u:t.current.push(u)};return[t.current,i]}var U=globalThis&&globalThis.__assign||function(){return U=Object.assign||function(e){for(var t,i=1,u=arguments.length;i<u;i++){t=arguments[i];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},U.apply(this,arguments)},Ee=globalThis&&globalThis.__read||function(e,t){var i=typeof Symbol=="function"&&e[Symbol.iterator];if(!i)return e;var u=i.call(e),n,a=[],l;try{for(;(t===void 0||t-- >0)&&!(n=u.next()).done;)a.push(n.value)}catch(r){l={error:r}}finally{try{n&&!n.done&&(i=u.return)&&i.call(u)}finally{if(l)throw l.error}}return a},ce=globalThis&&globalThis.__spreadArray||function(e,t,i){if(i||arguments.length===2)for(var u=0,n=t.length,a;u<n;u++)(a||!(u in t))&&(a||(a=Array.prototype.slice.call(t,0,u)),a[u]=t[u]);return e.concat(a||Array.prototype.slice.call(t))},Nr=function(){function e(t,i,u){var n=this;this.pathValue=[],this.pathLabel=[],this.config={},this._initNode=function(a,l){l===void 0&&(l=null);var r=n.config,d=r.showEmptyChildren,o=r.lazyload,v=U(U({},ze),n.config.fieldNames),O=a[v.children],D=Array.isArray(O)?d?!1:O.length===0:!0;o&&(v.isLeaf in a?D=!!a[v.isLeaf]:D=!1);var I=a[v.value],S=a[v.label],j=U(U({},a),{value:I,label:S,isLeaf:D,loading:!1,loaded:!1,disabled:l&&l.disabled||a[v.disabled],parent:l,pathValue:l?ce(ce([],Ee(l.pathValue),!1),[I],!1):[I],pathLabel:l?ce(ce([],Ee(l.pathLabel),!1),[S],!1):[S],_level:l?l._level+1:0,_checked:!1,_halfChecked:!1});n._data=U(U({},j),{parent:j.parent&&j.parent._data}),Object.keys(j).forEach(function(E){n[E]=j[E]}),O&&O.length&&(n.children=O.map(function(E,k){return new e(U(U({},E),{_index:k}),n.config,n)}),n._data.children=n.children.map(function(E){return E._data}))},this._isHalfChecked=function(){var a=n.children.reduce(function(l,r){var d=r._halfChecked?.5:r._checked?1:0;return l+d},0);return a!==n.children.length&&a>0},this._setNodeChildrenChecked=function(a,l){!l&&n.disabled||n.children&&n.children.length&&(n.children.forEach(function(r){r.disabled?l&&r.setCheckedStateIgnoreDisabled(a):r.setCheckedState(a)}),n.updateHalfState(a))},this.getSelfChildrenValue=function(){var a=[],l=function(r,d){if(!d||!d.length){a.push(r);return}(d||[]).forEach(function(o){l(o.pathValue,o.children)})};return l(n.pathValue,n.children),a},this.updateHalfState=function(a){n._halfChecked=n._isHalfChecked(),n._checked=n._halfChecked?!1:a},this.setCheckedProperty=function(a){n._checked=a,n._halfChecked=!1},this.setCheckedState=function(a){var l=a?n._checked:!n._checked&&!n._halfChecked;if(!(n.disabled||l)&&(n.setCheckedProperty(a),!n.config.changeOnSelect)){n._setNodeChildrenChecked(a);for(var r=n.parent;r&&!r.disabled;)r.updateHalfState(a),r=r.parent}},this.setCheckedStateIgnoreDisabled=function(a){if(a!==Boolean(n._checked)&&(n.setCheckedProperty(a),!n.config.changeOnSelect)){n._setNodeChildrenChecked(a,!0);for(var l=n.parent;l;)l.updateHalfState(a),l=l.parent}},this.getPathNodes=function(){for(var a=[n],l=n.parent;l;)a.unshift(l),l=l.parent;return a},this.getChildren=function(){return n.children},this.setLoading=function(a){n.loading=a,(a||a===void 0)&&(n.loaded=!1),a===!1&&(n.loaded=!0)},this.config=i||{},this._initNode(t,u||null)}return e}(),Vr=Nr,ue=globalThis&&globalThis.__assign||function(){return ue=Object.assign||function(e){for(var t,i=1,u=arguments.length;i<u;i++){t=arguments[i];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},ue.apply(this,arguments)},Or=function(){function e(t,i,u){var n=this;this.nodes=[],this.flatNodes=[],this.config={},this._calcNodes=function(l,r){return l?l.map(function(d,o){return new Vr(ue(ue({},d),{_index:o}),n.config,r)}):[]},this._updateFlatNodes=function(){var l=!n.config.changeOnSelect;n.flatNodes=[];var r=function(d){!d||((!l||d.isLeaf)&&n.flatNodes.push(d),_e(d.children)&&d.children.forEach(function(o){r(o)}))};n.nodes.forEach(function(d){r(d)})},this.setNodeCheckedByValue=function(l){var r=ge(l);n.flatNodes.forEach(function(d){var o=!1;n.config.showParent?d.pathValue.some(function(v,O,D){return Z(r,D.slice(0,O+1))})&&(o=!0):Z(r,d.pathValue)&&(o=!0),d.setCheckedStateIgnoreDisabled(o)})},this.appendOptionChildren=function(l,r){if(r&&l){var d=n._calcNodes(r,l);l.children=d,n._updateFlatNodes(),n.config.changeOnSelect||l.setCheckedState(!1)}},this.findNodeByValue=function(l){var r=null;return!l||!l.length||n.flatNodes.some(function(d){Ce(d.pathValue,l)&&(r=d)}),r},this.searchNodeByLabel=function(l){if(!l)return n.flatNodes;var r=n.config.filterOption,d=q(r)?r:function(o,v){return ne(v.label)&&v.label.indexOf(o)>-1};return n.flatNodes.filter(function(o){var v=o.getPathNodes();return v.some(function(O){return d(l,O._data)})})},this.getOptions=function(){return n.nodes},this.getCheckedNodes=function(){return n.config.showParent?n.getCheckedParentNodes():n.flatNodes.filter(function(l){return l._checked})},this.getCheckedParentNodes=function(){var l=new Set;return n.flatNodes.forEach(function(r){if(r._checked){var d=r.getPathNodes();d.some(function(o){if(o._checked)return l.has(o)||l.add(o),!0})}}),Array.from(l)},this.config=ue({},u);var a=Array.isArray(i)?i:[];this.nodes=this._calcNodes(t,null),this._updateFlatNodes(),this.setNodeCheckedByValue(a)}return e}(),xr=Or,Q="__arco_cascader__",Er="parent",Lr="child",Le={cascader:"cascader",select:"select"};function Pr(e){return!e||_e(e)&&e.length===0}function Ue(e){return{showEmptyChildren:e.showEmptyChildren,changeOnSelect:e.changeOnSelect,lazyload:!!e.loadMore,fieldNames:e.fieldNames,filterOption:e.filterOption,showParent:e.mode==="multiple"&&!e.changeOnSelect&&e.checkedStrategy===Er}}function Tr(e,t){var i=t?Array.isArray(t[0])?t:[t]:[];return new xr(e.options||[],i,Ue(e))}var ge=function(e){var t=e||[],i=t.reduce(function(u,n){return u.add([].concat(n).join(Q)),u},new Set);return i},Z=function(e,t){var i=t||[];return e.has(i.join(Q))},Ar=function(e,t){var i=t||[];return e.delete(i.join(Q))},ae=function(e,t,i){var u=[];if(e===void 0?u=[]:t?u=e:u=[e],i&&i.config.showParent){var n=i.getCheckedNodes(),a=ge(n.map(function(d){return d.pathValue})),l=[],r={};return u.map(function(d){d.some(function(o,v,O){var D=O.slice(0,v+1),I=Z(a,D);return I&&!r[D.join(Q)]&&(l.push(D),r[D.join(Q)]=1),I})}),l}return u},Dr=function(e){var t=function(i){return Array.isArray(i)?i.every(function(u){return u._checked||u.disabled?!0:t(u.children)}):!1};return e._halfChecked&&t(e==null?void 0:e.children)},We=function(e,t,i,u){var n=u&&Dr(i)?!1:u,a=t.getCheckedNodes().reduce(function(v,O){return v.add(O.pathValue.join(Q)),v},new Set);i.setCheckedState(n);var l=t.getCheckedNodes(),r=l.map(function(v){return v.pathValue}),d=ge(r),o=new Set;return e.filter(function(v){if(!Z(a,v)||Z(d,v))return o.add(v.join(Q)),!0}).concat(r.filter(function(v){return!Z(o,v)}))},he=globalThis&&globalThis.__assign||function(){return he=Object.assign||function(e){for(var t,i=1,u=arguments.length;i<u;i++){t=arguments[i];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},he.apply(this,arguments)},Pe=globalThis&&globalThis.__awaiter||function(e,t,i,u){function n(a){return a instanceof i?a:new i(function(l){l(a)})}return new(i||(i=Promise))(function(a,l){function r(v){try{o(u.next(v))}catch(O){l(O)}}function d(v){try{o(u.throw(v))}catch(O){l(O)}}function o(v){v.done?a(v.value):n(v.value).then(r,d)}o((u=u.apply(e,t||[])).next())})},Te=globalThis&&globalThis.__generator||function(e,t){var i={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},u,n,a,l;return l={next:r(0),throw:r(1),return:r(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function r(o){return function(v){return d([o,v])}}function d(o){if(u)throw new TypeError("Generator is already executing.");for(;i;)try{if(u=1,n&&(a=o[0]&2?n.return:o[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,o[1])).done)return a;switch(n=0,a&&(o=[o[0]&2,a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,n=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(a=i.trys,!(a=a.length>0&&a[a.length-1])&&(o[0]===6||o[0]===2)){i=0;continue}if(o[0]===3&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(o[0]===6&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(v){o=[6,v],n=0}finally{u=a=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},Ae=globalThis&&globalThis.__read||function(e,t){var i=typeof Symbol=="function"&&e[Symbol.iterator];if(!i)return e;var u=i.call(e),n,a=[],l;try{for(;(t===void 0||t-- >0)&&!(n=u.next()).done;)a.push(n.value)}catch(r){l={error:r}}finally{try{n&&!n.done&&(i=u.return)&&i.call(u)}finally{if(l)throw l.error}}return a},Rr=function(e){for(var t=0;t<e.length;t++)if(!e[t].disabled)return e[t]},De=function(e){if(e&&e.disabled){for(var t=e;t.parent&&t.parent.disabled;)t=t.parent;return t}return e},Re=function(e,t){return e<0?t:e>t?0:e},Mr=function(e){var t,i=Ae(wr(),2),u=i[0],n=i[1],a=Se(),l=e.store,r=e.prefixCls,d=e.value,o=e.multiple,v=e.renderFooter,O=e.renderOption,D=e.showEmptyChildren,I=e.loadMore,S=e.renderEmpty,j=e.rtl,E=e.icons,k=Ae(N.exports.useState(l.findNodeByValue(d&&d[d.length-1])||null),2),_=k[0],W=k[1],m=l.getOptions(),H=function(s){e.onChange&&e.onChange(s)},$=function(s){return Pe(void 0,void 0,void 0,function(){var p,h;return Te(this,function(C){switch(C.label){case 0:if(!(!s.isLeaf&&q(I)&&!s.children))return[3,5];s.setLoading(!0),a(),C.label=1;case 1:return C.trys.push([1,3,,4]),[4,I(s.pathValue,s.pathValue.length)];case 2:return p=C.sent(),l.appendOptionChildren(s,p),l.setNodeCheckedByValue(e.value),[3,4];case 3:return h=C.sent(),console.error(h),[3,4];case 4:s.setLoading(!1),a(),C.label=5;case 5:return[2]}})})},b=function(s,p){return p===void 0&&(p=!0),Pe(void 0,void 0,void 0,function(){return Te(this,function(h){return!s||s.disabled?[2]:(W(s),$(s),!o&&p&&(e.changeOnSelect||s.isLeaf)&&H([s.pathValue]),[2])})})},w=function(s,p){var h=We(e.value,l,s,p);s===_&&a(),W(s),e.changeOnSelect||$(s),H(h)},T=N.exports.useCallback(function(s){var p;s===void 0&&(s=_);for(var h=s;h;)(p=u[h._level])===null||p===void 0||p.scrollTo({index:h._index,options:{block:"nearest"}}),h=h._level<1?null:h.parent},[_]),x=N.exports.useCallback(function(s){var p=s.keyCode||s.which,h;switch(p){case Be.code:{s.preventDefault(),e.onEsc();break}case de.code:case je.code:{if(!_)h=Rr(m);else for(var C=De(_),F=C.parent&&C.parent.children||m,R=p===de.code?1:-1,f=Re(C._index+R,F.length-1);f!==C._index&&(h=F[f],h.disabled);)f=Re(f+R,F.length-1);return T(h),b(h,!1),s.preventDefault(),!1}case er.code:{if(_&&!_.disabled){var F=_.children||[];h=F[0]||_,b(h,!1)}return s.preventDefault(),!1}case Qe.code:{if(_){var C=De(_);h=C.parent||C}return b(h,!1),s.preventDefault(),!1}case ke.code:return _&&(o?w(_,!_._checked):b(_)),s.preventDefault(),!1}},[_]);oe(function(){W(function(s){var p;if(s&&s.pathValue&&s.pathValue.length){var h=s.pathValue,C={children:m};h.map(function(F){var R=C.children||[],f=R.find(function(z){return z.value===F});f&&(C=f,p=f)})}return p})},[l]),N.exports.useEffect(function(){e.popupVisible&&m.length&&setTimeout(T)},[e.popupVisible]),N.exports.useEffect(function(){var s=e.getTriggerElement();if(!!s)return e.popupVisible?He(s,"keydown",x):me(s,"keydown",x),function(){me(s,"keydown",x)}},[e.popupVisible,x]);var B=function(){var s=[m],p=_?_.getPathNodes():[];return p.forEach(function(h){h&&h.children&&s.push(h.children)}),s}(),A=q(e.dropdownColumnRender)?e.dropdownColumnRender:function(s){return s};return!B.length||!(!((t=B[0])===null||t===void 0)&&t.length)?g(fe,{children:S()}):g(rr,{component:ie.Fragment,children:B.map(function(s,p){var h,C,F,R=v?v(p,_||null):null;return s.length===0&&!D?null:g(nr,{timeout:{enter:300,exit:0},classNames:"cascaderSlide",onEnter:function(f){!f||(f.style.marginLeft="-"+f.scrollWidth+"px")},onEntering:function(f){!f||(f.style.marginLeft="0px")},onEntered:function(f){!f||(f.style.marginLeft="")},children:g("div",{className:Y(r+"-list-column",(h={},h[r+"-list-column-virtual"]=e.virtualListProps&&e.virtualListProps.threshold!==null,h[r+"-list-column-rtl"]=j,h)),style:he({zIndex:B.length-p},e.dropdownMenuColumnStyle),children:A(te("div",{className:Y(r+"-list-wrapper",(C={},C[r+"-list-wrapper-with-footer"]=R!==null,C)),children:[s.length===0?S&&S(e.virtualListProps?"100%":120):g($e,{...he({needFiller:!1,threshold:e.virtualListProps?100:null,data:s,isStaticItemHeight:!0,itemKey:"value"},le(e.virtualListProps)?e.virtualListProps:{},{wrapper:"ul",role:"menu",ref:function(f){return n(f,p)},className:Y(r+"-list",r+"-list-select",(F={},F[r+"-list-multiple"]=o,F[r+"-list-rtl"]=j,F))}),children:function(f){var z,G=!1;return _&&(G=_.pathValue[p]===f.value),g("li",{tabIndex:0,role:"menuitem","aria-haspopup":!f.isLeaf,"aria-expanded":G&&!f.isLeaf,"aria-disabled":f.disabled,title:ne(f.label)?f.label:void 0,className:Y(r+"-list-item",(z={},z[r+"-list-item-active"]=G,z[r+"-list-item-disabled"]=f.disabled,z)),children:g(kr,{prefixCls:r,rtl:j,multiple:o,option:f,selected:!o&&f.isLeaf&&Ce(e.value,f.pathValue),icons:E,onMouseEnter:function(){f.disabled||e.expandTrigger==="hover"&&(W(f),!f.isLeaf&&$(f))},renderOption:O&&function(){return O(f._data,p)},onClickOption:function(){f.isLeaf&&o&&!f.disableCheckbox?w(f,!f._checked):b(f)},onMultipleChecked:function(pe){w(f,pe)},onDoubleClickOption:e.onDoubleClickOption})},f.value)}}),R&&g("div",{className:r+"-list-footer",onMouseDown:function(f){f.stopPropagation()},children:R})]}),p)})},p)})})},Fr=Mr,ye=globalThis&&globalThis.__assign||function(){return ye=Object.assign||function(e){for(var t,i=1,u=arguments.length;i<u;i++){t=arguments[i];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},ye.apply(this,arguments)},Me=globalThis&&globalThis.__read||function(e,t){var i=typeof Symbol=="function"&&e[Symbol.iterator];if(!i)return e;var u=i.call(e),n,a=[],l;try{for(;(t===void 0||t-- >0)&&!(n=u.next()).done;)a.push(n.value)}catch(r){l={error:r}}finally{try{n&&!n.done&&(i=u.return)&&i.call(u)}finally{if(l)throw l.error}}return a},Fe=function(e,t){return e<0?t:e>t?0:e},Ir=function(e,t,i){var u=t;if(ne(t)){var n=t.toUpperCase().indexOf(e.toUpperCase());if(n>-1){var a=t.substr(0,n),l=t.substr(n+e.length);u=te(fe,{children:[a,g("span",{className:i+"-highlight",children:t.substr(n,e.length)}),l]})}}return u},jr=function(e){var t,i=e.store,u=e.prefixCls,n=e.multiple,a=e.onChange,l=e.inputValue,r=e.renderEmpty,d=e.style,o=e.defaultActiveFirstOption,v=e.rtl,O=e.icons,D=e.value||[],I=Me(N.exports.useState(i.searchNodeByLabel(l)||[]),2),S=I[0],j=I[1],E=N.exports.useRef(),k=N.exports.useRef(),_=tr(),W=Me(N.exports.useState(o?0:-1),2),m=W[0],H=W[1],$=function(b,w,T){if(T.stopPropagation(),!b.disabled)if(n){var x=We(e.value,i,b,w);a&&a(x)}else a&&a([b.pathValue])};return oe(function(){j(i.searchNodeByLabel(l))},[l,i]),oe(function(){H(function(b){return b>S.length-1?o?0:-1:b})},[S]),N.exports.useEffect(function(){var b=e.getTriggerElement();if(!!b){var w=function(T){T.stopPropagation();var x=T.keyCode||T.which;switch(x){case Be.code:return e.onEsc(),!1;case de.code:case je.code:{k.current=!0;for(var B=de.code===x?1:-1,A=Fe(m+B,S.length-1);A!==m;){var s=S[A];if(s.disabled)A=Fe(A+B,S.length-1);else break}return H(A),!1}case ke.code:var p=S[m];if(p){var h=D.some(function(C){return Ce(C,p.pathValue)});$(p,!h,T)}return!1}};return He(b,"keydown",w),function(){me(b,"keydown",w)}}},[S,m,D]),N.exports.useEffect(function(){var b,w=E.current;w&&(k.current||_)&&ar(w,{behavior:"auto",block:"nearest",scrollMode:"if-needed",boundary:(b=w.parentNode)===null||b===void 0?void 0:b.parentNode})},[m,S]),E.current=null,S.length?g("div",{className:u+"-list-wrapper",children:g($e,{...ye({needFiller:!1,wrapper:"ul",role:"menu",style:d,data:S,isStaticItemHeight:!0,threshold:e.virtualListProps?100:null},le(e.virtualListProps)?e.virtualListProps:{},{onMouseMove:function(){k.current=!1},className:Y(u+"-list",u+"-list-search",(t={},t[u+"-list-multiple"]=n,t[u+"-list-rtl"]=v,t))}),children:function(b,w){var T,x=b.getPathNodes(),B=x.map(function(h,C){return te("span",{children:[h.label,x.length===C+1?"":" / "]},h.label+"_"+h._level)}),A=b._checked,s={checked:A},p=q(e.renderOption)?e.renderOption(l,b._data,s):Ir(l,B,u);return g("li",{title:ne(p)?p:ne(B)?B:void 0,role:"menuitem","aria-disabled":b.disabled,ref:function(h){w===m&&(E.current=h),A&&!E.current&&(E.current=h)},className:Y(u+"-list-search-item",(T={},T[u+"-list-search-item-active"]=A,T[u+"-list-search-item-hover"]=w===m,T[u+"-list-search-item-disabled"]=b.disabled,T)),onClick:function(h){$(b,!A,h)},onMouseEnter:function(){!k.current&&!b.disabled&&H(w)},onMouseLeave:function(){!k.current&&!b.disabled&&H(o?0:-1)},children:g("div",{className:u+"-list-item-label",children:q(e.renderOption)?p:n?g(Ie,{checked:A,disabled:b.disabled,children:p}):te(fe,{children:[p,A&&g("span",{className:u+"-check-icon",children:O.checked})]})})},w)}})}):g(fe,{children:r&&r()})},Br=jr,Hr=globalThis&&globalThis.__read||function(e,t){var i=typeof Symbol=="function"&&e[Symbol.iterator];if(!i)return e;var u=i.call(e),n,a=[],l;try{for(;(t===void 0||t-- >0)&&!(n=u.next()).done;)a.push(n.value)}catch(r){l={error:r}}finally{try{n&&!n.done&&(i=u.return)&&i.call(u)}finally{if(l)throw l.error}}return a},$r=globalThis&&globalThis.__spreadArray||function(e,t,i){if(i||arguments.length===2)for(var u=0,n=t.length,a;u<n;u++)(a||!(u in t))&&(a||(a=Array.prototype.slice.call(t,0,u)),a[u]=t[u]);return e.concat(a||Array.prototype.slice.call(t))};function Ur(e,t){var i=N.exports.useRef(null),u=Se();return i.current||(i.current=e()),oe(function(){i.current=e(),u()},$r([],Hr(t),!1)),i.current}var ve=globalThis&&globalThis.__assign||function(){return ve=Object.assign||function(e){for(var t,i=1,u=arguments.length;i<u;i++){t=arguments[i];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},ve.apply(this,arguments)},be=globalThis&&globalThis.__read||function(e,t){var i=typeof Symbol=="function"&&e[Symbol.iterator];if(!i)return e;var u=i.call(e),n,a=[],l;try{for(;(t===void 0||t-- >0)&&!(n=u.next()).done;)a.push(n.value)}catch(r){l={error:r}}finally{try{n&&!n.done&&(i=u.return)&&i.call(u)}finally{if(l)throw l.error}}return a},ze={label:"label",value:"value",isLeaf:"isLeaf",children:"children",disabled:"disabled"},Wr={options:[],bordered:!0,fieldNames:ze,trigger:"click",expandTrigger:"click",checkedStrategy:Lr,defaultActiveFirstOption:!0},zr={bottom:4};function Jr(e,t){var i=N.exports.useContext(ir),u=i.getPrefixCls,n=i.renderEmpty,a=i.componentConfig,l=i.rtl,r=lr(e,Wr,a==null?void 0:a.Cascader),d=r.disabled,o=r.renderFormat,v=r.getPopupContainer,O=r.children,D=r.triggerProps,I=r.expandTrigger,S=r.icons,j={loading:(S==null?void 0:S.loading)||g(ur,{}),checked:(S==null?void 0:S.checked)||g(or,{}),next:(S==null?void 0:S.next)||(l?g(sr,{}):g(cr,{}))},E=u("cascader"),k=r.mode==="multiple",_=N.exports.useRef(null),W=Se(),m=Ur(function(){return Tr(r,ae("value"in r?r.value:r.defaultValue,k))},[JSON.stringify(Ue(r)),r.options]),H=be(N.exports.useState(function(){return"value"in r?ae(r.value,k,m):"defaultValue"in r?ae(r.defaultValue,k,m):[]}),2),$=H[0],b=H[1],w="value"in r?ae(r.value,k,m):$,T=be(Oe(!1,{value:r.popupVisible,defaultValue:r.defaultPopupVisible}),2),x=T[0],B=T[1],A=be(Oe("",{value:"inputValue"in r?r.inputValue||"":void 0}),3),s=A[0],p=A[1],h=A[2],C=N.exports.useRef(s),F=N.exports.useRef(null),R=N.exports.useRef(null),f=N.exports.useRef((m==null?void 0:m.getCheckedNodes())||[]),z=dr(E+"-popup-"),G=function(c,V){c!==C.current&&(p(c),C.current=c,F.current=V,r.onInputValueChange&&r.onInputValueChange(c,V))};N.exports.useEffect(function(){var c=F.current;h===s&&(c==="manual"||c==="optionListHide")&&r.onSearch&&r.onSearch(s,c),s!==C.current&&(C.current=s)},[s]),N.exports.useEffect(function(){var c=function(){clearTimeout(_.current),_.current=null};return!x&&s&&(_.current&&c(),_.current=setTimeout(function(){G("","optionListHide"),_.current=null},200)),function(){c()}},[x]),oe(function(){if("value"in r&&r.value!==$){var c=ae(r.value,k);m.setNodeCheckedByValue(c),b(c)}},[r.value,k]),N.exports.useImperativeHandle(t,function(){return R.current},[]);var pe=function(c){f.current=Array.from(new Set([].concat(c,f.current)))},we=function(c){var V=[],L=ge(c),P=function(M){M.some(function(y){if(Z(L,y.pathValue)&&(V.push(y.getPathNodes().map(function(re){return re._data})),Ar(L,y.pathValue)),!L.size)return!0})};return P(m.getCheckedNodes()),L.size&&P(f.current),V},J=N.exports.useCallback(function(c){c!==x&&(r.onVisibleChange&&r.onVisibleChange(c),"popupVisible"in r||B(c))},[r.onVisibleChange,x]),Ke=N.exports.useCallback(function(c){var V,L=we([c])[0]||[],P,M=_e(c)?c.map(function(y){return String(y)}):[];return L.length&&(M=L.map(function(y){return y.label})),q(o)?P=o(M):M.every(function(y){return ne(y)})?P=M.join(" / "):P=M.reduce(function(y,re,X){return y.concat(X===0?[re]:[" / ",re])},[]),{text:P||"",disabled:(V=L[L.length-1])===null||V===void 0?void 0:V.disabled}},[m,$,o]),ee=function(c,V){var L;V==="panel"&&le(r.showSearch)&&!r.showSearch.retainInputValueWhileSelect&&k&&G("","optionChecked");var P=r.onChange,M=r.changeOnSelect,y=r.expandTrigger,re=w===c;if(!re){k||m.setNodeCheckedByValue(c),pe(m.getCheckedNodes());var X=we(c),Ye=k?c:c[0],Ze=k?X:X[0];k||(s||X[0]&&((L=X[0][X[0].length-1])===null||L===void 0?void 0:L.isLeaf)||M&&y==="hover")&&J(!1),"value"in r?(m.setNodeCheckedByValue(w),W()):b(c),P&&P(Ye,Ze,{dropdownVisible:x})}},qe=function(c,V,L){if(L.stopPropagation(),!c.disabled){var P=w.filter(function(M,y){return y!==V});m.setNodeCheckedByValue(P),ee(P)}},Ne=function(c){var V=c||R.current&&R.current.getWidth();return g("div",{className:E+"-list-empty",style:{width:V},children:r.notFoundContent||n("Cascader")})},Ge=function(){var c,V=le(r.showSearch)?r.showSearch.panelMode:void 0,L=V===Le.select?!0:V===Le.cascader?!1:!q(r.onSearch)&&!!s,P=R.current&&R.current.getWidth(),M=q(r.dropdownRender)?r.dropdownRender:function(y){return y};return g("div",{id:z,className:Y(E+"-popup",r.dropdownMenuClassName,(c={},c[E+"-popup-trigger-hover"]=r.expandTrigger==="hover",c)),children:M(g("div",{className:E+"-popup-inner",onMouseDown:function(y){return y.preventDefault()},children:L?g(Br,{style:{minWidth:P},store:m,inputValue:s,renderEmpty:function(){return Ne(P)},multiple:k,onChange:function(y){ee(y,"panel")},prefixCls:E,rtl:l,onEsc:function(){J(!1)},renderOption:le(r.showSearch)&&r.showSearch.renderOption||void 0,getTriggerElement:function(){var y;return(y=R.current)===null||y===void 0?void 0:y.dom},value:w,virtualListProps:r.virtualListProps,defaultActiveFirstOption:r.defaultActiveFirstOption,icons:j}):g(Fr,{dropdownMenuColumnStyle:r.dropdownMenuColumnStyle,virtualListProps:r.virtualListProps,expandTrigger:I,store:m,dropdownColumnRender:r.dropdownColumnRender,renderOption:r.renderOption,changeOnSelect:r.changeOnSelect,showEmptyChildren:r.showEmptyChildren||!!r.loadMore,multiple:k,onChange:function(y){ee(y,"panel")},loadMore:r.loadMore,prefixCls:E,rtl:l,getTriggerElement:function(){var y;return(y=R.current)===null||y===void 0?void 0:y.dom},renderEmpty:Ne,popupVisible:x,value:w,renderFooter:r.renderFooter,icons:j,onEsc:function(){J(!1)},onDoubleClickOption:function(){r.changeOnSelect&&!k&&J(!1)}})}))})},Xe=function(c){ee(c)},Ve=function(c){return g(vr,{...ve({popup:Ge,trigger:r.trigger,disabled:d,getPopupContainer:v,position:l?"br":"bl",classNames:"slideDynamicOrigin",popupAlign:zr,unmountOnExit:"unmountOnExit"in r?r.unmountOnExit:!q(r.loadMore),popupVisible:x},D,{onVisibleChange:J}),children:c})};return O?Ve(O):g(fr,{...ve({},r,{ref:R,ariaControls:z,popupVisible:x,value:k?w:w&&w[0],inputValue:s,rtl:l,isEmptyValue:Pr(w),prefixCls:E,isMultiple:k,renderText:Ke,onRemoveCheckedItem:qe,onSort:Xe,renderView:Ve,onClear:function(c){var V;if(c.stopPropagation(),!k)ee([]);else{var L=m.getCheckedNodes(),P=L.filter(function(M){return M.disabled}).map(function(M){return M.pathValue});m.setNodeCheckedByValue(P),ee(P)}(V=r.onClear)===null||V===void 0||V.call(r,!!x)},onKeyDown:function(c){var V;if(!d){c.stopPropagation();var L=c.keyCode||c.which;L===ke.code&&!x&&(J(!0),c.preventDefault()),L===hr.code&&x&&J(!1),(V=r.onKeyDown)===null||V===void 0||V.call(r,c)}},onChangeInputValue:function(c){G(c,"manual"),x||J(!0)}})})}var Je=N.exports.forwardRef(Jr);Je.displayName="Cascader";var Kr=Je;function Yr({loading:e}){const t=gr(_r),[i]=K.useForm(),{lang:u}=N.exports.useContext(pr),n=async()=>{try{await i.validate(),yr.success("userSetting.saveSuccess")}catch{}},a=()=>{i.resetFields()},l=(r=1)=>g(Cr,{text:{rows:r,width:new Array(r).fill("100%")},animation:!0});return te(K,{style:{width:"500px",marginTop:"6px"},form:i,labelCol:{span:u==="en-US"?7:6},wrapperCol:{span:u==="en-US"?17:18},children:[g(K.Item,{label:t["userSetting.info.email"],field:"email",rules:[{type:"email",required:!0,message:t["userSetting.info.email.placeholder"]}],children:e?l():g(se,{placeholder:t["userSetting.info.email.placeholder"]})}),g(K.Item,{label:t["userSetting.info.nickName"],field:"nickName",rules:[{required:!0,message:t["userSetting.info.nickName.placeholder"]}],children:e?l():g(se,{placeholder:t["userSetting.info.nickName.placeholder"]})}),g(K.Item,{label:t["userSetting.info.area"],field:"rangeArea",rules:[{required:!0,message:t["userSetting.info.area.placeholder"]}],children:e?l():g(br,{options:["\u4E2D\u56FD"],placeholder:t["userSetting.info.area.placeholder"]})}),g(K.Item,{label:t["userSetting.info.location"],field:"location",initialValue:["BeiJing","BeiJing","HaiDian"],rules:[{required:!0}],children:e?l():g(Kr,{options:[{label:"\u5317\u4EAC\u5E02",value:"BeiJing",children:[{label:"\u5317\u4EAC\u5E02",value:"BeiJing",children:[{label:"\u6D77\u6DC0\u533A",value:"HaiDian"},{label:"\u671D\u9633\u533A",value:"ChaoYang"}]}]},{label:"\u4E0A\u6D77\u5E02",value:"ShangHai",children:[{label:"\u4E0A\u6D77\u5E02",value:"ShangHai",children:[{label:"\u9EC4\u6D66\u533A",value:"HuangPu"},{label:"\u9759\u5B89\u533A",value:"JingAn"}]}]}]})}),g(K.Item,{label:t["userSetting.info.address"],field:"address",children:e?l():g(se,{placeholder:t["userSetting.info.address.placeholder"]})}),g(K.Item,{label:t["userSetting.info.profile"],field:"profile",children:e?l(3):g(se.TextArea,{placeholder:t["userSetting.info.profile.placeholder"]})}),g(K.Item,{label:" ",children:te(mr,{children:[g(xe,{type:"primary",onClick:n,children:t["userSetting.save"]}),g(xe,{onClick:a,children:t["userSetting.reset"]})]})})]})}export{Yr as default};
