import{u as v,r as l,j as c,a as t,a4 as i,k as C,c as T,l as q,a5 as j}from"./index.7dafa16d.js";import{C as P}from"./index.519f9d90.js";import{l as S,C as w}from"./card-block.f9413417.js";import{s as d}from"./index.module.27984758.js";import A from"./card-add.c6c32f52.js";import"./index.43d26da3.js";import"./index.69569894.js";import"./index.1446e701.js";import"./index.305c74fb.js";const{Title:p}=T,{Row:N,Col:u}=j,n=new Array(10).fill({});function k(){const a=v(S),[h,x]=l.exports.useState(!0),[o,b]=l.exports.useState({quality:n,service:n,rules:n}),[s,f]=l.exports.useState("all"),g=()=>{q.get("/api/cardList").then(e=>{b(e.data)}).finally(()=>x(!1))};l.exports.useEffect(()=>{g()},[]);const m=(e,r)=>c(N,{gutter:24,className:d["card-content"],children:[r==="quality"&&t(u,{xs:24,sm:12,md:8,lg:6,xl:6,xxl:6,children:t(A,{description:a["cardList.add.quality"]})}),e.map((L,y)=>t(u,{xs:24,sm:12,md:8,lg:6,xl:6,xxl:6,children:t(w,{card:L,type:r,loading:h})},y))]});return c(P,{children:[t(p,{heading:6,children:a["menu.list.card"]}),c(i,{activeTab:s,type:"rounded",onChange:f,extra:t(C.Search,{style:{width:"240px"},placeholder:a[`cardList.tab.${s}.placeholder`]}),children:[t(i.TabPane,{title:a["cardList.tab.title.all"]},"all"),t(i.TabPane,{title:a["cardList.tab.title.quality"]},"quality"),t(i.TabPane,{title:a["cardList.tab.title.service"]},"service"),t(i.TabPane,{title:a["cardList.tab.title.rules"]},"rules")]}),t("div",{className:d.container,children:s==="all"?Object.entries(o).map(([e,r])=>c("div",{children:[t(p,{heading:6,children:a[`cardList.tab.title.${e}`]}),m(r,e)]},e)):t("div",{className:d["single-content"],children:m(o[s],s)})})]})}export{k as default};
