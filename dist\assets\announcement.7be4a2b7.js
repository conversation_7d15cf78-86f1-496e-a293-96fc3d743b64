import{r as n,u as d,j as r,a as t,c as m,L as h,d as f,l as y}from"./index.7dafa16d.js";import{S as k}from"./index.43d26da3.js";import{C as g}from"./index.519f9d90.js";import{i as x}from"./index.9464998a.js";const w="_item_1h3pz_1",_="_link_1h3pz_8";var i={item:w,link:_};function S(){const[o,c]=n.exports.useState([]),[l,s]=n.exports.useState(!0),a=d(x),p=()=>{s(!0),y.get("/api/workplace/announcement").then(e=>{c(e.data)}).finally(()=>{s(!1)})};n.exports.useEffect(()=>{p()},[]);function u(e){switch(e){case"activity":return"orangered";case"info":return"cyan";case"notice":return"arcoblue";default:return"arcoblue"}}return r(g,{children:[r("div",{style:{display:"flex",justifyContent:"space-between"},children:[t(m.Title,{heading:6,children:a["workplace.announcement"]}),t(h,{children:a["workplace.seeMore"]})]}),t(k,{loading:l,text:{rows:5,width:"100%"},animation:!0,children:t("div",{children:o.map(e=>r("div",{className:i.item,children:[t(f,{color:u(e.type),size:"small",children:a[`workplace.${e.type}`]}),t("span",{className:i.link,children:e.content})]},e.key))})})]})}export{S as default};
