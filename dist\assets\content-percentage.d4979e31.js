import{u as f,r as e,j as g,a as o,c as m,S as h,l as x}from"./index.7dafa16d.js";import{C as y}from"./index.519f9d90.js";import{D as C}from"./index.1a52f4db.js";import{i as F}from"./index.9464998a.js";function j(){const a=f(F),[s,n]=e.exports.useState([]),[i,r]=e.exports.useState(!0),l=()=>{r(!0),x.get("/api/workplace/content-percentage").then(t=>{n(t.data)}).finally(()=>{r(!1)})};return e.exports.useEffect(()=>{l()},[]),g(y,{children:[o(m.Title,{heading:6,children:a["workplace.contentPercentage"]}),o(h,{loading:i,style:{display:"block"},children:o(C,{autoFit:!0,height:340,data:s,radius:.7,innerRadius:.65,angleField:"count",colorField:"type",color:["#21CCFF","#313CA9","#249EFF"],interactions:[{type:"element-single-selected"}],tooltip:{showMarkers:!1},label:{visible:!0,type:"spider",formatter:t=>`${(t.percent*100).toFixed(0)}%`,style:{fill:"#86909C",fontSize:14}},legend:{position:"bottom"},statistic:{title:{style:{fontSize:"14px",lineHeight:2,color:"rgb(--var(color-text-1))"},formatter:()=>"\u5185\u5BB9\u91CF"},content:{style:{fontSize:"16px",color:"rgb(--var(color-text-1))"},formatter:(t,c)=>{const p=c.reduce((u,d)=>u+d.count,0);return Number(p).toLocaleString()}}}})})]})}export{j as default};
