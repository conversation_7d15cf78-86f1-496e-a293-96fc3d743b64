import{R as v,r as d,g,a as i,_ as y,j as p,u as H,n as x,F as C,aE as U,aF as w,a6 as V,B as f,at as Z,d as L,aG as j,aH as W,aI as K,c as G}from"./index.7dafa16d.js";import{S}from"./index.43d26da3.js";import{D as Q}from"./index.69569894.js";import{C as J}from"./index.519f9d90.js";import{P as I}from"./index.1446e701.js";import{s as c}from"./index.module.27984758.js";import{I as X}from"./index.305c74fb.js";function N(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);r&&(a=a.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),e.push.apply(e,a)}return e}function P(t){for(var r=1;r<arguments.length;r++){var e=arguments[r]!=null?arguments[r]:{};r%2?N(Object(e),!0).forEach(function(a){y(t,a,e[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):N(Object(e)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(e,a))})}return t}function Y(t,r){var e=d.exports.useContext(g),a=e.prefixCls,s=a===void 0?"arco":a,l=t.spin,o=t.className,n=P(P({"aria-hidden":!0,focusable:!1,ref:r},t),{},{className:"".concat(o?o+" ":"").concat(s,"-icon ").concat(s,"-icon-star-fill")});return l&&(n.className="".concat(n.className," ").concat(s,"-icon-loading")),delete n.spin,delete n.isIcon,i("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...n,children:i("path",{fill:"currentColor",stroke:"none",d:"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z"})})}var O=v.forwardRef(Y);O.defaultProps={isIcon:!0};O.displayName="IconStarFill";var ee=O;function k(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);r&&(a=a.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),e.push.apply(e,a)}return e}function E(t){for(var r=1;r<arguments.length;r++){var e=arguments[r]!=null?arguments[r]:{};r%2?k(Object(e),!0).forEach(function(a){y(t,a,e[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):k(Object(e)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(e,a))})}return t}function te(t,r){var e=d.exports.useContext(g),a=e.prefixCls,s=a===void 0?"arco":a,l=t.spin,o=t.className,n=E(E({"aria-hidden":!0,focusable:!1,ref:r},t),{},{className:"".concat(o?o+" ":"").concat(s,"-icon ").concat(s,"-icon-thumb-up-fill")});return l&&(n.className="".concat(n.className," ").concat(s,"-icon-loading")),delete n.spin,delete n.isIcon,p("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...n,children:[i("path",{fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M5 43V17h4v26H5Z",clipRule:"evenodd"}),i("path",{fill:"currentColor",stroke:"none",d:"M27.1 4.463a2 2 0 0 0-2.83.364L15.036 17H12v26h23.576a2 2 0 0 0 1.89-1.346l5.697-19.346c.899-2.598-1.03-5.308-3.78-5.308h-10.57l2.422-5.448a4 4 0 0 0-1.184-4.77L27.1 4.462Z"})]})}var F=v.forwardRef(te);F.defaultProps={isIcon:!0};F.displayName="IconThumbUpFill";var ae=F;function q(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);r&&(a=a.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),e.push.apply(e,a)}return e}function A(t){for(var r=1;r<arguments.length;r++){var e=arguments[r]!=null?arguments[r]:{};r%2?q(Object(e),!0).forEach(function(a){y(t,a,e[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):q(Object(e)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(e,a))})}return t}function re(t,r){var e=d.exports.useContext(g),a=e.prefixCls,s=a===void 0?"arco":a,l=t.spin,o=t.className,n=A(A({"aria-hidden":!0,focusable:!1,ref:r},t),{},{className:"".concat(o?o+" ":"").concat(s,"-icon ").concat(s,"-icon-pen-fill")});return l&&(n.className="".concat(n.className," ").concat(s,"-icon-loading")),delete n.spin,delete n.isIcon,p("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...n,children:[i("path",{fill:"currentColor",stroke:"none",d:"M31.07 8.444H43.07V37.444H31.07z",transform:"rotate(45 31.07 8.444)"}),i("path",{fill:"currentColor",stroke:"none",d:"M33.9 5.615a2 2 0 0 1 2.829 0l5.657 5.657a2 2 0 0 1 0 2.829l-1.415 1.414-8.485-8.486L33.9 5.615ZM17.636 38.85 9.15 30.363l-3.61 10.83a1 1 0 0 0 1.265 1.265l10.83-3.61Z"})]})}var D=v.forwardRef(re);D.defaultProps={isIcon:!0};D.displayName="IconPenFill";var ie=D;const ne={"en-US":{"menu.list":"List","menu.list.card":"Card List","cardList.tab.title.all":"All","cardList.tab.title.quality":"Content quality","cardList.tab.title.service":"Service opening","cardList.tab.title.rules":"Rule presets","cardList.tab.all.placeholder":"Search","cardList.tab.quality.placeholder":"Search queue","cardList.tab.service.placeholder":"Search service","cardList.tab.rules.placeholder":"Search rule","cardList.searchInput.placeholder":"Search service","cardList.add.quality":"Create quality inspection queue","cardList.enable":"Enable","cardList.disable":"Disable","cardList.action":"action","cardList.detail":"Detail","cardList.tab.title.announcement":"Recent Announcement","cardList.announcement.noData":"No announcement","cardList.statistic.enable":"Enable","cardList.statistic.disable":"Disable","cardList.statistic.applicationNum":"Applications","cardList.options.qualityInspection":"Quality inspection","cardList.options.remove":"Remove","cardList.options.cancel":"Cancel","cardList.options.subscribe":"Subscribe","cardList.options.renewal":"Renewal","cardList.tag.activated":"Activated","cardList.tag.opened":"Already Opened","cardList.tag.expired":"Expired"},"zh-CN":{"menu.list":"\u5217\u8868\u9875","menu.list.card":"\u5361\u7247\u5217\u8868","cardList.tab.title.all":"\u5168\u90E8","cardList.tab.title.quality":"\u5185\u5BB9\u8D28\u68C0","cardList.tab.title.service":"\u670D\u52A1\u5F00\u901A","cardList.tab.title.rules":"\u89C4\u5219\u9884\u7F6E","cardList.tab.all.placeholder":"\u641C\u7D22","cardList.tab.quality.placeholder":"\u641C\u7D22\u961F\u5217","cardList.tab.service.placeholder":"\u641C\u7D22\u670D\u52A1","cardList.tab.rules.placeholder":"\u641C\u7D22\u89C4\u5219","cardList.searchInput.placeholder":"\u641C\u7D22\u670D\u52A1","cardList.add.quality":"\u70B9\u51FB\u521B\u5EFA\u8D28\u68C0\u5185\u5BB9\u961F\u5217","cardList.enable":"\u542F\u7528","cardList.disable":"\u7981\u7528","cardList.action":"\u64CD\u4F5C","cardList.detail":"\u8BE6\u7EC6\u4FE1\u606F","cardList.tab.title.announcement":"\u6700\u8FD1\u516C\u544A","cardList.announcement.noData":"\u6682\u65E0\u516C\u544A","cardList.statistic.enable":"\u5DF2\u542F\u7528","cardList.statistic.disable":"\u672A\u542F\u7528","cardList.statistic.applicationNum":"\u5E94\u7528\u6570","cardList.options.qualityInspection":"\u8D28\u68C0","cardList.options.remove":"\u5220\u9664","cardList.options.cancel":"\u53D6\u6D88\u5F00\u901A","cardList.options.subscribe":"\u5F00\u901A\u670D\u52A1","cardList.options.renewal":"\u7EED\u7EA6\u670D\u52A1","cardList.tag.activated":"\u5DF2\u542F\u7528","cardList.tag.opened":"\u5DF2\u5F00\u901A","cardList.tag.expired":"\u5DF2\u8FC7\u671F"}};var se=ne;const B=[ee,ae,K,X,ie].map((t,r)=>i(t,{},r)),{Paragraph:ce}=G;function le(t){const{type:r,card:e={}}=t,[a,s]=d.exports.useState(!1),[l,o]=d.exports.useState(e.status),[n,m]=d.exports.useState(t.loading),u=H(se),b=async()=>{m(!0),await new Promise(h=>setTimeout(()=>{o(l!==1?1:0),h(null)},1e3)).finally(()=>m(!1))};d.exports.useEffect(()=>{m(t.loading)},[t.loading]),d.exports.useEffect(()=>{e.status!==l&&o(e.status)},[e.status]);const _=()=>r==="service"&&typeof e.icon=="number"?i("div",{className:c.icon,children:B[e.icon%B.length]}):null,$=()=>r==="quality"?p(C,{children:[i(I,{requiredPermissions:[{resource:/^menu.list.*/,actions:["read"]}],children:i(f,{type:"primary",style:{marginLeft:"12px"},loading:n,children:u["cardList.options.qualityInspection"]})}),i(I,{requiredPermissions:[{resource:/^menu.list.*/,actions:["write"]}],children:i(f,{loading:n,children:u["cardList.options.remove"]})})]}):r==="service"?i(C,{children:l===1?i(f,{loading:n,onClick:b,children:u["cardList.options.cancel"]}):i(f,{type:"outline",loading:n,onClick:b,children:l===0?u["cardList.options.subscribe"]:u["cardList.options.renewal"]})}):i(Z,{checked:!!l,loading:n,onChange:b}),R=()=>{if(r==="rules"&&l)return i(L,{color:"green",icon:i(j,{}),className:c.status,size:"small",children:u["cardList.tag.activated"]});switch(l){case 1:return i(L,{color:"green",icon:i(j,{}),className:c.status,size:"small",children:u["cardList.tag.opened"]});case 2:return i(L,{color:"red",icon:i(W,{}),className:c.status,size:"small",children:u["cardList.tag.expired"]});default:return null}},M=()=>n?i(S,{text:{rows:r!=="quality"?3:2},animation:!0,className:c["card-block-skeleton"]}):r!=="quality"?i(ce,{children:e.description}):i(Q,{column:2,data:[{label:"\u5F85\u8D28\u68C0\u6570",value:e.qualityCount},{label:"\u79EF\u538B\u65F6\u957F",value:`${e.duration}s`},{label:"\u5F85\u62BD\u68C0\u6570",value:e.randomCount}]}),T=x(c["card-block"],c[`${r}-card`]);return p(J,{bordered:!0,className:T,size:"small",title:n?i(S,{animation:!0,text:{rows:1,width:["100%"]},style:{width:"120px",height:"24px"},className:c["card-block-skeleton"]}):p(C,{children:[p("div",{className:x(c.title,{[c["title-more"]]:a}),children:[_(),e.title,R(),i(U,{droplist:i(w,{children:["\u64CD\u4F5C1","\u64CD\u4F5C2"].map((h,z)=>i(w.Item,{children:h},z.toString()))}),trigger:"click",onVisibleChange:s,popupVisible:a,children:i("div",{className:c.more,children:i(V,{})})})]}),i("div",{className:c.time,children:e.time})]}),children:[i("div",{className:c.content,children:M()}),i("div",{className:c.extra,children:$()})]})}var he=Object.freeze(Object.defineProperty({__proto__:null,default:le},Symbol.toStringTag,{value:"Module"}));export{le as C,he as c,se as l};
