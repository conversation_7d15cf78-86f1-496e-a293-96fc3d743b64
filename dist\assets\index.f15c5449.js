import{r as R,e as $,j as A,a as c,aD as M,au as W,C as B,f as G,q as H,R as T}from"./index.7dafa16d.js";function J(e,o){var i,d=e.style,m=e.className,a=e.prefixCls,y=e.index,t=y===void 0?1:y,x=e.current,f=x===void 0?1:x,N=e.status,h=e.title,u=e.description,C=e.icon,b=e.nextStepError,l=e.type,S=e.customDot,P=e.labelPlacement,_=e.disabled,z=e.onClick,g=e.onChange,r=e.direction,v=e.id,p=e.lineless;function E(k){if(l==="dot")return null;var w=t;return C?w=C:k==="finish"?w=c(M,{}):k==="error"&&(w=c(W,{})),c("div",{className:a+"-icon",children:w})}function s(k){_||(g&&f!==t&&g(t,v),z&&z(t,v,k))}var n;N?n=N:(f<t&&(n="wait"),f===t&&(n="process"),f>t&&(n="finish"));var D=$(a+"-item",a+"-item-"+n,(i={},i[a+"-item-custom"]=!!C,i[a+"-item-next-error"]=b,i[a+"-item-disabled"]=_,i[a+"-item-active"]=t===f,i),m),F=E(n),O=c("div",{className:a+"-item-icon",children:F}),L=S?S(O,{index:t,status:n,title:h,description:u}):O;return A("div",{ref:o,className:D,style:d,onClick:s,children:[!p&&(P==="vertical"||r==="vertical")&&c("div",{className:a+"-item-tail"}),l!=="arrow"&&L,A("div",{className:a+"-item-content",children:[c("div",{className:a+"-item-title",children:h}),u&&c("div",{className:a+"-item-description",children:u})]})]})}var q=R.exports.forwardRef(J);q.displayName="Step";var K=q,j=globalThis&&globalThis.__assign||function(){return j=Object.assign||function(e){for(var o,i=1,d=arguments.length;i<d;i++){o=arguments[i];for(var m in o)Object.prototype.hasOwnProperty.call(o,m)&&(e[m]=o[m])}return e},j.apply(this,arguments)},Q={current:1,type:"default",size:"default",direction:"horizontal",labelPlacement:"horizontal"};function U(e,o){var i,d=R.exports.useContext(B),m=d.getPrefixCls,a=d.componentConfig,y=d.rtl,t=G(e,Q,a==null?void 0:a.Steps),x=t.className,f=t.style,N=t.children,h=t.current,u=h===void 0?1:h,C=t.status,b=t.onChange,l=t.type,S=t.size,P=t.direction,_=t.labelPlacement,z=t.customDot,g=t.lineless,r=m("steps"),v=_;l==="dot"&&(v=P==="vertical"?"horizontal":"vertical"),l==="navigation"&&(v="horizontal");var p=P;(l==="navigation"||l==="arrow")&&(p="horizontal");var E=$(r,r+"-"+p,r+"-label-"+v,r+"-size-"+S,(i={},i[r+"-change-onclick"]=typeof b=="function",i[r+"-mode-"+l]=l!=="default",i[r+"-lineless"]=g,i[r+"-rtl"]=y,i),x);return c("div",{...j({ref:o,style:f,className:E},H(t)),children:T.Children.toArray(N).filter(function(s){return s&&s.type&&s.type.displayName==="Step"}).map(function(s,n){if(n+=1,s){var D=j({prefixCls:r,type:l,index:n,current:u,status:u===n?C:void 0,customDot:z,labelPlacement:v,direction:p,onChange:b,lineless:g},s.props);return C==="error"&&u===n+1&&(D.nextStepError=!0),T.cloneElement(s,D)}return null})})}var V=R.exports.forwardRef(U),I=V;I.displayName="Steps";I.Step=K;var Y=I;export{Y as S};
