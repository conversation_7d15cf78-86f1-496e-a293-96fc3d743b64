import{r as x,C as U,f as W,a9 as L,aa as O,t as X,O as Y,s as F,e as Z,j as h,a as p,F as A}from"./index.7dafa16d.js";var c=globalThis&&globalThis.__assign||function(){return c=Object.assign||function(u){for(var n,t=1,f=arguments.length;t<f;t++){n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(u[i]=n[i])}return u},c.apply(this,arguments)},$=globalThis&&globalThis.__read||function(u,n){var t=typeof Symbol=="function"&&u[Symbol.iterator];if(!t)return u;var f=t.call(u),i,S=[],y;try{for(;(n===void 0||n-- >0)&&!(i=f.next()).done;)S.push(i.value)}catch(s){y={error:s}}finally{try{i&&!i.done&&(t=f.return)&&t.call(f)}finally{if(y)throw y.error}}return S},E=function(u){return F(u)?u.reduce(function(n,t){return n+(t.span||1)},0):0},ee={layout:"horizontal",column:3,tableLayout:"auto"};function ae(u){var n,t=x.exports.useContext(U),f=t.getPrefixCls,i=t.componentConfig,S=t.rtl,y=t.size,s=W(u,ee,i==null?void 0:i.Descriptions),R=s.style,H=s.className,d=s.column,w=s.title,C=s.data,M=s.border,P=s.labelStyle,z=s.valueStyle,k=s.colon,g=s.layout,j=s.size,V=s.tableLayout,r=f("descriptions"),D=$(x.exports.useState(),2),_=D[0],q=D[1],I=x.exports.useRef(null);x.exports.useEffect(function(){return I.current=L.subscribe(function(a){for(var l=0;l<O.length;l++){var e=O[l];if(a[e]){q(e);break}}}),function(){L.unsubscribe(I.current)}},[]);var o=3;X(d)&&(o=d[_]||3),Y(d)&&d>0&&(o=d);var v=[];if(F(C)&&C.length>0&&o){C.forEach(function(a){var l=v[v.length-1],e=E(l);e===0?v.push([c(c({},a),{span:a.span?a.span>o?o:a.span:1})]):e===o?v.push([c(c({},a),{span:a.span?a.span>o?o:a.span:1})]):l.push(c(c({},a),{span:a.span?a.span+e>o?o-e:a.span:1}))});var N=v[v.length-1],T=E(N);T<o&&(N[N.length-1].span=N[N.length-1].span+o-T)}function B(a,l){return h(A,{children:[p("tr",{className:r+"-row",children:a.map(function(e,b){var m=e.span>1?{colSpan:e.span}:{};return h("td",{...c({key:(e.key||b)+"_label",className:r+"-item-label"},m,{style:P}),children:[e.label,k]})})}),p("tr",{className:r+"-row",children:a.map(function(e,b){var m=e.span>1?{colSpan:e.span}:{};return p("td",{...c({key:(e.key||b)+"_value",className:r+"-item-value"},m,{style:z}),children:e.value})})})]})}function G(a,l){return p("tr",{className:r+"-row",children:a.map(function(e,b){var m=e.span>1?{colSpan:e.span*2-1}:{};return h(A,{children:[h("td",{className:r+"-item-label",style:P,children:[e.label,k]}),p("td",{...c({className:r+"-item-value"},m,{style:z}),children:e.value})]})})},l)}function J(a,l){return p("tr",{className:r+"-row",children:a.map(function(e,b){var m=e.span>1?{colSpan:e.span}:{};return h("td",{...c({key:e.key||b},m,{className:r+"-item"}),children:[h("div",{className:r+"-item-label-inline",style:P,children:[e.label,k]}),p("div",{className:r+"-item-value-inline",style:z,children:e.value})]})})},l)}function K(a,l){return g==="inline-vertical"||g==="inline-horizontal"?J(a,l):g==="vertical"?B(a):G(a,l)}var Q=Z(r,(n={},n[r+"-border"]=M,n[r+"-layout-"+g]=g,n[r+"-size-"+(j||y)]=j||y,n[r+"-table-layout-fixed"]=V==="fixed",n[r+"-rtl"]=S,n),H);return h("div",{className:Q,style:R,children:[w&&p("div",{className:r+"-title",children:w}),p("div",{className:r+"-body",children:p("table",{className:r+"-table",cellPadding:0,cellSpacing:0,children:p("tbody",{children:v.map(function(a,l){return K(a,l)})})})})]})}ae.displayName="Descriptions";export{ae as D};
