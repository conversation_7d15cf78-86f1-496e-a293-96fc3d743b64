import{u as s,j as i,a4 as l,a as t,B as r}from"./index.7dafa16d.js";import{R as a}from"./index.503eee4e.js";import{C as n}from"./index.519f9d90.js";import{l as d}from"./index.662e0e1f.js";import c from"./data-statistic-list.7ace1de2.js";import{s as o}from"./index.module.1dd0e4f6.js";import"./b-tween.es.d368a2a1.js";function M(){const e=s(d);return i(n,{children:[i(l,{defaultActiveTab:"liveMethod",children:[t(l.Tab<PERSON>,{title:e["monitor.tab.title.liveMethod"]},"liveMethod"),t(l.<PERSON>b<PERSON><PERSON>,{title:e["monitor.tab.title.onlineUsers"]},"onlineUsers")]}),i("div",{className:o["data-statistic-content"],children:[i(a.Group,{defaultValue:"3",type:"button",children:[t(a,{value:"1",children:e["monitor.liveMethod.normal"]}),t(a,{value:"2",children:e["monitor.liveMethod.flowControl"]}),t(a,{value:"3",children:e["monitor.liveMethod.video"]}),t(a,{value:"4",children:e["monitor.liveMethod.web"]})]}),i("div",{className:o["data-statistic-list-wrapper"],children:[i("div",{className:o["data-statistic-list-header"],children:[t(r,{type:"text",children:e["monitor.editCarousel"]}),t(r,{disabled:!0,children:e["monitor.startCarousel"]})]}),t("div",{className:o["data-statistic-list-content"],children:t(c,{})})]})]})]})}export{M as default};
