import{r as e,a,aO as s,a7 as c,c as d,l as m}from"./index.7dafa16d.js";import{S as x}from"./index.43d26da3.js";import{s as f}from"./index.module.c0778cea.js";const{Paragraph:u}=d;function w(){const[r,i]=e.exports.useState(new Array(4).fill({})),[o,n]=e.exports.useState(!0),l=async()=>{const{data:t}=await m.get("/api/user/latestNews").finally(()=>n(!1));i(t)};return e.exports.useEffect(()=>{l()},[]),a(s,{dataSource:r,render:(t,p)=>a(s.Item,{style:{padding:"24px 20px 24px 0px"},children:o?a(x,{animation:!0,text:{width:["60%","90%"],rows:2},image:{shape:"circle"}}):a(s.Item.Meta,{className:f["list-meta-ellipsis"],avatar:a(c,{size:48,children:a("img",{src:t.avatar})}),title:t.title,description:a(u,{ellipsis:{rows:1},type:"secondary",style:{fontSize:"12px",margin:0},children:t.description})})},p)})}export{w as default};
