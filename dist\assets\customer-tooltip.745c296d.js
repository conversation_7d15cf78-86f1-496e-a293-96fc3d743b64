import{j as l,a as t,ar as d,c as n}from"./index.7dafa16d.js";var i={"customer-tooltip-title":"_customer-tooltip-title_1xpmx_1","customer-tooltip-item":"_customer-tooltip-item_1xpmx_4"};const{Text:r}=n;function p(e){const{formatter:c=o=>o,color:s,name:a}=e;return l("div",{className:i["customer-tooltip"],children:[t("div",{className:i["customer-tooltip-title"],children:t(r,{bold:!0,children:e.title})}),t("div",{children:e.data.map((o,m)=>l("div",{className:i["customer-tooltip-item"],children:[l("div",{children:[t(d,{color:s||o.color}),a||o.name]}),t("div",{children:t(r,{bold:!0,children:c(o.value)})})]},m))})]})}export{p as C};
