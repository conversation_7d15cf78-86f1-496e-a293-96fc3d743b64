import{R as x,r as b,g as y,j as i,a,_ as N,h as m,a7 as j,aN as w}from"./index.7dafa16d.js";import{S as u}from"./index.43d26da3.js";import{s}from"./index.module.c0778cea.js";import{I as C}from"./index.05c05262.js";function h(r,n){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(r);n&&(e=e.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),t.push.apply(t,e)}return t}function g(r){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?h(Object(t),!0).forEach(function(e){N(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):h(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function I(r,n){var t=b.exports.useContext(y),e=t.prefixCls,o=e===void 0?"arco":e,p=r.spin,l=r.className,c=g(g({"aria-hidden":!0,focusable:!1,ref:n},r),{},{className:"".concat(l?l+" ":"").concat(o,"-icon ").concat(o,"-icon-home")});return p&&(c.className="".concat(c.className," ").concat(o,"-icon-loading")),delete c.spin,delete c.isIcon,i("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...c,children:[a("path",{d:"M7 17 24 7l17 10v24H7V17Z"}),a("path",{d:"M20 28h8v13h-8V28Z"})]})}var d=x.forwardRef(I);d.defaultProps={isIcon:!0};d.displayName="IconHome";var P=d;function v(r,n){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(r);n&&(e=e.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),t.push.apply(t,e)}return t}function O(r){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?v(Object(t),!0).forEach(function(e){N(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):v(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function S(r,n){var t=b.exports.useContext(y),e=t.prefixCls,o=e===void 0?"arco":e,p=r.spin,l=r.className,c=O(O({"aria-hidden":!0,focusable:!1,ref:n},r),{},{className:"".concat(l?l+" ":"").concat(o,"-icon ").concat(o,"-icon-location")});return p&&(c.className="".concat(c.className," ").concat(o,"-icon-loading")),delete c.spin,delete c.isIcon,i("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...c,children:[a("circle",{cx:"24",cy:"19",r:"5"}),a("path",{d:"M39 20.405C39 28.914 24 43 24 43S9 28.914 9 20.405C9 11.897 15.716 5 24 5c8.284 0 15 6.897 15 15.405Z"})]})}var f=x.forwardRef(S);f.defaultProps={isIcon:!0};f.displayName="IconLocation";var D=f;function z(r){const{userInfo:n={},loading:t}=r,e=a(u,{text:{rows:1,style:{width:"100px",height:"20px",marginBottom:"-4px"},width:["100%"]},animation:!0}),o=a(u,{text:{rows:0},image:{style:{width:"64px",height:"64px"},shape:"circle"},animation:!0});return a("div",{className:s.header,children:i(m,{size:8,direction:"vertical",align:"center",className:s["header-content"],children:[t?o:a(j,{size:64,triggerIcon:a(C,{}),children:a("img",{src:n.avatar})}),a("div",{className:s.username,children:t?e:n.name}),a("div",{className:s["user-msg"],children:i(m,{size:18,children:[i("div",{children:[a(w,{}),a("span",{className:s["user-msg-text"],children:t?e:n.jobName})]}),i("div",{children:[a(P,{}),a("span",{className:s["user-msg-text"],children:t?e:n.organizationName})]}),i("div",{children:[a(D,{}),a("span",{className:s["user-msg-text"],children:t?e:n.locationName})]})]})})]})})}export{z as default};
