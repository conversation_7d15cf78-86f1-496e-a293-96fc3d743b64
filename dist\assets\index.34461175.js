import{u as C,r as s,j as i,a5 as n,a as e,c as u,h as v,B as o,ar as m,l}from"./index.7dafa16d.js";import{T as w}from"./index.503eee4e.js";import{S as r}from"./index.f15c5449.js";import{C as P}from"./index.519f9d90.js";import{i as N,P as b}from"./item.c13e41ce.js";import"./b-tween.es.d368a2a1.js";import"./index.43d26da3.js";import"./index.69569894.js";const k="_container_2201e_1",A="_steps_2201e_4";var h={container:k,steps:A};function K(){const t=C(N),[g,c]=s.exports.useState(!1),[d,x]=s.exports.useState({status:1}),[j,p]=s.exports.useState(!1),[y,S]=s.exports.useState({}),[T,f]=s.exports.useState(!1),[D,I]=s.exports.useState([]);function L(){c(!0),l.get("/api/basicProfile").then(a=>{x(a.data||{})}).finally(()=>{c(!1)})}function _(){p(!0),l.get("/api/basicProfile").then(a=>{S(a.data||{})}).finally(()=>{p(!1)})}function B(){f(!0),l.get("/api/adjustment").then(a=>{I(a.data)}).finally(()=>{f(!1)})}return s.exports.useEffect(()=>{L(),_(),B()},[]),i("div",{className:h.container,children:[i(P,{children:[i(n.Row,{justify:"space-between",align:"center",children:[e(n.Col,{span:16,children:e(u.Title,{heading:6,children:t["basicProfile.title.form"]})}),e(n.Col,{span:8,style:{textAlign:"right"},children:i(v,{children:[e(o,{children:t["basicProfile.cancel"]}),e(o,{type:"primary",children:t["basicProfile.goBack"]})]})})]}),i(r,{current:d.status,lineless:!0,className:h.steps,children:[e(r.Step,{title:t["basicProfile.steps.commit"]}),e(r.Step,{title:t["basicProfile.steps.approval"]}),e(r.Step,{title:t["basicProfile.steps.finish"]})]})]}),e(b,{title:t["basicProfile.title.currentParams"],data:d,type:"current",loading:g}),e(b,{title:t["basicProfile.title.originParams"],data:y,type:"origin",loading:j}),i(P,{children:[e(u.Title,{heading:6,children:t["basicProfile.adjustment.record"]}),e(w,{loading:T,data:D,columns:[{dataIndex:"contentId",title:t["basicProfile.adjustment.contentId"]},{dataIndex:"content",title:t["basicProfile.adjustment.content"]},{dataIndex:"status",title:t["basicProfile.adjustment.status"],render:a=>a?e(m,{status:"success",text:t["basicProfile.adjustment.success"]}):e(m,{status:"processing",text:t["basicProfile.adjustment.waiting"]})},{dataIndex:"updatedTime",title:t["basicProfile.adjustment.updatedTime"]},{title:t["basicProfile.adjustment.operation"],headerCellStyle:{paddingLeft:"15px"},render(){return e(o,{type:"text",children:t["basicProfile.adjustment.operation.view"]})}}]})]})]})}export{K as default};
