import{a as br,S as a1,j as zn,ad as oe,al as ao,bd as ip,be as o1,u as ap,r as Me,h as op,a5 as up,c as fp,l as Oo}from"./index.7dafa16d.js";import{C as Si}from"./index.519f9d90.js";import{l as lp,C as Df,c as u1,I as sp,T as f1,A as l1,r as pr,d as or,L as cp,a as vp,b as s1,F as hp}from"./index.1a52f4db.js";import{C as c1}from"./customer-tooltip.745c296d.js";import{u as gp}from"./useChartTheme.b3e60e50.js";import{l as pp}from"./index.b0c339fc.js";import dp from"./data-overview.4c06ea82.js";import mp from"./card-list.fc38b838.js";import"./index.43d26da3.js";import"./index.dc3f6cf8.js";import"./b-tween.es.d368a2a1.js";import"./pad.af73d6a9.js";import"./index.9498526d.js";function yp({data:r,loading:n,height:e}){return lp.registerShape("interval","border-radius",{draw(t,i){const a=t.points;let o=[];o.push(["M",a[0].x,a[0].y]),o.push(["L",a[1].x,a[1].y]),o.push(["L",a[2].x,a[2].y]),o.push(["L",a[3].x,a[3].y]),o.push("Z"),o=this.parsePath(o);const s=i.addGroup(),l=(o[1][2]-o[2][2])/2;return s.addShape("rect",{attrs:{x:o[0][1],y:o[0][2]-l*2,width:o[1][1]-o[0][1],height:o[1][2]-o[2][2],fill:t.color,radius:l}}),s}}),br(a1,{loading:n,style:{width:"100%"},children:zn(Df,{height:e||370,padding:"auto",data:r,autoFit:!0,className:"chart-wrapper",children:[br(u1,{transpose:!0}),br(sp,{color:"#4086FF",position:"name*count",size:10,shape:"border-radius"}),br(f1,{children:(t,i)=>br(c1,{title:t,data:i})}),br(l1,{name:"count",label:{formatter(t){return`${Number(t)/1e3}k`}}})]})})}function Se(){return new sa}function sa(){this.reset()}sa.prototype={constructor:sa,reset:function(){this.s=this.t=0},add:function(r){Is(_i,r,this.t),Is(this,_i.s,this.s),this.s?this.t+=_i.t:this.s=_i.t},valueOf:function(){return this.s}};var _i=new sa;function Is(r,n,e){var t=r.s=n+e,i=t-n,a=t-i;r.t=n-a+(e-i)}var tr=1e-6,Ns=1e-12,yr=Math.PI,Yr=yr/2,ca=yr/4,vn=yr*2,Nr=180/yr,ar=yr/180,Pr=Math.abs,gt=Math.atan,on=Math.atan2,nr=Math.cos,Ti=Math.ceil,v1=Math.exp,va=Math.log,qo=Math.pow,Z=Math.sin,Zt=Math.sign||function(r){return r>0?1:r<0?-1:0},Xr=Math.sqrt,Of=Math.tan;function h1(r){return r>1?0:r<-1?yr:Math.acos(r)}function Sn(r){return r>1?Yr:r<-1?-Yr:Math.asin(r)}function As(r){return(r=Z(r/2))*r}function Or(){}function ha(r,n){r&&Os.hasOwnProperty(r.type)&&Os[r.type](r,n)}var Ds={Feature:function(r,n){ha(r.geometry,n)},FeatureCollection:function(r,n){for(var e=r.features,t=-1,i=e.length;++t<i;)ha(e[t].geometry,n)}},Os={Sphere:function(r,n){n.sphere()},Point:function(r,n){r=r.coordinates,n.point(r[0],r[1],r[2])},MultiPoint:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)r=e[t],n.point(r[0],r[1],r[2])},LineString:function(r,n){Au(r.coordinates,n,0)},MultiLineString:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)Au(e[t],n,0)},Polygon:function(r,n){qs(r.coordinates,n)},MultiPolygon:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)qs(e[t],n)},GeometryCollection:function(r,n){for(var e=r.geometries,t=-1,i=e.length;++t<i;)ha(e[t],n)}};function Au(r,n,e){var t=-1,i=r.length-e,a;for(n.lineStart();++t<i;)a=r[t],n.point(a[0],a[1],a[2]);n.lineEnd()}function qs(r,n){var e=-1,t=r.length;for(n.polygonStart();++e<t;)Au(r[e],n,1);n.polygonEnd()}function sn(r,n){r&&Ds.hasOwnProperty(r.type)?Ds[r.type](r,n):ha(r,n)}var ga=Se(),pa=Se(),g1,p1,Du,Ou,qu,Vn={point:Or,lineStart:Or,lineEnd:Or,polygonStart:function(){ga.reset(),Vn.lineStart=wp,Vn.lineEnd=Ep},polygonEnd:function(){var r=+ga;pa.add(r<0?vn+r:r),this.lineStart=this.lineEnd=this.point=Or},sphere:function(){pa.add(vn)}};function wp(){Vn.point=Sp}function Ep(){d1(g1,p1)}function Sp(r,n){Vn.point=d1,g1=r,p1=n,r*=ar,n*=ar,Du=r,Ou=nr(n=n/2+ca),qu=Z(n)}function d1(r,n){r*=ar,n*=ar,n=n/2+ca;var e=r-Du,t=e>=0?1:-1,i=t*e,a=nr(n),o=Z(n),s=qu*o,l=Ou*a+s*nr(i),c=s*t*Z(i);ga.add(on(c,l)),Du=r,Ou=a,qu=o}function _p(r){return pa.reset(),sn(r,Vn),pa*2}function da(r){return[on(r[1],r[0]),Sn(r[2])]}function Oe(r){var n=r[0],e=r[1],t=nr(e);return[t*nr(n),t*Z(n),Z(e)]}function $i(r,n){return r[0]*n[0]+r[1]*n[1]+r[2]*n[2]}function et(r,n){return[r[1]*n[2]-r[2]*n[1],r[2]*n[0]-r[0]*n[2],r[0]*n[1]-r[1]*n[0]]}function Fo(r,n){r[0]+=n[0],r[1]+=n[1],r[2]+=n[2]}function Mi(r,n){return[r[0]*n,r[1]*n,r[2]*n]}function ma(r){var n=Xr(r[0]*r[0]+r[1]*r[1]+r[2]*r[2]);r[0]/=n,r[1]/=n,r[2]/=n}var Fr,wn,Gr,xn,Re,m1,y1,Ke,Wt=Se(),ve,ne,Kn={point:Fu,lineStart:Fs,lineEnd:zs,polygonStart:function(){Kn.point=E1,Kn.lineStart=Tp,Kn.lineEnd=$p,Wt.reset(),Vn.polygonStart()},polygonEnd:function(){Vn.polygonEnd(),Kn.point=Fu,Kn.lineStart=Fs,Kn.lineEnd=zs,ga<0?(Fr=-(Gr=180),wn=-(xn=90)):Wt>tr?xn=90:Wt<-tr&&(wn=-90),ne[0]=Fr,ne[1]=Gr}};function Fu(r,n){ve.push(ne=[Fr=r,Gr=r]),n<wn&&(wn=n),n>xn&&(xn=n)}function w1(r,n){var e=Oe([r*ar,n*ar]);if(Ke){var t=et(Ke,e),i=[t[1],-t[0],0],a=et(i,t);ma(a),a=da(a);var o=r-Re,s=o>0?1:-1,l=a[0]*Nr*s,c,u=Pr(o)>180;u^(s*Re<l&&l<s*r)?(c=a[1]*Nr,c>xn&&(xn=c)):(l=(l+360)%360-180,u^(s*Re<l&&l<s*r)?(c=-a[1]*Nr,c<wn&&(wn=c)):(n<wn&&(wn=n),n>xn&&(xn=n))),u?r<Re?mn(Fr,r)>mn(Fr,Gr)&&(Gr=r):mn(r,Gr)>mn(Fr,Gr)&&(Fr=r):Gr>=Fr?(r<Fr&&(Fr=r),r>Gr&&(Gr=r)):r>Re?mn(Fr,r)>mn(Fr,Gr)&&(Gr=r):mn(r,Gr)>mn(Fr,Gr)&&(Fr=r)}else ve.push(ne=[Fr=r,Gr=r]);n<wn&&(wn=n),n>xn&&(xn=n),Ke=e,Re=r}function Fs(){Kn.point=w1}function zs(){ne[0]=Fr,ne[1]=Gr,Kn.point=Fu,Ke=null}function E1(r,n){if(Ke){var e=r-Re;Wt.add(Pr(e)>180?e+(e>0?360:-360):e)}else m1=r,y1=n;Vn.point(r,n),w1(r,n)}function Tp(){Vn.lineStart()}function $p(){E1(m1,y1),Vn.lineEnd(),Pr(Wt)>tr&&(Fr=-(Gr=180)),ne[0]=Fr,ne[1]=Gr,Ke=null}function mn(r,n){return(n-=r)<0?n+360:n}function Mp(r,n){return r[0]-n[0]}function Bs(r,n){return r[0]<=r[1]?r[0]<=n&&n<=r[1]:n<r[0]||r[1]<n}function S1(r){var n,e,t,i,a,o,s;if(xn=Gr=-(Fr=wn=1/0),ve=[],sn(r,Kn),e=ve.length){for(ve.sort(Mp),n=1,t=ve[0],a=[t];n<e;++n)i=ve[n],Bs(t,i[0])||Bs(t,i[1])?(mn(t[0],i[1])>mn(t[0],t[1])&&(t[1]=i[1]),mn(i[0],t[1])>mn(t[0],t[1])&&(t[0]=i[0])):a.push(t=i);for(o=-1/0,e=a.length-1,n=0,t=a[e];n<=e;t=i,++n)i=a[n],(s=mn(t[1],i[0]))>o&&(o=s,Fr=i[0],Gr=t[1])}return ve=ne=null,Fr===1/0||wn===1/0?[[NaN,NaN],[NaN,NaN]]:[[Fr,wn],[Gr,xn]]}var Nt,ya,wa,Ea,Sa,_a,Ta,$a,zu,Bu,Gu,_1,T1,en,tn,an,An={sphere:Or,point:qf,lineStart:Gs,lineEnd:Us,polygonStart:function(){An.lineStart=Pp,An.lineEnd=Rp},polygonEnd:function(){An.lineStart=Gs,An.lineEnd=Us}};function qf(r,n){r*=ar,n*=ar;var e=nr(n);si(e*nr(r),e*Z(r),Z(n))}function si(r,n,e){++Nt,wa+=(r-wa)/Nt,Ea+=(n-Ea)/Nt,Sa+=(e-Sa)/Nt}function Gs(){An.point=xp}function xp(r,n){r*=ar,n*=ar;var e=nr(n);en=e*nr(r),tn=e*Z(r),an=Z(n),An.point=bp,si(en,tn,an)}function bp(r,n){r*=ar,n*=ar;var e=nr(n),t=e*nr(r),i=e*Z(r),a=Z(n),o=on(Xr((o=tn*a-an*i)*o+(o=an*t-en*a)*o+(o=en*i-tn*t)*o),en*t+tn*i+an*a);ya+=o,_a+=o*(en+(en=t)),Ta+=o*(tn+(tn=i)),$a+=o*(an+(an=a)),si(en,tn,an)}function Us(){An.point=qf}function Pp(){An.point=kp}function Rp(){$1(_1,T1),An.point=qf}function kp(r,n){_1=r,T1=n,r*=ar,n*=ar,An.point=$1;var e=nr(n);en=e*nr(r),tn=e*Z(r),an=Z(n),si(en,tn,an)}function $1(r,n){r*=ar,n*=ar;var e=nr(n),t=e*nr(r),i=e*Z(r),a=Z(n),o=tn*a-an*i,s=an*t-en*a,l=en*i-tn*t,c=Xr(o*o+s*s+l*l),u=Sn(c),v=c&&-u/c;zu+=v*o,Bu+=v*s,Gu+=v*l,ya+=u,_a+=u*(en+(en=t)),Ta+=u*(tn+(tn=i)),$a+=u*(an+(an=a)),si(en,tn,an)}function pt(r){Nt=ya=wa=Ea=Sa=_a=Ta=$a=zu=Bu=Gu=0,sn(r,An);var n=zu,e=Bu,t=Gu,i=n*n+e*e+t*t;return i<Ns&&(n=_a,e=Ta,t=$a,ya<tr&&(n=wa,e=Ea,t=Sa),i=n*n+e*e+t*t,i<Ns)?[NaN,NaN]:[on(e,n)*Nr,Sn(t/Xr(i))*Nr]}function Ue(r){return function(){return r}}function M1(r,n){function e(t,i){return t=r(t,i),n(t[0],t[1])}return r.invert&&n.invert&&(e.invert=function(t,i){return t=n.invert(t,i),t&&r.invert(t[0],t[1])}),e}function Uu(r,n){return[r>yr?r-vn:r<-yr?r+vn:r,n]}Uu.invert=Uu;function Ff(r,n,e){return(r%=vn)?n||e?M1(Ys(r),Vs(n,e)):Ys(r):n||e?Vs(n,e):Uu}function Hs(r){return function(n,e){return n+=r,[n>yr?n-vn:n<-yr?n+vn:n,e]}}function Ys(r){var n=Hs(r);return n.invert=Hs(-r),n}function Vs(r,n){var e=nr(r),t=Z(r),i=nr(n),a=Z(n);function o(s,l){var c=nr(l),u=nr(s)*c,v=Z(s)*c,f=Z(l),h=f*e+u*t;return[on(v*i-h*a,u*e-f*t),Sn(h*i+v*a)]}return o.invert=function(s,l){var c=nr(l),u=nr(s)*c,v=Z(s)*c,f=Z(l),h=f*i-v*a;return[on(v*i+f*a,u*e+h*t),Sn(h*e-u*t)]},o}function tt(r){r=Ff(r[0]*ar,r[1]*ar,r.length>2?r[2]*ar:0);function n(e){return e=r(e[0]*ar,e[1]*ar),e[0]*=Nr,e[1]*=Nr,e}return n.invert=function(e){return e=r.invert(e[0]*ar,e[1]*ar),e[0]*=Nr,e[1]*=Nr,e},n}function x1(r,n,e,t,i,a){if(!!e){var o=nr(n),s=Z(n),l=t*e;i==null?(i=n+t*vn,a=n-l/2):(i=Xs(o,i),a=Xs(o,a),(t>0?i<a:i>a)&&(i+=t*vn));for(var c,u=i;t>0?u>a:u<a;u-=l)c=da([o,-s*nr(u),-s*Z(u)]),r.point(c[0],c[1])}}function Xs(r,n){n=Oe(n),n[0]-=r,ma(n);var e=h1(-n[1]);return((-n[2]<0?-e:e)+vn-tr)%vn}function b1(){var r=Ue([0,0]),n=Ue(90),e=Ue(6),t,i,a={point:o};function o(l,c){t.push(l=i(l,c)),l[0]*=Nr,l[1]*=Nr}function s(){var l=r.apply(this,arguments),c=n.apply(this,arguments)*ar,u=e.apply(this,arguments)*ar;return t=[],i=Ff(-l[0]*ar,-l[1]*ar,0).invert,x1(a,c,u,1),l={type:"Polygon",coordinates:[t]},t=i=null,l}return s.center=function(l){return arguments.length?(r=typeof l=="function"?l:Ue([+l[0],+l[1]]),s):r},s.radius=function(l){return arguments.length?(n=typeof l=="function"?l:Ue(+l),s):n},s.precision=function(l){return arguments.length?(e=typeof l=="function"?l:Ue(+l),s):e},s}function P1(){var r=[],n;return{point:function(e,t){n.push([e,t])},lineStart:function(){r.push(n=[])},lineEnd:Or,rejoin:function(){r.length>1&&r.push(r.pop().concat(r.shift()))},result:function(){var e=r;return r=[],n=null,e}}}function Cp(r,n,e,t,i,a){var o=r[0],s=r[1],l=n[0],c=n[1],u=0,v=1,f=l-o,h=c-s,g;if(g=e-o,!(!f&&g>0)){if(g/=f,f<0){if(g<u)return;g<v&&(v=g)}else if(f>0){if(g>v)return;g>u&&(u=g)}if(g=i-o,!(!f&&g<0)){if(g/=f,f<0){if(g>v)return;g>u&&(u=g)}else if(f>0){if(g<u)return;g<v&&(v=g)}if(g=t-s,!(!h&&g>0)){if(g/=h,h<0){if(g<u)return;g<v&&(v=g)}else if(h>0){if(g>v)return;g>u&&(u=g)}if(g=a-s,!(!h&&g<0)){if(g/=h,h<0){if(g>v)return;g>u&&(u=g)}else if(h>0){if(g<u)return;g<v&&(v=g)}return u>0&&(r[0]=o+u*f,r[1]=s+u*h),v<1&&(n[0]=o+v*f,n[1]=s+v*h),!0}}}}}function Qi(r,n){return Pr(r[0]-n[0])<tr&&Pr(r[1]-n[1])<tr}function xi(r,n,e,t){this.x=r,this.z=n,this.o=e,this.e=t,this.v=!1,this.n=this.p=null}function R1(r,n,e,t,i){var a=[],o=[],s,l;if(r.forEach(function(g){if(!((p=g.length-1)<=0)){var p,d=g[0],m=g[p],y;if(Qi(d,m)){for(i.lineStart(),s=0;s<p;++s)i.point((d=g[s])[0],d[1]);i.lineEnd();return}a.push(y=new xi(d,g,null,!0)),o.push(y.o=new xi(d,null,y,!1)),a.push(y=new xi(m,g,null,!1)),o.push(y.o=new xi(m,null,y,!0))}}),!!a.length){for(o.sort(n),js(a),js(o),s=0,l=o.length;s<l;++s)o[s].e=e=!e;for(var c=a[0],u,v;;){for(var f=c,h=!0;f.v;)if((f=f.n)===c)return;u=f.z,i.lineStart();do{if(f.v=f.o.v=!0,f.e){if(h)for(s=0,l=u.length;s<l;++s)i.point((v=u[s])[0],v[1]);else t(f.x,f.n.x,1,i);f=f.n}else{if(h)for(u=f.p.z,s=u.length-1;s>=0;--s)i.point((v=u[s])[0],v[1]);else t(f.x,f.p.x,-1,i);f=f.p}f=f.o,u=f.z,h=!h}while(!f.v);i.lineEnd()}}}function js(r){if(!!(n=r.length)){for(var n,e=0,t=r[0],i;++e<n;)t.n=i=r[e],i.p=t,t=i;t.n=i=r[0],i.p=t}}function zf(r,n){return r<n?-1:r>n?1:r>=n?0:NaN}function Lp(r){return r.length===1&&(r=Ip(r)),{left:function(n,e,t,i){for(t==null&&(t=0),i==null&&(i=n.length);t<i;){var a=t+i>>>1;r(n[a],e)<0?t=a+1:i=a}return t},right:function(n,e,t,i){for(t==null&&(t=0),i==null&&(i=n.length);t<i;){var a=t+i>>>1;r(n[a],e)>0?i=a:t=a+1}return t}}}function Ip(r){return function(n,e){return zf(r(n),e)}}Lp(zf);function ge(r,n,e){r=+r,n=+n,e=(i=arguments.length)<2?(n=r,r=0,1):i<3?1:+e;for(var t=-1,i=Math.max(0,Math.ceil((n-r)/e))|0,a=new Array(i);++t<i;)a[t]=r+t*e;return a}function me(r,n){var e=r.length,t=-1,i,a;if(n==null){for(;++t<e;)if((i=r[t])!=null&&i>=i)for(a=i;++t<e;)(i=r[t])!=null&&i>a&&(a=i)}else for(;++t<e;)if((i=n(r[t],t,r))!=null&&i>=i)for(a=i;++t<e;)(i=n(r[t],t,r))!=null&&i>a&&(a=i);return a}function Bf(r){for(var n=r.length,e,t=-1,i=0,a,o;++t<n;)i+=r[t].length;for(a=new Array(i);--n>=0;)for(o=r[n],e=o.length;--e>=0;)a[--i]=o[e];return a}function rn(r,n){var e=r.length,t=-1,i,a;if(n==null){for(;++t<e;)if((i=r[t])!=null&&i>=i)for(a=i;++t<e;)(i=r[t])!=null&&a>i&&(a=i)}else for(;++t<e;)if((i=n(r[t],t,r))!=null&&i>=i)for(a=i;++t<e;)(i=n(r[t],t,r))!=null&&a>i&&(a=i);return a}function zo(r,n){var e=r.length,t=-1,i,a=0;if(n==null)for(;++t<e;)(i=+r[t])&&(a+=i);else for(;++t<e;)(i=+n(r[t],t,r))&&(a+=i);return a}var At=1e9,bi=-At;function Gf(r,n,e,t){function i(c,u){return r<=c&&c<=e&&n<=u&&u<=t}function a(c,u,v,f){var h=0,g=0;if(c==null||(h=o(c,v))!==(g=o(u,v))||l(c,u)<0^v>0)do f.point(h===0||h===3?r:e,h>1?t:n);while((h=(h+v+4)%4)!==g);else f.point(u[0],u[1])}function o(c,u){return Pr(c[0]-r)<tr?u>0?0:3:Pr(c[0]-e)<tr?u>0?2:1:Pr(c[1]-n)<tr?u>0?1:0:u>0?3:2}function s(c,u){return l(c.x,u.x)}function l(c,u){var v=o(c,1),f=o(u,1);return v!==f?v-f:v===0?u[1]-c[1]:v===1?c[0]-u[0]:v===2?c[1]-u[1]:u[0]-c[0]}return function(c){var u=c,v=P1(),f,h,g,p,d,m,y,E,S,$,x,I={point:C,lineStart:k,lineEnd:U,polygonStart:O,polygonEnd:A};function C(w,b){i(w,b)&&u.point(w,b)}function L(){for(var w=0,b=0,P=h.length;b<P;++b)for(var D=h[b],rr=1,hr=D.length,ur=D[0],gr,xr,Ur=ur[0],jr=ur[1];rr<hr;++rr)gr=Ur,xr=jr,ur=D[rr],Ur=ur[0],jr=ur[1],xr<=t?jr>t&&(Ur-gr)*(t-xr)>(jr-xr)*(r-gr)&&++w:jr<=t&&(Ur-gr)*(t-xr)<(jr-xr)*(r-gr)&&--w;return w}function O(){u=v,f=[],h=[],x=!0}function A(){var w=L(),b=x&&w,P=(f=Bf(f)).length;(b||P)&&(c.polygonStart(),b&&(c.lineStart(),a(null,null,1,c),c.lineEnd()),P&&R1(f,s,w,a,c),c.polygonEnd()),u=c,f=h=g=null}function k(){I.point=_,h&&h.push(g=[]),$=!0,S=!1,y=E=NaN}function U(){f&&(_(p,d),m&&S&&v.rejoin(),f.push(v.result())),I.point=C,S&&u.lineEnd()}function _(w,b){var P=i(w,b);if(h&&g.push([w,b]),$)p=w,d=b,m=P,$=!1,P&&(u.lineStart(),u.point(w,b));else if(P&&S)u.point(w,b);else{var D=[y=Math.max(bi,Math.min(At,y)),E=Math.max(bi,Math.min(At,E))],rr=[w=Math.max(bi,Math.min(At,w)),b=Math.max(bi,Math.min(At,b))];Cp(D,rr,r,n,e,t)?(S||(u.lineStart(),u.point(D[0],D[1])),u.point(rr[0],rr[1]),P||u.lineEnd(),x=!1):P&&(u.lineStart(),u.point(w,b),x=!1)}y=w,E=b,S=P}return I}}function Np(){var r=0,n=0,e=960,t=500,i,a,o;return o={stream:function(s){return i&&a===s?i:i=Gf(r,n,e,t)(a=s)},extent:function(s){return arguments.length?(r=+s[0][0],n=+s[0][1],e=+s[1][0],t=+s[1][1],i=a=null,o):[[r,n],[e,t]]}}}var Bo=Se();function k1(r,n){var e=n[0],t=n[1],i=[Z(e),-nr(e),0],a=0,o=0;Bo.reset();for(var s=0,l=r.length;s<l;++s)if(!!(u=(c=r[s]).length))for(var c,u,v=c[u-1],f=v[0],h=v[1]/2+ca,g=Z(h),p=nr(h),d=0;d<u;++d,f=y,g=S,p=$,v=m){var m=c[d],y=m[0],E=m[1]/2+ca,S=Z(E),$=nr(E),x=y-f,I=x>=0?1:-1,C=I*x,L=C>yr,O=g*S;if(Bo.add(on(O*I*Z(C),p*$+O*nr(C))),a+=L?x+I*vn:x,L^f>=e^y>=e){var A=et(Oe(v),Oe(m));ma(A);var k=et(i,A);ma(k);var U=(L^x>=0?-1:1)*Sn(k[2]);(t>U||t===U&&(A[0]||A[1]))&&(o+=L^x>=0?1:-1)}}return(a<-tr||a<tr&&Bo<-tr)^o&1}var Hu=Se(),Yu,Zi,ra,it={sphere:Or,point:Or,lineStart:Ap,lineEnd:Or,polygonStart:Or,polygonEnd:Or};function Ap(){it.point=Op,it.lineEnd=Dp}function Dp(){it.point=it.lineEnd=Or}function Op(r,n){r*=ar,n*=ar,Yu=r,Zi=Z(n),ra=nr(n),it.point=qp}function qp(r,n){r*=ar,n*=ar;var e=Z(n),t=nr(n),i=Pr(r-Yu),a=nr(i),o=Z(i),s=t*o,l=ra*e-Zi*t*a,c=Zi*e+ra*t*a;Hu.add(on(Xr(s*s+l*l),c)),Yu=r,Zi=e,ra=t}function C1(r){return Hu.reset(),sn(r,it),+Hu}var Vu=[null,null],Fp={type:"LineString",coordinates:Vu};function Jt(r,n){return Vu[0]=r,Vu[1]=n,C1(Fp)}var Ws={Feature:function(r,n){return Ma(r.geometry,n)},FeatureCollection:function(r,n){for(var e=r.features,t=-1,i=e.length;++t<i;)if(Ma(e[t].geometry,n))return!0;return!1}},Js={Sphere:function(){return!0},Point:function(r,n){return Ks(r.coordinates,n)},MultiPoint:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)if(Ks(e[t],n))return!0;return!1},LineString:function(r,n){return Qs(r.coordinates,n)},MultiLineString:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)if(Qs(e[t],n))return!0;return!1},Polygon:function(r,n){return Zs(r.coordinates,n)},MultiPolygon:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)if(Zs(e[t],n))return!0;return!1},GeometryCollection:function(r,n){for(var e=r.geometries,t=-1,i=e.length;++t<i;)if(Ma(e[t],n))return!0;return!1}};function Ma(r,n){return r&&Js.hasOwnProperty(r.type)?Js[r.type](r,n):!1}function Ks(r,n){return Jt(r,n)===0}function Qs(r,n){var e=Jt(r[0],r[1]),t=Jt(r[0],n),i=Jt(n,r[1]);return t+i<=e+tr}function Zs(r,n){return!!k1(r.map(zp),L1(n))}function zp(r){return r=r.map(L1),r.pop(),r}function L1(r){return[r[0]*ar,r[1]*ar]}function Bp(r,n){return(r&&Ws.hasOwnProperty(r.type)?Ws[r.type]:Ma)(r,n)}function rc(r,n,e){var t=ge(r,n-tr,e).concat(n);return function(i){return t.map(function(a){return[i,a]})}}function nc(r,n,e){var t=ge(r,n-tr,e).concat(n);return function(i){return t.map(function(a){return[a,i]})}}function I1(){var r,n,e,t,i,a,o,s,l=10,c=l,u=90,v=360,f,h,g,p,d=2.5;function m(){return{type:"MultiLineString",coordinates:y()}}function y(){return ge(Ti(t/u)*u,e,u).map(g).concat(ge(Ti(s/v)*v,o,v).map(p)).concat(ge(Ti(n/l)*l,r,l).filter(function(E){return Pr(E%u)>tr}).map(f)).concat(ge(Ti(a/c)*c,i,c).filter(function(E){return Pr(E%v)>tr}).map(h))}return m.lines=function(){return y().map(function(E){return{type:"LineString",coordinates:E}})},m.outline=function(){return{type:"Polygon",coordinates:[g(t).concat(p(o).slice(1),g(e).reverse().slice(1),p(s).reverse().slice(1))]}},m.extent=function(E){return arguments.length?m.extentMajor(E).extentMinor(E):m.extentMinor()},m.extentMajor=function(E){return arguments.length?(t=+E[0][0],e=+E[1][0],s=+E[0][1],o=+E[1][1],t>e&&(E=t,t=e,e=E),s>o&&(E=s,s=o,o=E),m.precision(d)):[[t,s],[e,o]]},m.extentMinor=function(E){return arguments.length?(n=+E[0][0],r=+E[1][0],a=+E[0][1],i=+E[1][1],n>r&&(E=n,n=r,r=E),a>i&&(E=a,a=i,i=E),m.precision(d)):[[n,a],[r,i]]},m.step=function(E){return arguments.length?m.stepMajor(E).stepMinor(E):m.stepMinor()},m.stepMajor=function(E){return arguments.length?(u=+E[0],v=+E[1],m):[u,v]},m.stepMinor=function(E){return arguments.length?(l=+E[0],c=+E[1],m):[l,c]},m.precision=function(E){return arguments.length?(d=+E,f=rc(a,i,90),h=nc(n,r,d),g=rc(s,o,90),p=nc(t,e,d),m):d},m.extentMajor([[-180,-90+tr],[180,90-tr]]).extentMinor([[-180,-80-tr],[180,80+tr]])}function Gp(){return I1()()}function xa(r,n){var e=r[0]*ar,t=r[1]*ar,i=n[0]*ar,a=n[1]*ar,o=nr(t),s=Z(t),l=nr(a),c=Z(a),u=o*nr(e),v=o*Z(e),f=l*nr(i),h=l*Z(i),g=2*Sn(Xr(As(a-t)+o*l*As(i-e))),p=Z(g),d=g?function(m){var y=Z(m*=g)/p,E=Z(g-m)/p,S=E*u+y*f,$=E*v+y*h,x=E*s+y*c;return[on($,S)*Nr,on(x,Xr(S*S+$*$))*Nr]}:function(){return[e*Nr,t*Nr]};return d.distance=g,d}function Ae(r){return r}var Go=Se(),Xu=Se(),N1,A1,ju,Wu,Qn={point:Or,lineStart:Or,lineEnd:Or,polygonStart:function(){Qn.lineStart=Up,Qn.lineEnd=Yp},polygonEnd:function(){Qn.lineStart=Qn.lineEnd=Qn.point=Or,Go.add(Pr(Xu)),Xu.reset()},result:function(){var r=Go/2;return Go.reset(),r}};function Up(){Qn.point=Hp}function Hp(r,n){Qn.point=D1,N1=ju=r,A1=Wu=n}function D1(r,n){Xu.add(Wu*r-ju*n),ju=r,Wu=n}function Yp(){D1(N1,A1)}var at=1/0,ba=at,ri=-at,Pa=ri,Ra={point:Vp,lineStart:Or,lineEnd:Or,polygonStart:Or,polygonEnd:Or,result:function(){var r=[[at,ba],[ri,Pa]];return ri=Pa=-(ba=at=1/0),r}};function Vp(r,n){r<at&&(at=r),r>ri&&(ri=r),n<ba&&(ba=n),n>Pa&&(Pa=n)}var Ju=0,Ku=0,Dt=0,ka=0,Ca=0,Xe=0,Qu=0,Zu=0,Ot=0,O1,q1,Gn,Un,bn={point:qe,lineStart:ec,lineEnd:tc,polygonStart:function(){bn.lineStart=Wp,bn.lineEnd=Jp},polygonEnd:function(){bn.point=qe,bn.lineStart=ec,bn.lineEnd=tc},result:function(){var r=Ot?[Qu/Ot,Zu/Ot]:Xe?[ka/Xe,Ca/Xe]:Dt?[Ju/Dt,Ku/Dt]:[NaN,NaN];return Ju=Ku=Dt=ka=Ca=Xe=Qu=Zu=Ot=0,r}};function qe(r,n){Ju+=r,Ku+=n,++Dt}function ec(){bn.point=Xp}function Xp(r,n){bn.point=jp,qe(Gn=r,Un=n)}function jp(r,n){var e=r-Gn,t=n-Un,i=Xr(e*e+t*t);ka+=i*(Gn+r)/2,Ca+=i*(Un+n)/2,Xe+=i,qe(Gn=r,Un=n)}function tc(){bn.point=qe}function Wp(){bn.point=Kp}function Jp(){F1(O1,q1)}function Kp(r,n){bn.point=F1,qe(O1=Gn=r,q1=Un=n)}function F1(r,n){var e=r-Gn,t=n-Un,i=Xr(e*e+t*t);ka+=i*(Gn+r)/2,Ca+=i*(Un+n)/2,Xe+=i,i=Un*r-Gn*n,Qu+=i*(Gn+r),Zu+=i*(Un+n),Ot+=i*3,qe(Gn=r,Un=n)}function z1(r){this._context=r}z1.prototype={_radius:4.5,pointRadius:function(r){return this._radius=r,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(r,n){switch(this._point){case 0:{this._context.moveTo(r,n),this._point=1;break}case 1:{this._context.lineTo(r,n);break}default:{this._context.moveTo(r+this._radius,n),this._context.arc(r,n,this._radius,0,vn);break}}},result:Or};var rf=Se(),Uo,B1,G1,qt,Ft,ni={point:Or,lineStart:function(){ni.point=Qp},lineEnd:function(){Uo&&U1(B1,G1),ni.point=Or},polygonStart:function(){Uo=!0},polygonEnd:function(){Uo=null},result:function(){var r=+rf;return rf.reset(),r}};function Qp(r,n){ni.point=U1,B1=qt=r,G1=Ft=n}function U1(r,n){qt-=r,Ft-=n,rf.add(Xr(qt*qt+Ft*Ft)),qt=r,Ft=n}function H1(){this._string=[]}H1.prototype={_radius:4.5,_circle:ic(4.5),pointRadius:function(r){return(r=+r)!==this._radius&&(this._radius=r,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._string.push("Z"),this._point=NaN},point:function(r,n){switch(this._point){case 0:{this._string.push("M",r,",",n),this._point=1;break}case 1:{this._string.push("L",r,",",n);break}default:{this._circle==null&&(this._circle=ic(this._radius)),this._string.push("M",r,",",n,this._circle);break}}},result:function(){if(this._string.length){var r=this._string.join("");return this._string=[],r}else return null}};function ic(r){return"m0,"+r+"a"+r+","+r+" 0 1,1 0,"+-2*r+"a"+r+","+r+" 0 1,1 0,"+2*r+"z"}function Zp(r,n){var e=4.5,t,i;function a(o){return o&&(typeof e=="function"&&i.pointRadius(+e.apply(this,arguments)),sn(o,t(i))),i.result()}return a.area=function(o){return sn(o,t(Qn)),Qn.result()},a.measure=function(o){return sn(o,t(ni)),ni.result()},a.bounds=function(o){return sn(o,t(Ra)),Ra.result()},a.centroid=function(o){return sn(o,t(bn)),bn.result()},a.projection=function(o){return arguments.length?(t=o==null?(r=null,Ae):(r=o).stream,a):r},a.context=function(o){return arguments.length?(i=o==null?(n=null,new H1):new z1(n=o),typeof e!="function"&&i.pointRadius(e),a):n},a.pointRadius=function(o){return arguments.length?(e=typeof o=="function"?o:(i.pointRadius(+o),+o),a):e},a.projection(r).context(n)}function Y1(r,n,e,t){return function(i,a){var o=n(a),s=i.invert(t[0],t[1]),l=P1(),c=n(l),u=!1,v,f,h,g={point:p,lineStart:m,lineEnd:y,polygonStart:function(){g.point=E,g.lineStart=S,g.lineEnd=$,f=[],v=[]},polygonEnd:function(){g.point=p,g.lineStart=m,g.lineEnd=y,f=Bf(f);var x=k1(v,s);f.length?(u||(a.polygonStart(),u=!0),R1(f,nd,x,e,a)):x&&(u||(a.polygonStart(),u=!0),a.lineStart(),e(null,null,1,a),a.lineEnd()),u&&(a.polygonEnd(),u=!1),f=v=null},sphere:function(){a.polygonStart(),a.lineStart(),e(null,null,1,a),a.lineEnd(),a.polygonEnd()}};function p(x,I){var C=i(x,I);r(x=C[0],I=C[1])&&a.point(x,I)}function d(x,I){var C=i(x,I);o.point(C[0],C[1])}function m(){g.point=d,o.lineStart()}function y(){g.point=p,o.lineEnd()}function E(x,I){h.push([x,I]);var C=i(x,I);c.point(C[0],C[1])}function S(){c.lineStart(),h=[]}function $(){E(h[0][0],h[0][1]),c.lineEnd();var x=c.clean(),I=l.result(),C,L=I.length,O,A,k;if(h.pop(),v.push(h),h=null,!!L){if(x&1){if(A=I[0],(O=A.length-1)>0){for(u||(a.polygonStart(),u=!0),a.lineStart(),C=0;C<O;++C)a.point((k=A[C])[0],k[1]);a.lineEnd()}return}L>1&&x&2&&I.push(I.pop().concat(I.shift())),f.push(I.filter(rd))}}return g}}function rd(r){return r.length>1}function nd(r,n){return((r=r.x)[0]<0?r[1]-Yr-tr:Yr-r[1])-((n=n.x)[0]<0?n[1]-Yr-tr:Yr-n[1])}var ac=Y1(function(){return!0},ed,id,[-yr,-Yr]);function ed(r){var n=NaN,e=NaN,t=NaN,i;return{lineStart:function(){r.lineStart(),i=1},point:function(a,o){var s=a>0?yr:-yr,l=Pr(a-n);Pr(l-yr)<tr?(r.point(n,e=(e+o)/2>0?Yr:-Yr),r.point(t,e),r.lineEnd(),r.lineStart(),r.point(s,e),r.point(a,e),i=0):t!==s&&l>=yr&&(Pr(n-t)<tr&&(n-=t*tr),Pr(a-s)<tr&&(a-=s*tr),e=td(n,e,a,o),r.point(t,e),r.lineEnd(),r.lineStart(),r.point(s,e),i=0),r.point(n=a,e=o),t=s},lineEnd:function(){r.lineEnd(),n=e=NaN},clean:function(){return 2-i}}}function td(r,n,e,t){var i,a,o=Z(r-e);return Pr(o)>tr?gt((Z(n)*(a=nr(t))*Z(e)-Z(t)*(i=nr(n))*Z(r))/(i*a*o)):(n+t)/2}function id(r,n,e,t){var i;if(r==null)i=e*Yr,t.point(-yr,i),t.point(0,i),t.point(yr,i),t.point(yr,0),t.point(yr,-i),t.point(0,-i),t.point(-yr,-i),t.point(-yr,0),t.point(-yr,i);else if(Pr(r[0]-n[0])>tr){var a=r[0]<n[0]?yr:-yr;i=e*a/2,t.point(-a,i),t.point(0,i),t.point(a,i)}else t.point(n[0],n[1])}function ad(r,n){var e=nr(r),t=e>0,i=Pr(e)>tr;function a(u,v,f,h){x1(h,r,n,f,u,v)}function o(u,v){return nr(u)*nr(v)>e}function s(u){var v,f,h,g,p;return{lineStart:function(){g=h=!1,p=1},point:function(d,m){var y=[d,m],E,S=o(d,m),$=t?S?0:c(d,m):S?c(d+(d<0?yr:-yr),m):0;if(!v&&(g=h=S)&&u.lineStart(),S!==h&&(E=l(v,y),(!E||Qi(v,E)||Qi(y,E))&&(y[0]+=tr,y[1]+=tr,S=o(y[0],y[1]))),S!==h)p=0,S?(u.lineStart(),E=l(y,v),u.point(E[0],E[1])):(E=l(v,y),u.point(E[0],E[1]),u.lineEnd()),v=E;else if(i&&v&&t^S){var x;!($&f)&&(x=l(y,v,!0))&&(p=0,t?(u.lineStart(),u.point(x[0][0],x[0][1]),u.point(x[1][0],x[1][1]),u.lineEnd()):(u.point(x[1][0],x[1][1]),u.lineEnd(),u.lineStart(),u.point(x[0][0],x[0][1])))}S&&(!v||!Qi(v,y))&&u.point(y[0],y[1]),v=y,h=S,f=$},lineEnd:function(){h&&u.lineEnd(),v=null},clean:function(){return p|(g&&h)<<1}}}function l(u,v,f){var h=Oe(u),g=Oe(v),p=[1,0,0],d=et(h,g),m=$i(d,d),y=d[0],E=m-y*y;if(!E)return!f&&u;var S=e*m/E,$=-e*y/E,x=et(p,d),I=Mi(p,S),C=Mi(d,$);Fo(I,C);var L=x,O=$i(I,L),A=$i(L,L),k=O*O-A*($i(I,I)-1);if(!(k<0)){var U=Xr(k),_=Mi(L,(-O-U)/A);if(Fo(_,I),_=da(_),!f)return _;var w=u[0],b=v[0],P=u[1],D=v[1],rr;b<w&&(rr=w,w=b,b=rr);var hr=b-w,ur=Pr(hr-yr)<tr,gr=ur||hr<tr;if(!ur&&D<P&&(rr=P,P=D,D=rr),gr?ur?P+D>0^_[1]<(Pr(_[0]-w)<tr?P:D):P<=_[1]&&_[1]<=D:hr>yr^(w<=_[0]&&_[0]<=b)){var xr=Mi(L,(-O+U)/A);return Fo(xr,I),[_,da(xr)]}}}function c(u,v){var f=t?r:yr-r,h=0;return u<-f?h|=1:u>f&&(h|=2),v<-f?h|=4:v>f&&(h|=8),h}return Y1(o,s,a,t?[0,-r]:[-yr,r-yr])}function od(r){return{stream:oo(r)}}function oo(r){return function(n){var e=new nf;for(var t in r)e[t]=r[t];return e.stream=n,e}}function nf(){}nf.prototype={constructor:nf,point:function(r,n){this.stream.point(r,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function uo(r,n,e){var t=n[1][0]-n[0][0],i=n[1][1]-n[0][1],a=r.clipExtent&&r.clipExtent();r.scale(150).translate([0,0]),a!=null&&r.clipExtent(null),sn(e,r.stream(Ra));var o=Ra.result(),s=Math.min(t/(o[1][0]-o[0][0]),i/(o[1][1]-o[0][1])),l=+n[0][0]+(t-s*(o[1][0]+o[0][0]))/2,c=+n[0][1]+(i-s*(o[1][1]+o[0][1]))/2;return a!=null&&r.clipExtent(a),r.scale(s*150).translate([l,c])}function Uf(r,n,e){return uo(r,[[0,0],n],e)}var oc=16,ud=nr(30*ar);function uc(r,n){return+n?ld(r,n):fd(r)}function fd(r){return oo({point:function(n,e){n=r(n,e),this.stream.point(n[0],n[1])}})}function ld(r,n){function e(t,i,a,o,s,l,c,u,v,f,h,g,p,d){var m=c-t,y=u-i,E=m*m+y*y;if(E>4*n&&p--){var S=o+f,$=s+h,x=l+g,I=Xr(S*S+$*$+x*x),C=Sn(x/=I),L=Pr(Pr(x)-1)<tr||Pr(a-v)<tr?(a+v)/2:on($,S),O=r(L,C),A=O[0],k=O[1],U=A-t,_=k-i,w=y*U-m*_;(w*w/E>n||Pr((m*U+y*_)/E-.5)>.3||o*f+s*h+l*g<ud)&&(e(t,i,a,o,s,l,A,k,L,S/=I,$/=I,x,p,d),d.point(A,k),e(A,k,L,S,$,x,c,u,v,f,h,g,p,d))}}return function(t){var i,a,o,s,l,c,u,v,f,h,g,p,d={point:m,lineStart:y,lineEnd:S,polygonStart:function(){t.polygonStart(),d.lineStart=$},polygonEnd:function(){t.polygonEnd(),d.lineStart=y}};function m(C,L){C=r(C,L),t.point(C[0],C[1])}function y(){v=NaN,d.point=E,t.lineStart()}function E(C,L){var O=Oe([C,L]),A=r(C,L);e(v,f,u,h,g,p,v=A[0],f=A[1],u=C,h=O[0],g=O[1],p=O[2],oc,t),t.point(v,f)}function S(){d.point=m,t.lineEnd()}function $(){y(),d.point=x,d.lineEnd=I}function x(C,L){E(i=C,L),a=v,o=f,s=h,l=g,c=p,d.point=E}function I(){e(v,f,u,h,g,p,a,o,i,s,l,c,oc,t),d.lineEnd=S,S()}return d}}var sd=oo({point:function(r,n){this.stream.point(r*ar,n*ar)}});function Q(r){return un(function(){return r})()}function un(r){var n,e=150,t=480,i=250,a,o,s=0,l=0,c=0,u=0,v=0,f,h,g=null,p=ac,d=null,m,y,E,S=Ae,$=.5,x=uc(A,$),I,C;function L(_){return _=h(_[0]*ar,_[1]*ar),[_[0]*e+a,o-_[1]*e]}function O(_){return _=h.invert((_[0]-a)/e,(o-_[1])/e),_&&[_[0]*Nr,_[1]*Nr]}function A(_,w){return _=n(_,w),[_[0]*e+a,o-_[1]*e]}L.stream=function(_){return I&&C===_?I:I=sd(p(f,x(S(C=_))))},L.clipAngle=function(_){return arguments.length?(p=+_?ad(g=_*ar,6*ar):(g=null,ac),U()):g*Nr},L.clipExtent=function(_){return arguments.length?(S=_==null?(d=m=y=E=null,Ae):Gf(d=+_[0][0],m=+_[0][1],y=+_[1][0],E=+_[1][1]),U()):d==null?null:[[d,m],[y,E]]},L.scale=function(_){return arguments.length?(e=+_,k()):e},L.translate=function(_){return arguments.length?(t=+_[0],i=+_[1],k()):[t,i]},L.center=function(_){return arguments.length?(s=_[0]%360*ar,l=_[1]%360*ar,k()):[s*Nr,l*Nr]},L.rotate=function(_){return arguments.length?(c=_[0]%360*ar,u=_[1]%360*ar,v=_.length>2?_[2]%360*ar:0,k()):[c*Nr,u*Nr,v*Nr]},L.precision=function(_){return arguments.length?(x=uc(A,$=_*_),U()):Xr($)},L.fitExtent=function(_,w){return uo(L,_,w)},L.fitSize=function(_,w){return Uf(L,_,w)};function k(){h=M1(f=Ff(c,u,v),n);var _=n(s,l);return a=t-_[0]*e,o=i+_[1]*e,U()}function U(){return I=C=null,L}return function(){return n=r.apply(this,arguments),L.invert=n.invert&&O,k()}}function Hf(r){var n=0,e=yr/3,t=un(r),i=t(n,e);return i.parallels=function(a){return arguments.length?t(n=a[0]*ar,e=a[1]*ar):[n*Nr,e*Nr]},i}function cd(r){var n=nr(r);function e(t,i){return[t*n,Z(i)/n]}return e.invert=function(t,i){return[t/n,Sn(i*n)]},e}function V1(r,n){var e=Z(r),t=(e+Z(n))/2;if(Pr(t)<tr)return cd(r);var i=1+e*(2*t-e),a=Xr(i)/t;function o(s,l){var c=Xr(i-2*t*Z(l))/t;return[c*Z(s*=t),a-c*nr(s)]}return o.invert=function(s,l){var c=a-l;return[on(s,Pr(c))/t*Zt(c),Sn((i-(s*s+c*c)*t*t)/(2*t))]},o}function La(){return Hf(V1).scale(155.424).center([0,33.6442])}function X1(){return La().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function vd(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function hd(){var r,n,e=X1(),t,i=La().rotate([154,0]).center([-2,58.5]).parallels([55,65]),a,o=La().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s,l,c={point:function(f,h){l=[f,h]}};function u(f){var h=f[0],g=f[1];return l=null,t.point(h,g),l||(a.point(h,g),l)||(s.point(h,g),l)}u.invert=function(f){var h=e.scale(),g=e.translate(),p=(f[0]-g[0])/h,d=(f[1]-g[1])/h;return(d>=.12&&d<.234&&p>=-.425&&p<-.214?i:d>=.166&&d<.234&&p>=-.214&&p<-.115?o:e).invert(f)},u.stream=function(f){return r&&n===f?r:r=vd([e.stream(n=f),i.stream(f),o.stream(f)])},u.precision=function(f){return arguments.length?(e.precision(f),i.precision(f),o.precision(f),v()):e.precision()},u.scale=function(f){return arguments.length?(e.scale(f),i.scale(f*.35),o.scale(f),u.translate(e.translate())):e.scale()},u.translate=function(f){if(!arguments.length)return e.translate();var h=e.scale(),g=+f[0],p=+f[1];return t=e.translate(f).clipExtent([[g-.455*h,p-.238*h],[g+.455*h,p+.238*h]]).stream(c),a=i.translate([g-.307*h,p+.201*h]).clipExtent([[g-.425*h+tr,p+.12*h+tr],[g-.214*h-tr,p+.234*h-tr]]).stream(c),s=o.translate([g-.205*h,p+.212*h]).clipExtent([[g-.214*h+tr,p+.166*h+tr],[g-.115*h-tr,p+.234*h-tr]]).stream(c),v()},u.fitExtent=function(f,h){return uo(u,f,h)},u.fitSize=function(f,h){return Uf(u,f,h)};function v(){return r=n=null,u}return u.scale(1070)}function j1(r){return function(n,e){var t=nr(n),i=nr(e),a=r(t*i);return[a*i*Z(n),a*Z(e)]}}function ci(r){return function(n,e){var t=Xr(n*n+e*e),i=r(t),a=Z(i),o=nr(i);return[on(n*a,t*o),Sn(t&&e*a/t)]}}var Qe=j1(function(r){return Xr(2/(1+r))});Qe.invert=ci(function(r){return 2*Sn(r/2)});function gd(){return Q(Qe).scale(124.75).clipAngle(180-.001)}var ye=j1(function(r){return(r=h1(r))&&r/Z(r)});ye.invert=ci(function(r){return r});function pd(){return Q(ye).scale(79.4188).clipAngle(180-.001)}function vi(r,n){return[r,va(Of((Yr+n)/2))]}vi.invert=function(r,n){return[r,2*gt(v1(n))-Yr]};function dd(){return W1(vi).scale(961/vn)}function W1(r){var n=Q(r),e=n.center,t=n.scale,i=n.translate,a=n.clipExtent,o=null,s,l,c;n.scale=function(v){return arguments.length?(t(v),u()):t()},n.translate=function(v){return arguments.length?(i(v),u()):i()},n.center=function(v){return arguments.length?(e(v),u()):e()},n.clipExtent=function(v){return arguments.length?(v==null?o=s=l=c=null:(o=+v[0][0],s=+v[0][1],l=+v[1][0],c=+v[1][1]),u()):o==null?null:[[o,s],[l,c]]};function u(){var v=yr*t(),f=n(tt(n.rotate()).invert([0,0]));return a(o==null?[[f[0]-v,f[1]-v],[f[0]+v,f[1]+v]]:r===vi?[[Math.max(f[0]-v,o),s],[Math.min(f[0]+v,l),c]]:[[o,Math.max(f[1]-v,s)],[l,Math.min(f[1]+v,c)]])}return u()}function Pi(r){return Of((Yr+r)/2)}function J1(r,n){var e=nr(r),t=r===n?Z(r):va(e/nr(n))/va(Pi(n)/Pi(r)),i=e*qo(Pi(r),t)/t;if(!t)return vi;function a(o,s){i>0?s<-Yr+tr&&(s=-Yr+tr):s>Yr-tr&&(s=Yr-tr);var l=i/qo(Pi(s),t);return[l*Z(t*o),i-l*nr(t*o)]}return a.invert=function(o,s){var l=i-s,c=Zt(t)*Xr(o*o+l*l);return[on(o,Pr(l))/t*Zt(l),2*gt(qo(i/c,1/t))-Yr]},a}function md(){return Hf(J1).scale(109.5).parallels([30,30])}function ei(r,n){return[r,n]}ei.invert=ei;function K1(){return Q(ei).scale(152.63)}function Q1(r,n){var e=nr(r),t=r===n?Z(r):(e-nr(n))/(n-r),i=e/t+r;if(Pr(t)<tr)return ei;function a(o,s){var l=i-s,c=t*o;return[l*Z(c),i-l*nr(c)]}return a.invert=function(o,s){var l=i-s;return[on(o,Pr(l))/t*Zt(l),i-Zt(t)*Xr(o*o+l*l)]},a}function yd(){return Hf(Q1).scale(131.154).center([0,13.9389])}function ti(r,n){var e=nr(n),t=nr(r)*e;return[e*Z(r)/t,Z(n)/t]}ti.invert=ci(gt);function Yf(){return Q(ti).scale(144.049).clipAngle(60)}function Ri(r,n,e,t){return r===1&&n===1&&e===0&&t===0?Ae:oo({point:function(i,a){this.stream.point(i*r+e,a*n+t)}})}function wd(){var r=1,n=0,e=0,t=1,i=1,a=Ae,o=null,s,l,c,u=Ae,v,f,h;function g(){return v=f=null,h}return h={stream:function(p){return v&&f===p?v:v=a(u(f=p))},clipExtent:function(p){return arguments.length?(u=p==null?(o=s=l=c=null,Ae):Gf(o=+p[0][0],s=+p[0][1],l=+p[1][0],c=+p[1][1]),g()):o==null?null:[[o,s],[l,c]]},scale:function(p){return arguments.length?(a=Ri((r=+p)*t,r*i,n,e),g()):r},translate:function(p){return arguments.length?(a=Ri(r*t,r*i,n=+p[0],e=+p[1]),g()):[n,e]},reflectX:function(p){return arguments.length?(a=Ri(r*(t=p?-1:1),r*i,n,e),g()):t<0},reflectY:function(p){return arguments.length?(a=Ri(r*t,r*(i=p?-1:1),n,e),g()):i<0},fitExtent:function(p,d){return uo(h,p,d)},fitSize:function(p,d){return Uf(h,p,d)}}}function Vf(r,n){return[nr(n)*Z(r),Z(n)]}Vf.invert=ci(Sn);function Z1(){return Q(Vf).scale(249.5).clipAngle(90+tr)}function Xf(r,n){var e=nr(n),t=1+nr(r)*e;return[e*Z(r)/t,Z(n)/t]}Xf.invert=ci(function(r){return 2*gt(r)});function Ed(){return Q(Xf).scale(250).clipAngle(142)}function jf(r,n){return[va(Of((Yr+n)/2)),-r]}jf.invert=function(r,n){return[-n,2*gt(v1(r))-Yr]};function Sd(){var r=W1(jf),n=r.center,e=r.rotate;return r.center=function(t){return arguments.length?n([-t[1],t[0]]):(t=n(),[t[1],-t[0]])},r.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):(t=e(),[t[0],t[1],t[2]-90])},e([0,0,90]).scale(159.155)}var _d=Object.freeze(Object.defineProperty({__proto__:null,geoArea:_p,geoBounds:S1,geoCentroid:pt,geoCircle:b1,geoClipExtent:Np,geoContains:Bp,geoDistance:Jt,geoGraticule:I1,geoGraticule10:Gp,geoInterpolate:xa,geoLength:C1,geoPath:Zp,geoAlbers:X1,geoAlbersUsa:hd,geoAzimuthalEqualArea:gd,geoAzimuthalEqualAreaRaw:Qe,geoAzimuthalEquidistant:pd,geoAzimuthalEquidistantRaw:ye,geoConicConformal:md,geoConicConformalRaw:J1,geoConicEqualArea:La,geoConicEqualAreaRaw:V1,geoConicEquidistant:yd,geoConicEquidistantRaw:Q1,geoEquirectangular:K1,geoEquirectangularRaw:ei,geoGnomonic:Yf,geoGnomonicRaw:ti,geoIdentity:wd,geoProjection:Q,geoProjectionMutator:un,geoMercator:dd,geoMercatorRaw:vi,geoOrthographic:Z1,geoOrthographicRaw:Vf,geoStereographic:Ed,geoStereographicRaw:Xf,geoTransverseMercator:Sd,geoTransverseMercatorRaw:jf,geoRotation:tt,geoStream:sn,geoTransform:od},Symbol.toStringTag,{value:"Module"})),hi=oe(_d),Y=Math.abs,qr=Math.atan,mr=Math.atan2,F=Math.cos,Xn=Math.exp,ot=Math.floor,Vr=Math.log,ii=Math.max,Nn=Math.min,ef=Math.pow,Ia=Math.round,dr=Math.sign||function(r){return r>0?1:r<0?-1:0},z=Math.sin,lr=Math.tan,X=1e-6,dt=1e-12,B=Math.PI,j=B/2,cn=B/4,Mn=Math.SQRT1_2,Tr=H(2),Rn=H(B),Cn=B*2,Lr=180/B,wr=B/180;function Td(r){return r?r/Math.sin(r):1}function er(r){return r>1?j:r<-1?-j:Math.asin(r)}function Jr(r){return r>1?0:r<-1?B:Math.acos(r)}function H(r){return r>0?Math.sqrt(r):0}function $d(r){return r=Xn(2*r),(r-1)/(r+1)}function Wf(r){return(Xn(r)-Xn(-r))/2}function rv(r){return(Xn(r)+Xn(-r))/2}function Md(r){return Vr(r+H(r*r+1))}function xd(r){return Vr(r+H(r*r-1))}function nv(r){var n=lr(r/2),e=2*Vr(F(r/2))/(n*n);function t(i,a){var o=F(i),s=F(a),l=z(a),c=s*o,u=-((1-c?Vr((1+c)/2)/(1-c):-.5)+e/(1+c));return[u*s*z(i),u*l]}return t.invert=function(i,a){var o=H(i*i+a*a),s=-r/2,l=50,c;if(!o)return[0,0];do{var u=s/2,v=F(u),f=z(u),h=lr(u),g=Vr(1/v);s-=c=(2/h*g-e*h-o)/(-g/(f*f)+1-e/(2*v*v))}while(Y(c)>X&&--l>0);var p=z(s);return[mr(i*p,o*F(s)),er(a*p/o)]},t}function bd(){var r=j,n=un(nv),e=n(r);return e.radius=function(t){return arguments.length?n(r=t*wr):r*Lr},e.scale(179.976).clipAngle(147)}function fo(r,n){var e=F(n),t=Td(Jr(e*F(r/=2)));return[2*e*z(r)*t,z(n)*t]}fo.invert=function(r,n){if(!(r*r+4*n*n>B*B+X)){var e=r,t=n,i=25;do{var a=z(e),o=z(e/2),s=F(e/2),l=z(t),c=F(t),u=z(2*t),v=l*l,f=c*c,h=o*o,g=1-f*s*s,p=g?Jr(c*s)*H(d=1/g):d=0,d,m=2*p*c*o-r,y=p*l-n,E=d*(f*h+p*c*s*v),S=d*(.5*a*u-p*2*l*o),$=d*.25*(u*o-p*l*f*a),x=d*(v*s+p*h*c),I=S*$-x*E;if(!I)break;var C=(y*S-m*x)/I,L=(m*$-y*E)/I;e-=C,t-=L}while((Y(C)>X||Y(L)>X)&&--i>0);return[e,t]}};function Pd(){return Q(fo).scale(152.63)}function ev(r){var n=z(r),e=F(r),t=r>=0?1:-1,i=lr(t*r),a=(1+n-e)/2;function o(s,l){var c=F(l),u=F(s/=2);return[(1+c)*z(s),(t*l>-mr(u,i)-.001?0:-t*10)+a+z(l)*e-(1+c)*n*u]}return o.invert=function(s,l){var c=0,u=0,v=50;do{var f=F(c),h=z(c),g=F(u),p=z(u),d=1+g,m=d*h-s,y=a+p*e-d*n*f-l,E=d*f/2,S=-h*p,$=n*d*h/2,x=e*g+n*f*p,I=S*$-x*E,C=(y*S-m*x)/I/2,L=(m*$-y*E)/I;c-=C,u-=L}while((Y(C)>X||Y(L)>X)&&--v>0);return t*u>-mr(F(c),i)-.001?[c*2,u]:null},o}function Rd(){var r=20*wr,n=r>=0?1:-1,e=lr(n*r),t=un(ev),i=t(r),a=i.stream;return i.parallel=function(o){return arguments.length?(e=lr((n=(r=o*wr)>=0?1:-1)*r),t(r)):r*Lr},i.stream=function(o){var s=i.rotate(),l=a(o),c=(i.rotate([0,0]),a(o));return i.rotate(s),l.sphere=function(){c.polygonStart(),c.lineStart();for(var u=n*-180;n*u<180;u+=n*90)c.point(u,n*90);for(;n*(u-=r)>=-180;)c.point(u,n*-mr(F(u*wr/2),e)*Lr);c.lineEnd(),c.polygonEnd()},l},i.scale(218.695).center([0,28.0974])}function lo(r,n){var e=lr(n/2),t=H(1-e*e),i=1+t*F(r/=2),a=z(r)*t/i,o=e/i,s=a*a,l=o*o;return[4/3*a*(3+s-3*l),4/3*o*(3+3*s-l)]}lo.invert=function(r,n){if(r*=3/8,n*=3/8,!r&&Y(n)>1)return null;var e=r*r,t=n*n,i=1+e+t,a=H((i-H(i*i-4*n*n))/2),o=er(a)/3,s=a?xd(Y(n/a))/3:Md(Y(r))/3,l=F(o),c=rv(s),u=c*c-l*l;return[dr(r)*2*mr(Wf(s)*l,.25-u),dr(n)*2*mr(c*z(o),.25+u)]};function kd(){return Q(lo).scale(66.1603)}var Ho=H(8),Cd=Vr(1+Tr);function Jf(r,n){var e=Y(n);return e<cn?[r,Vr(lr(cn+n/2))]:[r*F(e)*(2*Tr-1/z(e)),dr(n)*(2*Tr*(e-cn)-Vr(lr(e/2)))]}Jf.invert=function(r,n){if((a=Y(n))<Cd)return[r,2*qr(Xn(n))-j];var e=cn,t=25,i,a;do{var o=F(e/2),s=lr(e/2);e-=i=(Ho*(e-cn)-Vr(s)-a)/(Ho-o*o/(2*s))}while(Y(i)>dt&&--t>0);return[r/(F(e)*(Ho-1/z(e))),dr(n)*e]};function Ld(){return Q(Jf).scale(112.314)}function tv(r){var n=2*B/r;function e(t,i){var a=ye(t,i);if(Y(t)>j){var o=mr(a[1],a[0]),s=H(a[0]*a[0]+a[1]*a[1]),l=n*Ia((o-j)/n)+j,c=mr(z(o-=l),2-F(o));o=l+er(B/s*z(c))-c,a[0]=s*F(o),a[1]=s*z(o)}return a}return e.invert=function(t,i){var a=H(t*t+i*i);if(a>j){var o=mr(i,t),s=n*Ia((o-j)/n)+j,l=o>s?-1:1,c=a*F(s-o),u=1/lr(l*Jr((c-B)/H(B*(B-2*c)+a*a)));o=s+2*qr((u+l*H(u*u-3))/3),t=a*F(o),i=a*z(o)}return ye.invert(t,i)},e}function Id(){var r=5,n=un(tv),e=n(r),t=e.stream,i=.01,a=-F(i*wr),o=z(i*wr);return e.lobes=function(s){return arguments.length?n(r=+s):r},e.stream=function(s){var l=e.rotate(),c=t(s),u=(e.rotate([0,0]),t(s));return e.rotate(l),c.sphere=function(){u.polygonStart(),u.lineStart();for(var v=0,f=360/r,h=2*B/r,g=90-180/r,p=j;v<r;++v,g-=f,p-=h)u.point(mr(o*F(p),a)*Lr,er(o*z(p))*Lr),g<-90?(u.point(-90,-180-g-i),u.point(-90,-180-g+i)):(u.point(90,g+i),u.point(90,g-i));u.lineEnd(),u.polygonEnd()},c},e.scale(87.8076).center([0,17.1875]).clipAngle(180-.001)}function iv(r,n){var e=r*z(n),t=30,i;do n-=i=(n+z(n)-e)/(1+F(n));while(Y(i)>X&&--t>0);return n/2}function Kf(r,n,e){function t(i,a){return[r*i*F(a=iv(e,a)),n*z(a)]}return t.invert=function(i,a){return a=er(a/n),[i/(r*F(a)),er((2*a+z(2*a))/e)]},t}var _e=Kf(Tr/j,Tr,B);function Nd(){return Q(_e).scale(169.529)}var Na=2.00276,av=1.11072;function so(r,n){var e=iv(B,n);return[Na*r/(1/F(n)+av/F(e)),(n+Tr*z(e))/Na]}so.invert=function(r,n){var e=Na*n,t=n<0?-cn:cn,i=25,a,o;do o=e-Tr*z(t),t-=a=(z(2*t)+2*t-B*z(o))/(2*F(2*t)+2+B*F(o)*Tr*F(t));while(Y(a)>X&&--i>0);return o=e-Tr*z(t),[r*(1/F(o)+av/F(t))/Na,o]};function Ad(){return Q(so).scale(160.857)}function mt(r){var n=0,e=un(r),t=e(n);return t.parallel=function(i){return arguments.length?e(n=i*wr):n*Lr},t}function ue(r,n){return[r*F(n),n]}ue.invert=function(r,n){return[r/F(n),n]};function Dd(){return Q(ue).scale(152.63)}function ov(r){if(!r)return ue;var n=1/lr(r);function e(t,i){var a=n+r-i,o=a&&t*F(i)/a;return[a*z(o),n-a*F(o)]}return e.invert=function(t,i){var a=H(t*t+(i=n-i)*i),o=n+r-a;return[a/F(o)*mr(t,i),o]},e}function Od(){return mt(ov).scale(123.082).center([0,26.1441]).parallel(45)}function uv(r){function n(e,t){var i=j-t,a=i&&e*r*z(i)/i;return[i*z(a)/r,j-i*F(a)]}return n.invert=function(e,t){var i=e*r,a=j-t,o=H(i*i+a*a),s=mr(i,a);return[(o?o/z(o):1)*s/r,j-o]},n}function qd(){var r=.5,n=un(uv),e=n(r);return e.fraction=function(t){return arguments.length?n(r=+t):r},e.scale(158.837)}var fv=Kf(1,4/B,B);function Fd(){return Q(fv).scale(152.63)}function fc(r,n,e,t,i,a){var o=F(a),s;if(Y(r)>1||Y(a)>1)s=Jr(e*i+n*t*o);else{var l=z(r/2),c=z(a/2);s=2*er(H(l*l+n*t*c*c))}return Y(s)>X?[s,mr(t*z(a),n*i-e*t*o)]:[0,0]}function Yo(r,n,e){return Jr((r*r+n*n-e*e)/(2*r*n))}function zd(r){return r-2*B*ot((r+B)/(2*B))}function lv(r,n,e){for(var t=[[r[0],r[1],z(r[1]),F(r[1])],[n[0],n[1],z(n[1]),F(n[1])],[e[0],e[1],z(e[1]),F(e[1])]],i=t[2],a,o=0;o<3;++o,i=a)a=t[o],i.v=fc(a[1]-i[1],i[3],i[2],a[3],a[2],a[0]-i[0]),i.point=[0,0];var s=Yo(t[0].v[0],t[2].v[0],t[1].v[0]),l=Yo(t[0].v[0],t[1].v[0],t[2].v[0]),c=B-s;t[2].point[1]=0,t[0].point[0]=-(t[1].point[0]=t[0].v[0]/2);var u=[t[2].point[0]=t[0].point[0]+t[2].v[0]*F(s),2*(t[0].point[1]=t[1].point[1]=t[2].v[0]*z(s))];function v(f,h){var g=z(h),p=F(h),d=new Array(3),m;for(m=0;m<3;++m){var y=t[m];if(d[m]=fc(h-y[1],y[3],y[2],p,g,f-y[0]),!d[m][0])return y.point;d[m][1]=zd(d[m][1]-y.v[1])}var E=u.slice();for(m=0;m<3;++m){var S=m==2?0:m+1,$=Yo(t[m].v[0],d[m][0],d[S][0]);d[m][1]<0&&($=-$),m?m==1?($=l-$,E[0]-=d[m][0]*F($),E[1]-=d[m][0]*z($)):($=c-$,E[0]+=d[m][0]*F($),E[1]+=d[m][0]*z($)):(E[0]+=d[m][0]*F($),E[1]-=d[m][0]*z($))}return E[0]/=3,E[1]/=3,E}return v}function Vo(r){return r[0]*=wr,r[1]*=wr,r}function Bd(){return sv([0,22],[45,22],[22.5,-22]).scale(380).center([22.5,2])}function sv(r,n,e){var t=pt({type:"MultiPoint",coordinates:[r,n,e]}),i=[-t[0],-t[1]],a=tt(i),o=Q(lv(Vo(a(r)),Vo(a(n)),Vo(a(e)))).rotate(i),s=o.center;return delete o.rotate,o.center=function(l){return arguments.length?s(a(l)):a.invert(s())},o.clipAngle(90)}function Hn(r,n){var e=H(1-z(n));return[2/Rn*r*e,Rn*(1-e)]}Hn.invert=function(r,n){var e=(e=n/Rn-1)*e;return[e>0?r*H(B/e)/2:0,er(1-e)]};function Gd(){return Q(Hn).scale(95.6464).center([0,30])}function cv(r){var n=lr(r);function e(t,i){return[t,(t?t/z(t):1)*(z(i)*F(t)-n*F(i))]}return e.invert=n?function(t,i){t&&(i*=z(t)/t);var a=F(t);return[t,2*mr(H(a*a+n*n-i*i)-a,n-i)]}:function(t,i){return[t,er(t?i*lr(t)/t:i)]},e}function Ud(){return mt(cv).scale(249.828).clipAngle(90)}var Aa=H(3);function Qf(r,n){return[Aa*r*(2*F(2*n/3)-1)/Rn,Aa*Rn*z(n/3)]}Qf.invert=function(r,n){var e=3*er(n/(Aa*Rn));return[Rn*r/(Aa*(2*F(2*e/3)-1)),e]};function Hd(){return Q(Qf).scale(156.19)}function Zf(r){var n=F(r);function e(t,i){return[t*n,z(i)/n]}return e.invert=function(t,i){return[t/n,er(i*n)]},e}function Yd(){return mt(Zf).parallel(38.58).scale(195.044)}function vv(r){var n=F(r);function e(t,i){return[t*n,(1+n)*lr(i/2)]}return e.invert=function(t,i){return[t/n,qr(i/(1+n))*2]},e}function Vd(){return mt(vv).scale(124.75)}function rl(r,n){var e=H(8/(3*B));return[e*r*(1-Y(n)/B),e*n]}rl.invert=function(r,n){var e=H(8/(3*B)),t=n/e;return[r/(e*(1-Y(t)/B)),t]};function Xd(){return Q(rl).scale(165.664)}function nl(r,n){var e=H(4-3*z(Y(n)));return[2/H(6*B)*r*e,dr(n)*H(2*B/3)*(2-e)]}nl.invert=function(r,n){var e=2-Y(n)/H(2*B/3);return[r*H(6*B)/(2*e),dr(n)*er((4-e*e)/3)]};function jd(){return Q(nl).scale(165.664)}function el(r,n){var e=H(B*(4+B));return[2/e*r*(1+H(1-4*n*n/(B*B))),4/e*n]}el.invert=function(r,n){var e=H(B*(4+B))/2;return[r*e/(1+H(1-n*n*(4+B)/(4*B))),n*e/2]};function Wd(){return Q(el).scale(180.739)}function tl(r,n){var e=(2+j)*z(n);n/=2;for(var t=0,i=1/0;t<10&&Y(i)>X;t++){var a=F(n);n-=i=(n+z(n)*(a+2)-e)/(2*a*(1+a))}return[2/H(B*(4+B))*r*(1+F(n)),2*H(B/(4+B))*z(n)]}tl.invert=function(r,n){var e=n*H((4+B)/B)/2,t=er(e),i=F(t);return[r/(2/H(B*(4+B))*(1+i)),er((t+e*(i+2))/(2+j))]};function Jd(){return Q(tl).scale(180.739)}function il(r,n){return[r*(1+F(n))/H(2+B),2*n/H(2+B)]}il.invert=function(r,n){var e=H(2+B),t=n*e/2;return[e*r/(1+F(t)),t]};function Kd(){return Q(il).scale(173.044)}function al(r,n){for(var e=(1+j)*z(n),t=0,i=1/0;t<10&&Y(i)>X;t++)n-=i=(n+z(n)-e)/(1+F(n));return e=H(2+B),[r*(1+F(n))/e,2*n/e]}al.invert=function(r,n){var e=1+j,t=H(e/2);return[r*2*t/(1+F(n*=t)),er((n+z(n))/e)]};function Qd(){return Q(al).scale(173.044)}var Da=3+2*Tr;function ol(r,n){var e=z(r/=2),t=F(r),i=H(F(n)),a=F(n/=2),o=z(n)/(a+Tr*t*i),s=H(2/(1+o*o)),l=H((Tr*a+(t+e)*i)/(Tr*a+(t-e)*i));return[Da*(s*(l-1/l)-2*Vr(l)),Da*(s*o*(l+1/l)-2*qr(o))]}ol.invert=function(r,n){if(!(a=lo.invert(r/1.2,n*1.065)))return null;var e=a[0],t=a[1],i=20,a;r/=Da,n/=Da;do{var o=e/2,s=t/2,l=z(o),c=F(o),u=z(s),v=F(s),f=F(t),h=H(f),g=u/(v+Tr*c*h),p=g*g,d=H(2/(1+p)),m=Tr*v+(c+l)*h,y=Tr*v+(c-l)*h,E=m/y,S=H(E),$=S-1/S,x=S+1/S,I=d*$-2*Vr(S)-r,C=d*g*x-2*qr(g)-n,L=u&&Mn*h*l*p/u,O=(Tr*c*v+h)/(2*(v+Tr*c*h)*(v+Tr*c*h)*h),A=-.5*g*d*d*d,k=A*L,U=A*O,_=(_=2*v+Tr*h*(c-l))*_*S,w=(Tr*c*v*h+f)/_,b=-(Tr*l*u)/(h*_),P=$*k-2*w/S+d*(w+w/E),D=$*U-2*b/S+d*(b+b/E),rr=g*x*k-2*L/(1+p)+d*x*L+d*g*(w-w/E),hr=g*x*U-2*O/(1+p)+d*x*O+d*g*(b-b/E),ur=D*rr-hr*P;if(!ur)break;var gr=(C*D-I*hr)/ur,xr=(I*rr-C*P)/ur;e-=gr,t=ii(-j,Nn(j,t-xr))}while((Y(gr)>X||Y(xr)>X)&&--i>0);return Y(Y(t)-j)<X?[0,t]:i&&[e,t]};function Zd(){return Q(ol).scale(62.5271)}var Oa=F(35*wr);function ul(r,n){var e=lr(n/2);return[r*Oa*H(1-e*e),(1+Oa)*e]}ul.invert=function(r,n){var e=n/(1+Oa);return[r&&r/(Oa*H(1-e*e)),2*qr(e)]};function r2(){return Q(ul).scale(137.152)}function fl(r,n){var e=n/2,t=F(e);return[2*r/Rn*F(n)*t*t,Rn*lr(e)]}fl.invert=function(r,n){var e=qr(n/Rn),t=F(e),i=2*e;return[r*Rn/2/(F(i)*t*t),i]};function n2(){return Q(fl).scale(135.264)}function lc(r){return[r[0]/2,er(lr(r[1]/2*wr))*Lr]}function sc(r){return[r[0]*2,2*qr(z(r[1]*wr))*Lr]}function e2(r){r==null&&(r=Z1);var n=r(),e=K1().scale(Lr).precision(0).clipAngle(null).translate([0,0]);function t(a){return n(lc(a))}n.invert&&(t.invert=function(a){return sc(n.invert(a))}),t.stream=function(a){var o=n.stream(a),s=e.stream({point:function(l,c){o.point(l/2,er(lr(-c/2*wr))*Lr)},lineStart:function(){o.lineStart()},lineEnd:function(){o.lineEnd()},polygonStart:function(){o.polygonStart()},polygonEnd:function(){o.polygonEnd()}});return s.sphere=o.sphere,s};function i(a){t[a]=function(o){return arguments.length?(n[a](o),t):n[a]()}}return t.rotate=function(a){return arguments.length?(e.rotate(a),t):e.rotate()},t.center=function(a){return arguments.length?(n.center(lc(a)),t):sc(n.center())},i("clipAngle"),i("clipExtent"),i("scale"),i("translate"),i("precision"),t.scale(249.5)}function hv(r,n){var e=2*B/n,t=r*r;function i(a,o){var s=ye(a,o),l=s[0],c=s[1],u=l*l+c*c;if(u>t){var v=H(u),f=mr(c,l),h=e*Ia(f/e),g=f-h,p=r*F(g),d=(r*z(g)-g*z(p))/(j-p),m=cc(g,d),y=(B-r)/ki(m,p,B);l=v;var E=50,S;do l-=S=(r+ki(m,p,l)*y-v)/(m(l)*y);while(Y(S)>X&&--E>0);c=g*z(l),l<j&&(c-=d*(l-j));var $=z(h),x=F(h);s[0]=l*x-c*$,s[1]=l*$+c*x}return s}return i.invert=function(a,o){var s=a*a+o*o;if(s>t){var l=H(s),c=mr(o,a),u=e*Ia(c/e),v=c-u;a=l*F(v),o=l*z(v);for(var f=a-j,h=z(a),g=o/h,p=a<j?1/0:0,d=10;;){var m=r*z(g),y=r*F(g),E=z(y),S=j-y,$=(m-g*E)/S,x=cc(g,$);if(Y(p)<dt||!--d)break;g-=p=(g*h-$*f-o)/(h-f*2*(S*(y+g*m*F(y)-E)-m*(m-g*E))/(S*S))}l=r+ki(x,y,a)*(B-r)/ki(x,y,B),c=u+g,a=l*F(c),o=l*z(c)}return ye.invert(a,o)},i}function cc(r,n){return function(e){var t=r*F(e);return e<j&&(t-=n),H(1+t*t)}}function ki(r,n,e){for(var t=50,i=(e-n)/t,a=r(n)+r(e),o=1,s=n;o<t;++o)a+=2*r(s+=i);return a*.5*i}function t2(){var r=6,n=30*wr,e=F(n),t=z(n),i=un(hv),a=i(n,r),o=a.stream,s=.01,l=-F(s*wr),c=z(s*wr);return a.radius=function(u){return arguments.length?(e=F(n=u*wr),t=z(n),i(n,r)):n*Lr},a.lobes=function(u){return arguments.length?i(n,r=+u):r},a.stream=function(u){var v=a.rotate(),f=o(u),h=(a.rotate([0,0]),o(u));return a.rotate(v),f.sphere=function(){h.polygonStart(),h.lineStart();for(var g=0,p=2*B/r,d=0;g<r;++g,d-=p)h.point(mr(c*F(d),l)*Lr,er(c*z(d))*Lr),h.point(mr(t*F(d-p/2),e)*Lr,er(t*z(d-p/2))*Lr);h.lineEnd(),h.polygonEnd()},f},a.rotate([90,-40]).scale(91.7095).clipAngle(180-.001)}function co(r,n,e,t,i,a,o,s){arguments.length<8&&(s=0);function l(c,u){if(!u)return[r*c/B,0];var v=u*u,f=r+v*(n+v*(e+v*t)),h=u*(i-1+v*(a-s+v*o)),g=(f*f+h*h)/(2*h),p=c*er(f/g)/B;return[g*z(p),u*(1+v*s)+g*(1-F(p))]}return l.invert=function(c,u){var v=B*c/r,f=u,h,g,p=50;do{var d=f*f,m=r+d*(n+d*(e+d*t)),y=f*(i-1+d*(a-s+d*o)),E=m*m+y*y,S=2*y,$=E/S,x=$*$,I=er(m/$)/B,C=v*I,L=m*m,O=(2*n+d*(4*e+d*6*t))*f,A=i+d*(3*a+d*5*o),k=2*(m*O+y*(A-1)),U=2*(A-1),_=(k*S-E*U)/(S*S),w=F(C),b=z(C),P=$*w,D=$*b,rr=v/B*(1/H(1-L/x))*(O*$-m*_)/x,hr=D-c,ur=f*(1+d*s)+$-P-u,gr=_*b+P*rr,xr=P*I,Ur=1+_-(_*w-D*rr),jr=D*I,$e=gr*jr-Ur*xr;if(!$e)break;v-=h=(ur*gr-hr*Ur)/$e,f-=g=(hr*jr-ur*xr)/$e}while((Y(h)>X||Y(g)>X)&&--p>0);return[v,f]},l}var gv=co(2.8284,-1.6988,.75432,-.18071,1.76003,-.38914,.042555);function i2(){return Q(gv).scale(149.995)}var pv=co(2.583819,-.835827,.170354,-.038094,1.543313,-.411435,.082742);function a2(){return Q(pv).scale(153.93)}var dv=co(5/6*B,-.62636,-.0344,0,1.3493,-.05524,0,.045);function o2(){return Q(dv).scale(130.945)}function ll(r,n){var e=r*r,t=n*n;return[r*(1-.162388*t)*(.87-952426e-9*e*e),n*(1+t/12)]}ll.invert=function(r,n){var e=r,t=n,i=50,a;do{var o=t*t;t-=a=(t*(1+o/12)-n)/(1+o/4)}while(Y(a)>X&&--i>0);i=50,r/=1-.162388*o;do{var s=(s=e*e)*s;e-=a=(e*(.87-952426e-9*s)-r)/(.87-.00476213*s)}while(Y(a)>X&&--i>0);return[e,t]};function u2(){return Q(ll).scale(131.747)}var mv=co(2.6516,-.76534,.19123,-.047094,1.36289,-.13965,.031762);function f2(){return Q(mv).scale(131.087)}function yv(r){var n=r(j,0)[0]-r(-j,0)[0];function e(t,i){var a=t>0?-.5:.5,o=r(t+a*B,i);return o[0]-=a*n,o}return r.invert&&(e.invert=function(t,i){var a=t>0?-.5:.5,o=r.invert(t+a*n,i),s=o[0]-a*B;return s<-B?s+=2*B:s>B&&(s-=2*B),o[0]=s,o}),e}function vo(r,n){var e=dr(r),t=dr(n),i=F(n),a=F(r)*i,o=z(r)*i,s=z(t*n);r=Y(mr(o,s)),n=er(a),Y(r-j)>X&&(r%=j);var l=l2(r>B/4?j-r:r,n);return r>B/4&&(s=l[0],l[0]=-l[1],l[1]=-s),l[0]*=e,l[1]*=-t,l}vo.invert=function(r,n){Y(r)>1&&(r=dr(r)*2-r),Y(n)>1&&(n=dr(n)*2-n);var e=dr(r),t=dr(n),i=-e*r,a=-t*n,o=a/i<1,s=s2(o?a:i,o?i:a),l=s[0],c=s[1],u=F(c);return o&&(l=-j-l),[e*(mr(z(l)*u,-z(c))+B),t*er(F(l)*u)]};function l2(r,n){if(n===j)return[0,0];var e=z(n),t=e*e,i=t*t,a=1+i,o=1+3*i,s=1-i,l=er(1/H(a)),c=s+t*a*l,u=(1-e)/c,v=H(u),f=u*a,h=H(f),g=v*s,p,d;if(r===0)return[0,-(g+t*h)];var m=F(n),y=1/m,E=2*e*m,S=(-3*t+l*o)*E,$=(-c*m-(1-e)*S)/(c*c),x=.5*$/v,I=s*x-2*t*v*E,C=t*a*$+u*o*E,L=-y*E,O=-y*C,A=-2*y*I,k=4*r/B,U;if(r>.222*B||n<B/4&&r>.175*B){if(p=(g+t*H(f*(1+i)-g*g))/(1+i),r>B/4)return[p,p];var _=p,w=.5*p;p=.5*(w+_),d=50;do{var b=H(f-p*p),P=p*(A+L*b)+O*er(p/h)-k;if(!P)break;P<0?w=p:_=p,p=.5*(w+_)}while(Y(_-w)>X&&--d>0)}else{p=X,d=25;do{var D=p*p,rr=H(f-D),hr=A+L*rr,ur=p*hr+O*er(p/h)-k,gr=hr+(O-L*D)/rr;p-=U=rr?ur/gr:0}while(Y(U)>X&&--d>0)}return[p,-g-t*H(f-p*p)]}function s2(r,n){for(var e=0,t=1,i=.5,a=50;;){var o=i*i,s=H(i),l=er(1/H(1+o)),c=1-o+i*(1+o)*l,u=(1-s)/c,v=H(u),f=u*(1+o),h=v*(1-o),g=f-r*r,p=H(g),d=n+h+i*p;if(Y(t-e)<dt||--a===0||d===0)break;d>0?e=i:t=i,i=.5*(e+t)}if(!a)return null;var m=er(s),y=F(m),E=1/y,S=2*s*y,$=(-3*i+l*(1+3*o))*S,x=(-c*y-(1-s)*$)/(c*c),I=.5*x/v,C=(1-o)*I-2*i*v*S,L=-2*E*C,O=-E*S,A=-E*(i*(1+o)*x+u*(1+3*o)*S);return[B/4*(r*(L+O*p)+A*er(r/H(f))),m]}function c2(){return Q(yv(vo)).scale(239.75)}function v2(r,n,e){var t,i,a;return r?(t=Xo(r,e),n?(i=Xo(n,1-e),a=i[1]*i[1]+e*t[0]*t[0]*i[0]*i[0],[[t[0]*i[2]/a,t[1]*t[2]*i[0]*i[1]/a],[t[1]*i[1]/a,-t[0]*t[2]*i[0]*i[2]/a],[t[2]*i[1]*i[2]/a,-e*t[0]*t[1]*i[0]/a]]):[[t[0],0],[t[1],0],[t[2],0]]):(i=Xo(n,1-e),[[0,i[0]/i[1]],[1/i[1],0],[i[2]/i[1],0]])}function Xo(r,n){var e,t,i,a,o;if(n<X)return a=z(r),t=F(r),e=n*(r-a*t)/4,[a-e*t,t+e*a,1-n*a*a/2,r-e];if(n>=1-X)return e=(1-n)/4,t=rv(r),a=$d(r),i=1/t,o=t*Wf(r),[a+e*(o-r)/(t*t),i-e*a*i*(o-r),i+e*a*i*(o+r),2*qr(Xn(r))-j+e*(o-r)/t];var s=[1,0,0,0,0,0,0,0,0],l=[H(n),0,0,0,0,0,0,0,0],c=0;for(t=H(1-n),o=1;Y(l[c]/s[c])>X&&c<8;)e=s[c++],l[c]=(e-t)/2,s[c]=(e+t)/2,t=H(e*t),o*=2;i=o*s[c]*r;do a=l[c]*z(t=i)/s[c],i=(er(a)+i)/2;while(--c);return[z(i),a=F(i),a/F(i-t),i]}function h2(r,n,e){var t=Y(r),i=Y(n),a=Wf(i);if(t){var o=1/z(t),s=1/(lr(t)*lr(t)),l=-(s+e*(a*a*o*o)-1+e),c=(e-1)*s,u=(-l+H(l*l-4*c))/2;return[Kt(qr(1/H(u)),e)*dr(r),Kt(qr(H((u/s-1)/e)),1-e)*dr(n)]}return[0,Kt(qr(a),1-e)*dr(n)]}function Kt(r,n){if(!n)return r;if(n===1)return Vr(lr(r/2+cn));for(var e=1,t=H(1-n),i=H(n),a=0;Y(i)>X;a++){if(r%B){var o=qr(t*lr(r)/e);o<0&&(o+=B),r+=o+~~(r/B)*B}else r+=r;i=(e+t)/2,t=H(e*t),i=((e=i)-t)/2}return r/(ef(2,a)*e)}function ho(r,n){var e=(Tr-1)/(Tr+1),t=H(1-e*e),i=Kt(j,t*t),a=-1,o=Vr(lr(B/4+Y(n)/2)),s=Xn(a*o)/H(e),l=g2(s*F(a*r),s*z(a*r)),c=h2(l[0],l[1],t*t);return[-c[1],(n>=0?1:-1)*(.5*i-c[0])]}function g2(r,n){var e=r*r,t=n+1,i=1-e-n*n;return[.5*((r>=0?j:-j)-mr(i,2*r)),-.25*Vr(i*i+4*e)+.5*Vr(t*t+e)]}function p2(r,n){var e=n[0]*n[0]+n[1]*n[1];return[(r[0]*n[0]+r[1]*n[1])/e,(r[1]*n[0]-r[0]*n[1])/e]}ho.invert=function(r,n){var e=(Tr-1)/(Tr+1),t=H(1-e*e),i=Kt(j,t*t),a=-1,o=v2(.5*i-n,-r,t*t),s=p2(o[0],o[1]),l=mr(s[1],s[0])/a;return[l,2*qr(Xn(.5/a*Vr(e*s[0]*s[0]+e*s[1]*s[1])))-j]};function d2(){return Q(yv(ho)).scale(151.496)}function wv(r,n){if(arguments.length<2&&(n=r),n===1)return Qe;if(n===1/0)return Ev;function e(t,i){var a=Qe(t/n,i);return a[0]*=r,a}return e.invert=function(t,i){var a=Qe.invert(t/r,i);return a[0]*=n,a},e}function Ev(r,n){return[r*F(n)/F(n/=2),2*z(n)]}Ev.invert=function(r,n){var e=2*er(n/2);return[r*F(e/2)/F(e),e]};function m2(){var r=2,n=un(wv),e=n(r);return e.coefficient=function(t){return arguments.length?n(r=+t):r},e.scale(169.529)}function Sv(r){var n=z(r),e=F(r),t=vc(r);t.invert=vc(-r);function i(a,o){var s=t(a,o);a=s[0],o=s[1];var l=z(o),c=F(o),u=F(a),v=Jr(n*l+e*c*u),f=z(v),h=Y(f)>X?v/f:1;return[h*e*z(a),(Y(a)>j?h:-h)*(n*c-e*l*u)]}return i.invert=function(a,o){var s=H(a*a+o*o),l=-z(s),c=F(s),u=s*c,v=-o*l,f=s*n,h=H(u*u+v*v-f*f),g=mr(u*f+v*h,v*f-u*h),p=(s>j?-1:1)*mr(a*l,s*F(g)*c+o*z(g)*l);return t.invert(p,g)},i}function vc(r){var n=z(r),e=F(r);return function(t,i){var a=F(i),o=F(t)*a,s=z(t)*a,l=z(i);return[mr(s,o*e-l*n),er(l*e+o*n)]}}function y2(){var r=0,n=un(Sv),e=n(r),t=e.rotate,i=e.stream,a=b1();return e.parallel=function(o){if(!arguments.length)return r*Lr;var s=e.rotate();return n(r=o*wr).rotate(s)},e.rotate=function(o){return arguments.length?(t.call(e,[o[0],o[1]-r*Lr]),a.center([-o[0],-o[1]]),e):(o=t.call(e),o[1]+=r*Lr,o)},e.stream=function(o){return o=i(o),o.sphere=function(){o.polygonStart();var s=.01,l=a.radius(90-s)().coordinates[0],c=l.length-1,u=-1,v;for(o.lineStart();++u<c;)o.point((v=l[u])[0],v[1]);for(o.lineEnd(),l=a.radius(90+s)().coordinates[0],c=l.length-1,o.lineStart();--u>=0;)o.point((v=l[u])[0],v[1]);o.lineEnd(),o.polygonEnd()},o},e.scale(79.4187).parallel(45).clipAngle(180-.001)}var tf=41+48/36+37/3600,jo=Zf(0);function _v(r){var n=tf*wr,e=Hn(B,n)[0]-Hn(-B,n)[0],t=jo(0,n)[1],i=Hn(0,n)[1],a=Rn-i,o=Cn/r,s=4/Cn,l=t+a*a*4/Cn;function c(u,v){var f,h=Y(v);if(h>n){var g=Nn(r-1,ii(0,ot((u+B)/o)));u+=B*(r-1)/r-g*o,f=Hn(u,h),f[0]=f[0]*Cn/e-Cn*(r-1)/(2*r)+g*Cn/r,f[1]=t+(f[1]-i)*4*a/Cn,v<0&&(f[1]=-f[1])}else f=jo(u,v);return f[0]*=s,f[1]/=l,f}return c.invert=function(u,v){u/=s,v*=l;var f=Y(v);if(f>t){var h=Nn(r-1,ii(0,ot((u+B)/o)));u=(u+B*(r-1)/r-h*o)*e/Cn;var g=Hn.invert(u,.25*(f-t)*Cn/a+i);return g[0]-=B*(r-1)/r-h*o,v<0&&(g[1]=-g[1]),g}return jo.invert(u,v)},c}function w2(r){return{type:"Polygon",coordinates:[ge(-180,180+r/2,r).map(function(n,e){return[n,e&1?90-1e-6:tf]}).concat(ge(180,-180-r/2,-r).map(function(n,e){return[n,e&1?-90+1e-6:-tf]}))]}}function E2(){var r=4,n=un(_v),e=n(r),t=e.stream;return e.lobes=function(i){return arguments.length?n(r=+i):r},e.stream=function(i){var a=e.rotate(),o=t(i),s=(e.rotate([0,0]),t(i));return e.rotate(a),o.sphere=function(){sn(w2(180/r),s)},o},e.scale(239.75)}function Tv(r){var n=1+r,e=z(1/n),t=er(e),i=2*H(B/(a=B+4*t*n)),a,o=.5*i*(n+H(r*(2+r))),s=r*r,l=n*n;function c(u,v){var f=1-z(v),h,g;if(f&&f<2){var p=j-v,d=25,m;do{var y=z(p),E=F(p),S=t+mr(y,n-E),$=1+l-2*n*E;p-=m=(p-s*t-n*y+$*S-.5*f*a)/(2*n*y*S)}while(Y(m)>dt&&--d>0);h=i*H($),g=u*S/B}else h=i*(r+f),g=u*t/B;return[h*z(g),o-h*F(g)]}return c.invert=function(u,v){var f=u*u+(v-=o)*v,h=(1+l-f/(i*i))/(2*n),g=Jr(h),p=z(g),d=t+mr(p,n-h);return[er(u/H(f))*B/d,er(1-2*(g-s*t-n*p+(1+l-2*n*h)*d)/a)]},c}function S2(){var r=1,n=un(Tv),e=n(r);return e.ratio=function(t){return arguments.length?n(r=+t):r},e.scale(167.774).center([0,18.67])}var go=.7109889596207567,ut=.0528035274542;function po(r,n){return n>-go?(r=_e(r,n),r[1]+=ut,r):ue(r,n)}po.invert=function(r,n){return n>-go?_e.invert(r,n-ut):ue.invert(r,n)};function _2(){return Q(po).rotate([-20,-55]).scale(164.263).center([0,-5.4036])}function mo(r,n){return Y(n)>go?(r=_e(r,n),r[1]-=n>0?ut:-ut,r):ue(r,n)}mo.invert=function(r,n){return Y(n)>go?_e.invert(r,n+(n>0?ut:-ut)):ue.invert(r,n)};function T2(){return Q(mo).scale(152.63)}function $2(r,n){return Y(r[0]-n[0])<X&&Y(r[1]-n[1])<X}function hc(r,n){for(var e=-1,t=r.length,i=r[0],a,o,s,l=[];++e<t;){a=r[e],o=(a[0]-i[0])/n,s=(a[1]-i[1])/n;for(var c=0;c<n;++c)l.push([i[0]+c*o,i[1]+c*s]);i=a}return l.push(a),l}function M2(r){var n=[],e,t,i,a,o,s,l,c=r[0].length;for(l=0;l<c;++l)e=r[0][l],t=e[0][0],i=e[0][1],a=e[1][1],o=e[2][0],s=e[2][1],n.push(hc([[t+X,i+X],[t+X,a-X],[o-X,a-X],[o-X,s+X]],30));for(l=r[1].length-1;l>=0;--l)e=r[1][l],t=e[0][0],i=e[0][1],a=e[1][1],o=e[2][0],s=e[2][1],n.push(hc([[o-X,s-X],[o-X,a+X],[t+X,a+X],[t+X,i-X]],30));return{type:"Polygon",coordinates:[Bf(n)]}}function Fe(r,n){var e=M2(n);n=n.map(function(s){return s.map(function(l){return[[l[0][0]*wr,l[0][1]*wr],[l[1][0]*wr,l[1][1]*wr],[l[2][0]*wr,l[2][1]*wr]]})});var t=n.map(function(s){return s.map(function(l){var c=r(l[0][0],l[0][1])[0],u=r(l[2][0],l[2][1])[0],v=r(l[1][0],l[0][1])[1],f=r(l[1][0],l[1][1])[1],h;return v>f&&(h=v,v=f,f=h),[[c,v],[u,f]]})});function i(s,l){for(var c=l<0?-1:1,u=n[+(l<0)],v=0,f=u.length-1;v<f&&s>u[v][2][0];++v);var h=r(s-u[v][1][0],l);return h[0]+=r(u[v][1][0],c*l>c*u[v][0][1]?u[v][0][1]:l)[0],h}r.invert&&(i.invert=function(s,l){for(var c=t[+(l<0)],u=n[+(l<0)],v=0,f=c.length;v<f;++v){var h=c[v];if(h[0][0]<=s&&s<h[1][0]&&h[0][1]<=l&&l<h[1][1]){var g=r.invert(s-r(u[v][1][0],0)[0],l);return g[0]+=u[v][1][0],$2(i(g[0],g[1]),[s,l])?g:null}}});var a=Q(i),o=a.stream;return a.stream=function(s){var l=a.rotate(),c=o(s),u=(a.rotate([0,0]),o(s));return a.rotate(l),c.sphere=function(){sn(e,u)},c},a}var x2=[[[[-180,0],[-100,90],[-40,0]],[[-40,0],[30,90],[180,0]]],[[[-180,0],[-160,-90],[-100,0]],[[-100,0],[-60,-90],[-20,0]],[[-20,0],[20,-90],[80,0]],[[80,0],[140,-90],[180,0]]]];function b2(){return Fe(so,x2).scale(160.857)}var P2=[[[[-180,0],[-100,90],[-40,0]],[[-40,0],[30,90],[180,0]]],[[[-180,0],[-160,-90],[-100,0]],[[-100,0],[-60,-90],[-20,0]],[[-20,0],[20,-90],[80,0]],[[80,0],[140,-90],[180,0]]]];function R2(){return Fe(mo,P2).scale(152.63)}var k2=[[[[-180,0],[-100,90],[-40,0]],[[-40,0],[30,90],[180,0]]],[[[-180,0],[-160,-90],[-100,0]],[[-100,0],[-60,-90],[-20,0]],[[-20,0],[20,-90],[80,0]],[[80,0],[140,-90],[180,0]]]];function C2(){return Fe(_e,k2).scale(169.529)}var L2=[[[[-180,0],[-90,90],[0,0]],[[0,0],[90,90],[180,0]]],[[[-180,0],[-90,-90],[0,0]],[[0,0],[90,-90],[180,0]]]];function I2(){return Fe(_e,L2).scale(169.529).rotate([20,0])}var N2=[[[[-180,35],[-30,90],[0,35]],[[0,35],[30,90],[180,35]]],[[[-180,-10],[-102,-90],[-65,-10]],[[-65,-10],[5,-90],[77,-10]],[[77,-10],[103,-90],[180,-10]]]];function A2(){return Fe(po,N2).rotate([-20,-55]).scale(164.263).center([0,-5.4036])}var D2=[[[[-180,0],[-110,90],[-40,0]],[[-40,0],[0,90],[40,0]],[[40,0],[110,90],[180,0]]],[[[-180,0],[-110,-90],[-40,0]],[[-40,0],[0,-90],[40,0]],[[40,0],[110,-90],[180,0]]]];function O2(){return Fe(ue,D2).scale(152.63).rotate([-20,0])}function sl(r,n){return[3/Cn*r*H(B*B/3-n*n),n]}sl.invert=function(r,n){return[Cn/3*r/H(B*B/3-n*n),n]};function q2(){return Q(sl).scale(158.837)}function $v(r){function n(e,t){if(Y(Y(t)-j)<X)return[0,t<0?-2:2];var i=z(t),a=ef((1+i)/(1-i),r/2),o=.5*(a+1/a)+F(e*=r);return[2*z(e)/o,(a-1/a)/o]}return n.invert=function(e,t){var i=Y(t);if(Y(i-2)<X)return e?null:[0,dr(t)*j];if(i>2)return null;e/=2,t/=2;var a=e*e,o=t*t,s=2*t/(1+a+o);return s=ef((1+s)/(1-s),1/r),[mr(2*e,1-a-o)/r,er((s-1)/(s+1))]},n}function F2(){var r=.5,n=un($v),e=n(r);return e.spacing=function(t){return arguments.length?n(r=+t):r},e.scale(124.75)}var Wo=B/Tr;function cl(r,n){return[r*(1+H(F(n)))/2,n/(F(n/2)*F(r/6))]}cl.invert=function(r,n){var e=Y(r),t=Y(n),i=X,a=j;t<Wo?a*=t/Wo:i+=6*Jr(Wo/t);for(var o=0;o<25;o++){var s=z(a),l=H(F(a)),c=z(a/2),u=F(a/2),v=z(i/6),f=F(i/6),h=.5*i*(1+l)-e,g=a/(u*f)-t,p=l?-.25*i*s/l:0,d=.5*(1+l),m=(1+.5*a*c/u)/(u*f),y=a/u*(v/6)/(f*f),E=p*y-m*d,S=(h*y-g*d)/E,$=(g*p-h*m)/E;if(a-=S,i-=$,Y(S)<X&&Y($)<X)break}return[r<0?-i:i,n<0?-a:a]};function z2(){return Q(cl).scale(97.2672)}function vl(r,n){var e=r*r,t=n*n;return[r*(.975534+t*(-.119161+e*-.0143059+t*-.0547009)),n*(1.00384+e*(.0802894+t*-.02855+e*199025e-9)+t*(.0998909+t*-.0491032))]}vl.invert=function(r,n){var e=dr(r)*B,t=n/2,i=50;do{var a=e*e,o=t*t,s=e*t,l=e*(.975534+o*(-.119161+a*-.0143059+o*-.0547009))-r,c=t*(1.00384+a*(.0802894+o*-.02855+a*199025e-9)+o*(.0998909+o*-.0491032))-n,u=.975534-o*(.119161+3*a*.0143059+o*.0547009),v=-s*(2*.119161+4*.0547009*o+2*.0143059*a),f=s*(2*.0802894+4*199025e-9*a+2*-.02855*o),h=1.00384+a*(.0802894+199025e-9*a)+o*(3*(.0998909-.02855*a)-5*.0491032*o),g=v*f-h*u,p=(c*v-l*h)/g,d=(l*f-c*u)/g;e-=p,t-=d}while((Y(p)>X||Y(d)>X)&&--i>0);return i&&[e,t]};function B2(){return Q(vl).scale(139.98)}function hl(r,n){return[z(r)/F(n),lr(n)*F(r)]}hl.invert=function(r,n){var e=r*r,t=n*n,i=t+1,a=r?Mn*H((i-H(e*e+2*e*(t-1)+i*i))/e+1):1/H(i);return[er(r*a),dr(n)*Jr(a)]};function G2(){return Q(hl).scale(144.049).clipAngle(90-.001)}function Mv(r){var n=F(r),e=lr(cn+r/2);function t(i,a){var o=a-r,s=Y(o)<X?i*n:Y(s=cn+a/2)<X||Y(Y(s)-j)<X?0:i*o/Vr(lr(s)/e);return[s,o]}return t.invert=function(i,a){var o,s=a+r;return[Y(a)<X?i/n:Y(o=cn+s/2)<X||Y(Y(o)-j)<X?0:i*Vr(lr(o)/e)/a,s]},t}function U2(){return mt(Mv).parallel(40).scale(158.837)}function gl(r,n){return[r,1.25*Vr(lr(cn+.4*n))]}gl.invert=function(r,n){return[r,2.5*qr(Xn(.8*n))-.625*B]};function H2(){return Q(gl).scale(108.318)}function xv(r){var n=r.length-1;function e(t,i){for(var a=F(i),o=2/(1+a*F(t)),s=o*a*z(t),l=o*z(i),c=n,u=r[c],v=u[0],f=u[1],h;--c>=0;)u=r[c],v=u[0]+s*(h=v)-l*f,f=u[1]+s*f+l*h;return v=s*(h=v)-l*f,f=s*f+l*h,[v,f]}return e.invert=function(t,i){var a=20,o=t,s=i;do{for(var l=n,c=r[l],u=c[0],v=c[1],f=0,h=0,g;--l>=0;)c=r[l],f=u+o*(g=f)-s*h,h=v+o*h+s*g,u=c[0]+o*(g=u)-s*v,v=c[1]+o*v+s*g;f=u+o*(g=f)-s*h,h=v+o*h+s*g,u=o*(g=u)-s*v-t,v=o*v+s*g-i;var p=f*f+h*h,d,m;o-=d=(u*f+v*h)/p,s-=m=(v*f-u*h)/p}while(Y(d)+Y(m)>X*X&&--a>0);if(a){var y=H(o*o+s*s),E=2*qr(y*.5),S=z(E);return[mr(o*S,y*F(E)),y?er(s*S/y):0]}},e}var Y2=[[.9972523,0],[.0052513,-.0041175],[.0074606,.0048125],[-.0153783,-.1968253],[.0636871,-.1408027],[.3660976,-.2937382]],V2=[[.98879,0],[0,0],[-.050909,0],[0,0],[.075528,0]],X2=[[.984299,0],[.0211642,.0037608],[-.1036018,-.0575102],[-.0329095,-.0320119],[.0499471,.1223335],[.026046,.0899805],[7388e-7,-.1435792],[.0075848,-.1334108],[-.0216473,.0776645],[-.0225161,.0853673]],j2=[[.9245,0],[0,0],[.01943,0]],W2=[[.721316,0],[0,0],[-.00881625,-.00617325]];function J2(){return yt(Y2,[152,-64]).scale(1500).center([-160.908,62.4864]).clipAngle(25)}function K2(){return yt(V2,[95,-38]).scale(1e3).clipAngle(55).center([-96.5563,38.8675])}function Q2(){return yt(X2,[120,-45]).scale(359.513).clipAngle(55).center([-117.474,53.0628])}function Z2(){return yt(j2,[-20,-18]).scale(209.091).center([20,16.7214]).clipAngle(82)}function rm(){return yt(W2,[165,10]).scale(250).clipAngle(130).center([-165,-10])}function yt(r,n){var e=Q(xv(r)).rotate(n).clipAngle(90),t=tt(n),i=e.center;return delete e.rotate,e.center=function(a){return arguments.length?i(t(a)):t.invert(i())},e}var qa=H(6),Fa=H(7);function pl(r,n){var e=er(7*z(n)/(3*qa));return[qa*r*(2*F(2*e/3)-1)/Fa,9*z(e/3)/Fa]}pl.invert=function(r,n){var e=3*er(n*Fa/9);return[r*Fa/(qa*(2*F(2*e/3)-1)),er(z(e)*3*qa/7)]};function nm(){return Q(pl).scale(164.859)}function dl(r,n){for(var e=(1+Mn)*z(n),t=n,i=0,a;i<25&&(t-=a=(z(t/2)+z(t)-e)/(.5*F(t/2)+F(t)),!(Y(a)<X));i++);return[r*(1+2*F(t)/F(t/2))/(3*Tr),2*H(3)*z(t/2)/H(2+Tr)]}dl.invert=function(r,n){var e=n*H(2+Tr)/(2*H(3)),t=2*er(e);return[3*Tr*r/(1+2*F(t)/F(t/2)),er((e+z(t))/(1+Mn))]};function em(){return Q(dl).scale(188.209)}function ml(r,n){for(var e=H(6/(4+B)),t=(1+B/4)*z(n),i=n/2,a=0,o;a<25&&(i-=o=(i/2+z(i)-t)/(.5+F(i)),!(Y(o)<X));a++);return[e*(.5+F(i))*r/1.5,e*i]}ml.invert=function(r,n){var e=H(6/(4+B)),t=n/e;return Y(Y(t)-j)<X&&(t=t<0?-j:j),[1.5*r/(e*(.5+F(t))),er((t/2+z(t))/(1+B/4))]};function tm(){return Q(ml).scale(166.518)}function yl(r,n){var e=n*n,t=e*e;return[r*(.8707-.131979*e+t*(-.013791+t*(.003971*e-.001529*t))),n*(1.007226+e*(.015085+t*(-.044475+.028874*e-.005916*t)))]}yl.invert=function(r,n){var e=n,t=25,i;do{var a=e*e,o=a*a;e-=i=(e*(1.007226+a*(.015085+o*(-.044475+.028874*a-.005916*o)))-n)/(1.007226+a*(.015085*3+o*(-.044475*7+.028874*9*a-.005916*11*o)))}while(Y(i)>X&&--t>0);return[r/(.8707+(a=e*e)*(-.131979+a*(-.013791+a*a*a*(.003971-.001529*a)))),e]};function im(){return Q(yl).scale(175.295)}function wl(r,n){var e=n*n,t=e*e,i=e*t;return[r*(.84719-.13063*e+i*i*(-.04515+.05494*e-.02326*t+.00331*i)),n*(1.01183+t*t*(-.02625+.01926*e-.00396*t))]}wl.invert=function(r,n){var e=n,t=25,i,a,o,s;do a=e*e,o=a*a,e-=i=(e*(1.01183+o*o*(-.02625+.01926*a-.00396*o))-n)/(1.01183+o*o*(9*-.02625+11*.01926*a+13*-.00396*o));while(Y(i)>dt&&--t>0);return a=e*e,o=a*a,s=a*o,[r/(.84719-.13063*a+s*s*(-.04515+.05494*a-.02326*o+.00331*s)),e]};function am(){return Q(wl).scale(175.295)}function El(r,n){return[r*(1+F(n))/2,2*(n-lr(n/2))]}El.invert=function(r,n){for(var e=n/2,t=0,i=1/0;t<10&&Y(i)>X;++t){var a=F(n/2);n-=i=(n-lr(n/2)-e)/(1-.5/(a*a))}return[2*r/(1+F(n)),n]};function om(){return Q(El).scale(152.63)}var Sl=1.0148,_l=.23185,Tl=-.14499,$l=.02406,um=Sl,fm=5*_l,lm=7*Tl,sm=9*$l,Ci=1.790857183;function Ml(r,n){var e=n*n;return[r,n*(Sl+e*e*(_l+e*(Tl+$l*e)))]}Ml.invert=function(r,n){n>Ci?n=Ci:n<-Ci&&(n=-Ci);var e=n,t;do{var i=e*e;e-=t=(e*(Sl+i*i*(_l+i*(Tl+$l*i)))-n)/(um+i*i*(fm+i*(lm+sm*i)))}while(Y(t)>X);return[r,e]};function cm(){return Q(Ml).scale(139.319)}function xl(r,n){if(Y(n)<X)return[r,0];var e=lr(n),t=r*z(n);return[z(t)/e,n+(1-F(t))/e]}xl.invert=function(r,n){if(Y(n)<X)return[r,0];var e=r*r+n*n,t=n*.5,i=10,a;do{var o=lr(t),s=1/F(t),l=e-2*n*t+t*t;t-=a=(o*l+2*(t-n))/(2+l*s*s+2*(t-n)*o)}while(Y(a)>X&&--i>0);return o=lr(t),[(Y(n)<Y(t+1/o)?er(r*o):dr(r)*(Jr(Y(r*o))+j))/z(t),t]};function vm(){return Q(xl).scale(103.74)}function hm(r,n){var e=gc(r[1],r[0]),t=gc(n[1],n[0]),i=pm(e,t),a=pc(e)/pc(t);return na([1,0,r[0][0],0,1,r[0][1]],na([a,0,0,0,a,0],na([F(i),z(i),0,-z(i),F(i),0],[1,0,-n[0][0],0,1,-n[0][1]])))}function gm(r){var n=1/(r[0]*r[4]-r[1]*r[3]);return[n*r[4],-n*r[1],n*(r[1]*r[5]-r[2]*r[4]),-n*r[3],n*r[0],n*(r[2]*r[3]-r[0]*r[5])]}function na(r,n){return[r[0]*n[0]+r[1]*n[3],r[0]*n[1]+r[1]*n[4],r[0]*n[2]+r[1]*n[5]+r[2],r[3]*n[0]+r[4]*n[3],r[3]*n[1]+r[4]*n[4],r[3]*n[2]+r[4]*n[5]+r[5]]}function gc(r,n){return[r[0]-n[0],r[1]-n[1]]}function pc(r){return H(r[0]*r[0]+r[1]*r[1])}function pm(r,n){return mr(r[0]*n[1]-r[1]*n[0],r[0]*n[0]+r[1]*n[1])}function yo(r,n,e){e=e==null?-B/6:e,t(r,{transform:[F(e),z(e),0,-z(e),F(e),0]});function t(c,u){if(c.edges=mm(c.face),u.face){var v=c.shared=dm(c.face,u.face),f=hm(v.map(u.project),v.map(c.project));c.transform=u.transform?na(u.transform,f):f;for(var h=u.edges,g=0,p=h.length;g<p;++g)fe(v[0],h[g][1])&&fe(v[1],h[g][0])&&(h[g]=c),fe(v[0],h[g][0])&&fe(v[1],h[g][1])&&(h[g]=c);for(h=c.edges,g=0,p=h.length;g<p;++g)fe(v[0],h[g][0])&&fe(v[1],h[g][1])&&(h[g]=u),fe(v[0],h[g][1])&&fe(v[1],h[g][0])&&(h[g]=u)}else c.transform=u.transform;return c.children&&c.children.forEach(function(d){t(d,c)}),c}function i(c,u){var v=n(c,u),f=v.project([c*Lr,u*Lr]),h;return(h=v.transform)?[h[0]*f[0]+h[1]*f[1]+h[2],-(h[3]*f[0]+h[4]*f[1]+h[5])]:(f[1]=-f[1],f)}Pv(r)&&(i.invert=function(c,u){var v=a(r,[c,-u]);return v&&(v[0]*=wr,v[1]*=wr,v)});function a(c,u){var v=c.project.invert,f=c.transform,h=u;if(f&&(f=gm(f),h=[f[0]*h[0]+f[1]*h[1]+f[2],f[3]*h[0]+f[4]*h[1]+f[5]]),v&&c===o(g=v(h)))return g;for(var g,p=c.children,d=0,m=p&&p.length;d<m;++d)if(g=a(p[d],u))return g}function o(c){return n(c[0]*wr,c[1]*wr)}var s=Q(i),l=s.stream;return s.stream=function(c){var u=s.rotate(),v=l(c),f=(s.rotate([0,0]),l(c));return s.rotate(u),v.sphere=function(){f.polygonStart(),f.lineStart(),bv(f,r),f.lineEnd(),f.polygonEnd()},v},s}function bv(r,n,e){var t,i=n.edges,a=i.length,o,s={type:"MultiPoint",coordinates:n.face},l=n.face.filter(function(p){return Y(p[1])!==90}),c=S1({type:"MultiPoint",coordinates:l}),u=!1,v=-1,f=c[1][0]-c[0][0],h=f===180||f===360?[(c[0][0]+c[1][0])/2,(c[0][1]+c[1][1])/2]:pt(s);if(e)for(;++v<a&&i[v]!==e;);++v;for(var g=0;g<a;++g)o=i[(g+v)%a],Array.isArray(o)?(u||(r.point((t=xa(o[0],h)(X))[0],t[1]),u=!0),r.point((t=xa(o[1],h)(X))[0],t[1])):(u=!1,o!==e&&bv(r,o,n))}function fe(r,n){return r&&n&&r[0]===n[0]&&r[1]===n[1]}function dm(r,n){for(var e,t,i=r.length,a=null,o=0;o<i;++o){e=r[o];for(var s=n.length;--s>=0;)if(t=n[s],e[0]===t[0]&&e[1]===t[1]){if(a)return[a,e];a=e}}}function mm(r){for(var n=r.length,e=[],t=r[n-1],i=0;i<n;++i)e.push([t,t=r[i]]);return e}function Pv(r){return r.project.invert||r.children&&r.children.some(Pv)}var ym=[[0,90],[-90,0],[0,0],[90,0],[180,0],[0,-90]],za=[[0,2,1],[0,3,2],[5,1,2],[5,2,3],[0,1,4],[0,4,3],[5,4,1],[5,3,4]].map(function(r){return r.map(function(n){return ym[n]})});function wm(r){r=r||function(e){var t=pt({type:"MultiPoint",coordinates:e});return Yf().scale(1).translate([0,0]).rotate([-t[0],-t[1]])};var n=za.map(function(e){return{face:e,project:r(e)}});return[-1,0,0,1,0,1,4,5].forEach(function(e,t){var i=n[e];i&&(i.children||(i.children=[])).push(n[t])}),yo(n[0],function(e,t){return n[e<-B/2?t<0?6:4:e<0?t<0?2:0:e<B/2?t<0?3:1:t<0?7:5]}).scale(101.858).center([0,45])}var Rv=2/H(3);function kv(r,n){var e=Hn(r,n);return[e[0]*Rv,e[1]]}kv.invert=function(r,n){return Hn.invert(r/Rv,n)};function Em(r){r=r||function(e){var t=pt({type:"MultiPoint",coordinates:e});return Q(kv).translate([0,0]).scale(1).rotate(t[1]>0?[-t[0],0]:[180-t[0],180])};var n=za.map(function(e){return{face:e,project:r(e)}});return[-1,0,0,1,0,1,4,5].forEach(function(e,t){var i=n[e];i&&(i.children||(i.children=[])).push(n[t])}),yo(n[0],function(e,t){return n[e<-B/2?t<0?6:4:e<0?t<0?2:0:e<B/2?t<0?3:1:t<0?7:5]}).scale(121.906).center([0,48.5904])}function Sm(r){r=r||function(o){var s=o.length===6?pt({type:"MultiPoint",coordinates:o}):o[0];return Yf().scale(1).translate([0,0]).rotate([-s[0],-s[1]])};var n=za.map(function(o){for(var s=o.map(Ko),l=s.length,c=s[l-1],u,v=[],f=0;f<l;++f)u=s[f],v.push(dc([c[0]*.9486832980505138+u[0]*.31622776601683794,c[1]*.9486832980505138+u[1]*.31622776601683794,c[2]*.9486832980505138+u[2]*.31622776601683794]),dc([u[0]*.9486832980505138+c[0]*.31622776601683794,u[1]*.9486832980505138+c[1]*.31622776601683794,u[2]*.9486832980505138+c[2]*.31622776601683794])),c=u;return v}),e=[],t=[-1,0,0,1,0,1,4,5];n.forEach(function(o,s){for(var l=za[s],c=l.length,u=e[s]=[],v=0;v<c;++v)n.push([l[v],o[(v*2+2)%(2*c)],o[(v*2+1)%(2*c)]]),t.push(s),u.push(_m(Ko(o[(v*2+2)%(2*c)]),Ko(o[(v*2+1)%(2*c)])))});var i=n.map(function(o){return{project:r(o),face:o}});t.forEach(function(o,s){var l=i[o];l&&(l.children||(l.children=[])).push(i[s])});function a(o,s){var l=F(s),c=[l*F(o),l*z(o),z(s)],u=o<-B/2?s<0?6:4:o<0?s<0?2:0:o<B/2?s<0?3:1:s<0?7:5,v=e[u];return i[Jo(v[0],c)<0?8+3*u:Jo(v[1],c)<0?8+3*u+1:Jo(v[2],c)<0?8+3*u+2:u]}return yo(i[0],a).scale(110.625).center([0,45])}function Jo(r,n){for(var e=0,t=r.length,i=0;e<t;++e)i+=r[e]*n[e];return i}function _m(r,n){return[r[1]*n[2]-r[2]*n[1],r[2]*n[0]-r[0]*n[2],r[0]*n[1]-r[1]*n[0]]}function dc(r){return[mr(r[1],r[0])*Lr,er(ii(-1,Nn(1,r[2])))*Lr]}function Ko(r){var n=r[0]*wr,e=r[1]*wr,t=F(e);return[t*F(n),t*z(n),z(e)]}function ea(){}function Tm(r){if((e=r.length)<4)return!1;for(var n=0,e,t=r[e-1][1]*r[0][0]-r[e-1][0]*r[0][1];++n<e;)t+=r[n-1][1]*r[n][0]-r[n-1][0]*r[n][1];return t<=0}function $m(r,n){for(var e=n[0],t=n[1],i=!1,a=0,o=r.length,s=o-1;a<o;s=a++){var l=r[a],c=l[0],u=l[1],v=r[s],f=v[0],h=v[1];u>t^h>t&&e<(f-c)*(t-u)/(h-u)+c&&(i=!i)}return i}function Mm(r,n){var e=n.stream,t;if(!e)throw new Error("invalid projection");switch(r&&r.type){case"Feature":t=Cv;break;case"FeatureCollection":t=xm;break;default:t=bl;break}return t(r,e)}function xm(r,n){return{type:"FeatureCollection",features:r.features.map(function(e){return Cv(e,n)})}}function Cv(r,n){return{type:"Feature",id:r.id,properties:r.properties,geometry:bl(r.geometry,n)}}function bm(r,n){return{type:"GeometryCollection",geometries:r.geometries.map(function(e){return bl(e,n)})}}function bl(r,n){if(!r)return null;if(r.type==="GeometryCollection")return bm(r,n);var e;switch(r.type){case"Point":e=mc;break;case"MultiPoint":e=mc;break;case"LineString":e=yc;break;case"MultiLineString":e=yc;break;case"Polygon":e=Qo;break;case"MultiPolygon":e=Qo;break;case"Sphere":e=Qo;break;default:return null}return sn(r,n(e)),e.result()}var Zr=[],Bn=[],mc={point:function(r,n){Zr.push([r,n])},result:function(){var r=Zr.length?Zr.length<2?{type:"Point",coordinates:Zr[0]}:{type:"MultiPoint",coordinates:Zr}:null;return Zr=[],r}},yc={lineStart:ea,point:function(r,n){Zr.push([r,n])},lineEnd:function(){Zr.length&&(Bn.push(Zr),Zr=[])},result:function(){var r=Bn.length?Bn.length<2?{type:"LineString",coordinates:Bn[0]}:{type:"MultiLineString",coordinates:Bn}:null;return Bn=[],r}},Qo={polygonStart:ea,lineStart:ea,point:function(r,n){Zr.push([r,n])},lineEnd:function(){var r=Zr.length;if(r){do Zr.push(Zr[0].slice());while(++r<4);Bn.push(Zr),Zr=[]}},polygonEnd:ea,result:function(){if(!Bn.length)return null;var r=[],n=[];return Bn.forEach(function(e){Tm(e)?r.push([e]):n.push(e)}),n.forEach(function(e){var t=e[0];r.some(function(i){if($m(i[0],t))return i.push(e),!0})||r.push([e])}),Bn=[],r.length?r.length>1?{type:"MultiPolygon",coordinates:r}:{type:"Polygon",coordinates:r[0]}:null}};function Pl(r){var n=r(j,0)[0]-r(-j,0)[0];function e(t,i){var a=Y(t)<j,o=r(a?t:t>0?t-B:t+B,i),s=(o[0]-o[1])*Mn,l=(o[0]+o[1])*Mn;if(a)return[s,l];var c=n*Mn,u=s>0^l>0?-1:1;return[u*s-dr(l)*c,u*l-dr(s)*c]}return r.invert&&(e.invert=function(t,i){var a=(t+i)*Mn,o=(i-t)*Mn,s=Y(a)<.5*n&&Y(o)<.5*n;if(!s){var l=n*Mn,c=a>0^o>0?-1:1,u=-c*t+(o>0?1:-1)*l,v=-c*i+(a>0?1:-1)*l;a=(-u-v)*Mn,o=(u-v)*Mn}var f=r.invert(a,o);return s||(f[0]+=a>0?B:-B),f}),Q(e).rotate([-90,-90,45]).clipAngle(180-.001)}function Pm(){return Pl(vo).scale(176.423)}function wc(){return Pl(ho).scale(111.48)}function Rm(r,n){if(!(0<=(n=+n)&&n<=20))throw new Error("invalid digits");function e(l){var c=l.length,u=2,v=new Array(c);for(v[0]=+l[0].toFixed(n),v[1]=+l[1].toFixed(n);u<c;)v[u]=l[u],++u;return v}function t(l){return l.map(e)}function i(l){return l.map(t)}function a(l){if(l==null)return l;var c;switch(l.type){case"GeometryCollection":c={type:"GeometryCollection",geometries:l.geometries.map(a)};break;case"Point":c={type:"Point",coordinates:e(l.coordinates)};break;case"MultiPoint":case"LineString":c={type:l.type,coordinates:t(l.coordinates)};break;case"MultiLineString":case"Polygon":c={type:l.type,coordinates:i(l.coordinates)};break;case"MultiPolygon":c={type:"MultiPolygon",coordinates:l.coordinates.map(i)};break;default:return l}return l.bbox!=null&&(c.bbox=l.bbox),c}function o(l){var c={type:"Feature",properties:l.properties,geometry:a(l.geometry)};return l.id!=null&&(c.id=l.id),l.bbox!=null&&(c.bbox=l.bbox),c}if(r!=null)switch(r.type){case"Feature":return o(r);case"FeatureCollection":{var s={type:"FeatureCollection",features:r.features.map(o)};return r.bbox!=null&&(s.bbox=r.bbox),s}default:return a(r)}return r}function Lv(r){var n=z(r);function e(t,i){var a=n?lr(t*n/2)/n:t/2;if(!i)return[2*a,-r];var o=2*qr(a*z(i)),s=1/lr(i);return[z(o)*s,i+(1-F(o))*s-r]}return e.invert=function(t,i){if(Y(i+=r)<X)return[n?2*qr(n*t/2)/n:t,0];var a=t*t+i*i,o=0,s=10,l;do{var c=lr(o),u=1/F(o),v=a-2*i*o+o*o;o-=l=(c*v+2*(o-i))/(2+v*u*u+2*(o-i)*c)}while(Y(l)>X&&--s>0);var f=t*(c=lr(o)),h=lr(Y(i)<Y(o+1/c)?er(f)*.5:Jr(f)*.5+B/4)/z(o);return[n?2*qr(n*h)/n:2*h,o]},e}function km(){return mt(Lv).scale(131.215)}var dn=[[.9986,-.062],[1,0],[.9986,.062],[.9954,.124],[.99,.186],[.9822,.248],[.973,.31],[.96,.372],[.9427,.434],[.9216,.4958],[.8962,.5571],[.8679,.6176],[.835,.6769],[.7986,.7346],[.7597,.7903],[.7186,.8435],[.6732,.8936],[.6213,.9394],[.5722,.9761],[.5322,1]];dn.forEach(function(r){r[1]*=1.0144});function Rl(r,n){var e=Nn(18,Y(n)*36/B),t=ot(e),i=e-t,a=(v=dn[t])[0],o=v[1],s=(v=dn[++t])[0],l=v[1],c=(v=dn[Nn(19,++t)])[0],u=v[1],v;return[r*(s+i*(c-a)/2+i*i*(c-2*s+a)/2),(n>0?j:-j)*(l+i*(u-o)/2+i*i*(u-2*l+o)/2)]}Rl.invert=function(r,n){var e=n/j,t=e*90,i=Nn(18,Y(t/5)),a=ii(0,ot(i));do{var o=dn[a][1],s=dn[a+1][1],l=dn[Nn(19,a+2)][1],c=l-o,u=l-2*s+o,v=2*(Y(e)-s)/c,f=u/c,h=v*(1-f*v*(1-2*f*v));if(h>=0||a===1){t=(n>=0?5:-5)*(h+i);var g=50,p;do i=Nn(18,Y(t)/5),a=ot(i),h=i-a,o=dn[a][1],s=dn[a+1][1],l=dn[Nn(19,a+2)][1],t-=(p=(n>=0?j:-j)*(s+h*(l-o)/2+h*h*(l-2*s+o)/2)-n)*Lr;while(Y(p)>dt&&--g>0);break}}while(--a>=0);var d=dn[a][0],m=dn[a+1][0],y=dn[Nn(19,a+2)][0];return[r/(m+h*(y-d)/2+h*h*(y-2*m+d)/2),t*wr]};function Cm(){return Q(Rl).scale(152.63)}function Lm(r){function n(e,t){var i=F(t),a=(r-1)/(r-i*F(e));return[a*i*z(e),a*z(t)]}return n.invert=function(e,t){var i=e*e+t*t,a=H(i),o=(r-H(1-i*(r+1)/(r-1)))/((r-1)/a+a/(r-1));return[mr(e*o,a*H(1-o*o)),a?er(t*o/a):0]},n}function Iv(r,n){var e=Lm(r);if(!n)return e;var t=F(n),i=z(n);function a(o,s){var l=e(o,s),c=l[1],u=c*i/(r-1)+t;return[l[0]*t/u,c/u]}return a.invert=function(o,s){var l=(r-1)/(r-1-s*i);return e.invert(l*o,l*s*t)},a}function Im(){var r=2,n=0,e=un(Iv),t=e(r,n);return t.distance=function(i){return arguments.length?e(r=+i,n):r},t.tilt=function(i){return arguments.length?e(r,n=i*wr):n*Lr},t.scale(432.147).clipAngle(Jr(1/r)*Lr-1e-6)}var wo=1e-4,Ec=1e4,kl=-180,af=kl+wo,Nv=180,of=Nv-wo,Cl=-90,uf=Cl+wo,Ll=90,ff=Ll-wo;function Nm(r){return r.length>0}function Am(r){return Math.floor(r*Ec)/Ec}function Sc(r){return r===Cl||r===Ll?[0,r]:[kl,Am(r)]}function Il(r){var n=r[0],e=r[1],t=!1;return n<=af?(n=kl,t=!0):n>=of&&(n=Nv,t=!0),e<=uf?(e=Cl,t=!0):e>=ff&&(e=Ll,t=!0),t?[n,e]:r}function _c(r){return r.map(Il)}function Tc(r,n,e){for(var t=0,i=r.length;t<i;++t){var a=r[t].slice();e.push({index:-1,polygon:n,ring:a});for(var o=0,s=a.length;o<s;++o){var l=a[o],c=l[0],u=l[1];if(c<=af||c>=of||u<=uf||u>=ff){a[o]=Il(l);for(var v=o+1;v<s;++v){var f=a[v],h=f[0],g=f[1];if(h>af&&h<of&&g>uf&&g<ff)break}if(v===o+1)continue;if(o){var p={index:-1,polygon:n,ring:a.slice(0,o+1)};p.ring[p.ring.length-1]=Sc(u),e[e.length-1]=p}else e.pop();if(v>=s)break;e.push({index:-1,polygon:n,ring:a=a.slice(v-1)}),a[0]=Sc(a[0][1]),o=-1,s=a.length}}}}function $c(r){var n,e=r.length,t={},i={},a,o,s,l,c;for(n=0;n<e;++n){if(a=r[n],o=a.ring[0],l=a.ring[a.ring.length-1],o[0]===l[0]&&o[1]===l[1]){a.polygon.push(a.ring),r[n]=null;continue}a.index=n,t[o]=i[l]=a}for(n=0;n<e;++n)if(a=r[n],a){if(o=a.ring[0],l=a.ring[a.ring.length-1],s=i[o],c=t[l],delete t[o],delete i[l],o[0]===l[0]&&o[1]===l[1]){a.polygon.push(a.ring);continue}s?(delete i[o],delete t[s.ring[0]],s.ring.pop(),r[s.index]=null,a={index:-1,polygon:s.polygon,ring:s.ring.concat(a.ring)},s===c?a.polygon.push(a.ring):(a.index=e++,r.push(t[a.ring[0]]=i[a.ring[a.ring.length-1]]=a))):c?(delete t[l],delete i[c.ring[c.ring.length-1]],a.ring.pop(),a={index:e++,polygon:c.polygon,ring:a.ring.concat(c.ring)},r[c.index]=null,r.push(t[a.ring[0]]=i[a.ring[a.ring.length-1]]=a)):(a.ring.push(a.ring[0]),a.polygon.push(a.ring))}}function Mc(r){var n={type:"Feature",geometry:Nl(r.geometry)};return r.id!=null&&(n.id=r.id),r.bbox!=null&&(n.bbox=r.bbox),r.properties!=null&&(n.properties=r.properties),n}function Nl(r){if(r==null)return r;var n,e,t,i;switch(r.type){case"GeometryCollection":n={type:"GeometryCollection",geometries:r.geometries.map(Nl)};break;case"Point":n={type:"Point",coordinates:Il(r.coordinates)};break;case"MultiPoint":case"LineString":n={type:r.type,coordinates:_c(r.coordinates)};break;case"MultiLineString":n={type:"MultiLineString",coordinates:r.coordinates.map(_c)};break;case"Polygon":{var a=[];Tc(r.coordinates,a,e=[]),$c(e),n={type:"Polygon",coordinates:a};break}case"MultiPolygon":{e=[],t=-1,i=r.coordinates.length;for(var o=new Array(i);++t<i;)Tc(r.coordinates[t],o[t]=[],e);$c(e),n={type:"MultiPolygon",coordinates:o.filter(Nm)};break}default:return r}return r.bbox!=null&&(n.bbox=r.bbox),n}function Dm(r){if(r==null)return r;switch(r.type){case"Feature":return Mc(r);case"FeatureCollection":{var n={type:"FeatureCollection",features:r.features.map(Mc)};return r.bbox!=null&&(n.bbox=r.bbox),n}default:return Nl(r)}}function Al(r,n){var e=lr(n/2),t=z(cn*e);return[r*(.74482-.34588*t*t),1.70711*e]}Al.invert=function(r,n){var e=n/1.70711,t=z(cn*e);return[r/(.74482-.34588*t*t),2*qr(e)]};function Om(){return Q(Al).scale(146.153)}function Av(r,n,e){var t=xa(n,e),i=t(.5),a=tt([-i[0],-i[1]])(n),o=t.distance/2,s=-er(z(a[1]*wr)/z(o)),l=[-i[0],-i[1],-(a[0]>0?B-s:s)*Lr],c=Q(r(o)).rotate(l),u=tt(l),v=c.center;return delete c.rotate,c.center=function(f){return arguments.length?v(u(f)):u.invert(v())},c.clipAngle(90)}function Dv(r){var n=F(r);function e(t,i){var a=ti(t,i);return a[0]*=n,a}return e.invert=function(t,i){return ti.invert(t/n,i)},e}function qm(){return Ov([-158,21.5],[-77,39]).clipAngle(60).scale(400)}function Ov(r,n){return Av(Dv,r,n)}function qv(r){if(!(r*=2))return ye;var n=-r/2,e=-n,t=r*r,i=lr(e),a=.5/z(e);function o(s,l){var c=Jr(F(l)*F(s-n)),u=Jr(F(l)*F(s-e)),v=l<0?-1:1;return c*=c,u*=u,[(c-u)/(2*r),v*H(4*t*u-(t-c+u)*(t-c+u))/(2*r)]}return o.invert=function(s,l){var c=l*l,u=F(H(c+(f=s+n)*f)),v=F(H(c+(f=s+e)*f)),f,h;return[mr(h=u-v,f=(u+v)*i),(l<0?-1:1)*Jr(H(f*f+h*h)*a)]},o}function Fm(){return Fv([-158,21.5],[-77,39]).clipAngle(130).scale(122.571)}function Fv(r,n){return Av(qv,r,n)}function Dl(r,n){if(Y(n)<X)return[r,0];var e=Y(n/j),t=er(e);if(Y(r)<X||Y(Y(n)-j)<X)return[0,dr(n)*B*lr(t/2)];var i=F(t),a=Y(B/r-r/B)/2,o=a*a,s=i/(e+i-1),l=s*(2/e-1),c=l*l,u=c+o,v=s-c,f=o+s;return[dr(r)*B*(a*v+H(o*v*v-u*(s*s-c)))/u,dr(n)*B*(l*f-a*H((o+1)*u-f*f))/u]}Dl.invert=function(r,n){if(Y(n)<X)return[r,0];if(Y(r)<X)return[0,j*z(2*qr(n/B))];var e=(r/=B)*r,t=(n/=B)*n,i=e+t,a=i*i,o=-Y(n)*(1+i),s=o-2*t+e,l=-2*o+1+2*t+a,c=t/l+(2*s*s*s/(l*l*l)-9*o*s/(l*l))/27,u=(o-s*s/(3*l))/l,v=2*H(-u/3),f=Jr(3*c/(u*v))/3;return[B*(i-1+H(1+2*(e-t)+a))/(2*r),dr(n)*B*(-v*F(f+B/3)-s/(3*l))]};function zm(){return Q(Dl).scale(79.4183)}function Ol(r,n){if(Y(n)<X)return[r,0];var e=Y(n/j),t=er(e);if(Y(r)<X||Y(Y(n)-j)<X)return[0,dr(n)*B*lr(t/2)];var i=F(t),a=Y(B/r-r/B)/2,o=a*a,s=i*(H(1+o)-a*i)/(1+o*e*e);return[dr(r)*B*s,dr(n)*B*H(1-s*(2*a+s))]}Ol.invert=function(r,n){if(!r)return[0,j*z(2*qr(n/B))];var e=Y(r/B),t=(1-e*e-(n/=B)*n)/(2*e),i=t*t,a=H(i+1);return[dr(r)*B*(a-t),dr(n)*j*z(2*mr(H((1-2*t*e)*(t+a)-e),H(a+t+e)))]};function Bm(){return Q(Ol).scale(79.4183)}function ql(r,n){if(Y(n)<X)return[r,0];var e=n/j,t=er(e);if(Y(r)<X||Y(Y(n)-j)<X)return[0,B*lr(t/2)];var i=(B/r-r/B)/2,a=e/(1+F(t));return[B*(dr(r)*H(i*i+1-a*a)-i),B*a]}ql.invert=function(r,n){if(!n)return[r,0];var e=n/B,t=(B*B*(1-e*e)-r*r)/(2*B*r);return[r?B*(dr(r)*H(t*t+1)-t):0,j*z(2*qr(e))]};function Gm(){return Q(ql).scale(79.4183)}function Fl(r,n){if(!n)return[r,0];var e=Y(n);if(!r||e===j)return[0,n];var t=e/j,i=t*t,a=(8*t-i*(i+2)-5)/(2*i*(t-1)),o=a*a,s=t*a,l=i+o+2*s,c=t+3*a,u=r/j,v=u+1/u,f=dr(Y(r)-j)*H(v*v-4),h=f*f,g=l*(i+o*h-1)+(1-i)*(i*(c*c+4*o)+12*s*o+4*o*o),p=(f*(l+o-1)+2*H(g))/(4*l+h);return[dr(r)*j*p,dr(n)*j*H(1+f*Y(p)-p*p)]}Fl.invert=function(r,n){var e;if(!r||!n)return[r,n];n/=B;var t=dr(r)*r/j,i=(t*t-1+4*n*n)/Y(t),a=i*i,o=2*n,s=50;do{var l=o*o,c=(8*o-l*(l+2)-5)/(2*l*(o-1)),u=(3*o-l*o-10)/(2*l*o),v=c*c,f=o*c,h=o+c,g=h*h,p=o+3*c,d=g*(l+v*a-1)+(1-l)*(l*(p*p+4*v)+v*(12*f+4*v)),m=-2*h*(4*f*v+(1-4*l+3*l*l)*(1+u)+v*(-6+14*l-a+(-8+8*l-2*a)*u)+f*(-8+12*l+(-10+10*l-a)*u)),y=H(d),E=i*(g+v-1)+2*y-t*(4*g+a),S=i*(2*c*u+2*h*(1+u))+m/y-8*h*(i*(-1+v+g)+2*y)*(1+u)/(a+4*g);o-=e=E/S}while(e>X&&--s>0);return[dr(r)*(H(i*i+4)+i)*B/4,j*o]};function Um(){return Q(Fl).scale(127.16)}var zv=4*B+3*H(3),xc=2*H(2*B*H(3)/zv),Bv=Kf(xc*H(3)/B,xc,zv/6);function Hm(){return Q(Bv).scale(176.84)}function zl(r,n){return[r*H(1-3*n*n/(B*B)),n]}zl.invert=function(r,n){return[r/H(1-3*n*n/(B*B)),n]};function Ym(){return Q(zl).scale(152.63)}function Bl(r,n){var e=.90631*z(n),t=H(1-e*e),i=H(2/(1+t*F(r/=3)));return[2.66723*t*i*z(r),1.24104*e*i]}Bl.invert=function(r,n){var e=r/2.66723,t=n/1.24104,i=H(e*e+t*t),a=2*er(i/2);return[3*mr(r*lr(a),2.66723*i),i&&er(n*z(a)/(1.24104*.90631*i))]};function Vm(){return Q(Bl).scale(172.632)}function Gl(r,n){var e=F(n),t=F(r)*e,i=1-t,a=F(r=mr(z(r)*e,-z(n))),o=z(r);return e=H(1-t*t),[o*e-a*i,-a*e-o*i]}Gl.invert=function(r,n){var e=(r*r+n*n)/-2,t=H(-e*(2+e)),i=n*e+r*t,a=r*e-n*t,o=H(a*a+i*i);return[mr(t*i,o*(1+e)),o?-er(t*a/o):0]};function Xm(){return Q(Gl).rotate([0,-90,45]).scale(124.75).clipAngle(180-.001)}function Ul(r,n){var e=fo(r,n);return[(e[0]+r/j)/2,(e[1]+n)/2]}Ul.invert=function(r,n){var e=r,t=n,i=25;do{var a=F(t),o=z(t),s=z(2*t),l=o*o,c=a*a,u=z(e),v=F(e/2),f=z(e/2),h=f*f,g=1-c*v*v,p=g?Jr(a*v)*H(d=1/g):d=0,d,m=.5*(2*p*a*f+e/j)-r,y=.5*(p*o+t)-n,E=.5*d*(c*h+p*a*v*l)+.5/j,S=d*(u*s/4-p*o*f),$=.125*d*(s*f-p*o*c*u),x=.5*d*(l*v+p*h*a)+.5,I=S*$-x*E,C=(y*S-m*x)/I,L=(m*$-y*E)/I;e-=C,t-=L}while((Y(C)>X||Y(L)>X)&&--i>0);return[e,t]};function jm(){return Q(Ul).scale(158.837)}var Wm=Object.freeze(Object.defineProperty({__proto__:null,geoAiry:bd,geoAiryRaw:nv,geoAitoff:Pd,geoAitoffRaw:fo,geoArmadillo:Rd,geoArmadilloRaw:ev,geoAugust:kd,geoAugustRaw:lo,geoBaker:Ld,geoBakerRaw:Jf,geoBerghaus:Id,geoBerghausRaw:tv,geoBoggs:Ad,geoBoggsRaw:so,geoBonne:Od,geoBonneRaw:ov,geoBottomley:qd,geoBottomleyRaw:uv,geoBromley:Fd,geoBromleyRaw:fv,geoChamberlin:sv,geoChamberlinRaw:lv,geoChamberlinAfrica:Bd,geoCollignon:Gd,geoCollignonRaw:Hn,geoCraig:Ud,geoCraigRaw:cv,geoCraster:Hd,geoCrasterRaw:Qf,geoCylindricalEqualArea:Yd,geoCylindricalEqualAreaRaw:Zf,geoCylindricalStereographic:Vd,geoCylindricalStereographicRaw:vv,geoEckert1:Xd,geoEckert1Raw:rl,geoEckert2:jd,geoEckert2Raw:nl,geoEckert3:Wd,geoEckert3Raw:el,geoEckert4:Jd,geoEckert4Raw:tl,geoEckert5:Kd,geoEckert5Raw:il,geoEckert6:Qd,geoEckert6Raw:al,geoEisenlohr:Zd,geoEisenlohrRaw:ol,geoFahey:r2,geoFaheyRaw:ul,geoFoucaut:n2,geoFoucautRaw:fl,geoGilbert:e2,geoGingery:t2,geoGingeryRaw:hv,geoGinzburg4:i2,geoGinzburg4Raw:gv,geoGinzburg5:a2,geoGinzburg5Raw:pv,geoGinzburg6:o2,geoGinzburg6Raw:dv,geoGinzburg8:u2,geoGinzburg8Raw:ll,geoGinzburg9:f2,geoGinzburg9Raw:mv,geoGringorten:c2,geoGringortenRaw:vo,geoGuyou:d2,geoGuyouRaw:ho,geoHammer:m2,geoHammerRaw:wv,geoHammerRetroazimuthal:y2,geoHammerRetroazimuthalRaw:Sv,geoHealpix:E2,geoHealpixRaw:_v,geoHill:S2,geoHillRaw:Tv,geoHomolosine:T2,geoHomolosineRaw:mo,geoInterrupt:Fe,geoInterruptedBoggs:b2,geoInterruptedHomolosine:R2,geoInterruptedMollweide:C2,geoInterruptedMollweideHemispheres:I2,geoInterruptedSinuMollweide:A2,geoInterruptedSinusoidal:O2,geoKavrayskiy7:q2,geoKavrayskiy7Raw:sl,geoLagrange:F2,geoLagrangeRaw:$v,geoLarrivee:z2,geoLarriveeRaw:cl,geoLaskowski:B2,geoLaskowskiRaw:vl,geoLittrow:G2,geoLittrowRaw:hl,geoLoximuthal:U2,geoLoximuthalRaw:Mv,geoMiller:H2,geoMillerRaw:gl,geoModifiedStereographic:yt,geoModifiedStereographicRaw:xv,geoModifiedStereographicAlaska:J2,geoModifiedStereographicGs48:K2,geoModifiedStereographicGs50:Q2,geoModifiedStereographicMiller:Z2,geoModifiedStereographicLee:rm,geoMollweide:Nd,geoMollweideRaw:_e,geoMtFlatPolarParabolic:nm,geoMtFlatPolarParabolicRaw:pl,geoMtFlatPolarQuartic:em,geoMtFlatPolarQuarticRaw:dl,geoMtFlatPolarSinusoidal:tm,geoMtFlatPolarSinusoidalRaw:ml,geoNaturalEarth:im,geoNaturalEarthRaw:yl,geoNaturalEarth2:am,geoNaturalEarth2Raw:wl,geoNellHammer:om,geoNellHammerRaw:El,geoPatterson:cm,geoPattersonRaw:Ml,geoPolyconic:vm,geoPolyconicRaw:xl,geoPolyhedral:yo,geoPolyhedralButterfly:wm,geoPolyhedralCollignon:Em,geoPolyhedralWaterman:Sm,geoProject:Mm,geoGringortenQuincuncial:Pm,geoPeirceQuincuncial:wc,geoPierceQuincuncial:wc,geoQuantize:Rm,geoQuincuncial:Pl,geoRectangularPolyconic:km,geoRectangularPolyconicRaw:Lv,geoRobinson:Cm,geoRobinsonRaw:Rl,geoSatellite:Im,geoSatelliteRaw:Iv,geoSinuMollweide:_2,geoSinuMollweideRaw:po,geoSinusoidal:Dd,geoSinusoidalRaw:ue,geoStitch:Dm,geoTimes:Om,geoTimesRaw:Al,geoTwoPointAzimuthal:Ov,geoTwoPointAzimuthalRaw:Dv,geoTwoPointAzimuthalUsa:qm,geoTwoPointEquidistant:Fv,geoTwoPointEquidistantRaw:qv,geoTwoPointEquidistantUsa:Fm,geoVanDerGrinten:zm,geoVanDerGrintenRaw:Dl,geoVanDerGrinten2:Bm,geoVanDerGrinten2Raw:Ol,geoVanDerGrinten3:Gm,geoVanDerGrinten3Raw:ql,geoVanDerGrinten4:Um,geoVanDerGrinten4Raw:Fl,geoWagner4:Hm,geoWagner4Raw:Bv,geoWagner6:Ym,geoWagner6Raw:zl,geoWagner7:Vm,geoWagner7Raw:Bl,geoWiechel:Xm,geoWiechelRaw:Gl,geoWinkel3:jm,geoWinkel3Raw:Ul},Symbol.toStringTag,{value:"Module"})),Gv=oe(Wm),Te={},Hl={exports:{}};/*!
 * EventEmitter v5.2.9 - git.io/ee
 * Unlicense - http://unlicense.org/
 * Oliver Caldwell - https://oli.me.uk/
 * @preserve
 */(function(r){(function(n){function e(){}var t=e.prototype,i=n.EventEmitter;function a(l,c){for(var u=l.length;u--;)if(l[u].listener===c)return u;return-1}function o(l){return function(){return this[l].apply(this,arguments)}}t.getListeners=function(c){var u=this._getEvents(),v,f;if(c instanceof RegExp){v={};for(f in u)u.hasOwnProperty(f)&&c.test(f)&&(v[f]=u[f])}else v=u[c]||(u[c]=[]);return v},t.flattenListeners=function(c){var u=[],v;for(v=0;v<c.length;v+=1)u.push(c[v].listener);return u},t.getListenersAsObject=function(c){var u=this.getListeners(c),v;return u instanceof Array&&(v={},v[c]=u),v||u};function s(l){return typeof l=="function"||l instanceof RegExp?!0:l&&typeof l=="object"?s(l.listener):!1}t.addListener=function(c,u){if(!s(u))throw new TypeError("listener must be a function");var v=this.getListenersAsObject(c),f=typeof u=="object",h;for(h in v)v.hasOwnProperty(h)&&a(v[h],u)===-1&&v[h].push(f?u:{listener:u,once:!1});return this},t.on=o("addListener"),t.addOnceListener=function(c,u){return this.addListener(c,{listener:u,once:!0})},t.once=o("addOnceListener"),t.defineEvent=function(c){return this.getListeners(c),this},t.defineEvents=function(c){for(var u=0;u<c.length;u+=1)this.defineEvent(c[u]);return this},t.removeListener=function(c,u){var v=this.getListenersAsObject(c),f,h;for(h in v)v.hasOwnProperty(h)&&(f=a(v[h],u),f!==-1&&v[h].splice(f,1));return this},t.off=o("removeListener"),t.addListeners=function(c,u){return this.manipulateListeners(!1,c,u)},t.removeListeners=function(c,u){return this.manipulateListeners(!0,c,u)},t.manipulateListeners=function(c,u,v){var f,h,g=c?this.removeListener:this.addListener,p=c?this.removeListeners:this.addListeners;if(typeof u=="object"&&!(u instanceof RegExp))for(f in u)u.hasOwnProperty(f)&&(h=u[f])&&(typeof h=="function"?g.call(this,f,h):p.call(this,f,h));else for(f=v.length;f--;)g.call(this,u,v[f]);return this},t.removeEvent=function(c){var u=typeof c,v=this._getEvents(),f;if(u==="string")delete v[c];else if(c instanceof RegExp)for(f in v)v.hasOwnProperty(f)&&c.test(f)&&delete v[f];else delete this._events;return this},t.removeAllListeners=o("removeEvent"),t.emitEvent=function(c,u){var v=this.getListenersAsObject(c),f,h,g,p,d;for(p in v)if(v.hasOwnProperty(p))for(f=v[p].slice(0),g=0;g<f.length;g++)h=f[g],h.once===!0&&this.removeListener(c,h.listener),d=h.listener.apply(this,u||[]),d===this._getOnceReturnValue()&&this.removeListener(c,h.listener);return this},t.trigger=o("emitEvent"),t.emit=function(c){var u=Array.prototype.slice.call(arguments,1);return this.emitEvent(c,u)},t.setOnceReturnValue=function(c){return this._onceReturnValue=c,this},t._getOnceReturnValue=function(){return this.hasOwnProperty("_onceReturnValue")?this._onceReturnValue:!0},t._getEvents=function(){return this._events||(this._events={})},e.noConflict=function(){return n.EventEmitter=i,e},r.exports?r.exports=e:n.EventEmitter=e})(typeof window!="undefined"?window:ao||{})})(Hl);Object.defineProperty(Te,"__esModule",{value:!0});Te.View=void 0;var Uv=pr,Jm=Uv.__importDefault(Hl.exports),Wr=or;function Km(r){var n={};return Wr.forIn(r,function(e,t){Wr.isObject(e)&&e.isView?n[t]=e:Wr.isArray(e)?n[t]=e.concat([]):Wr.isPlainObject(e)?n[t]=Wr.clone(e):n[t]=e}),n}var Qm=function(r){Uv.__extends(n,r);function n(e,t){var i=r.call(this)||this;if(i.isView=!0,i.isDataView=!0,i.watchingStates=null,i.dataType="table",i.transforms=[],i.origin=[],i.rows=[],e&&e.isDataSet?i.dataSet=e:(i.dataSet=null,t=e),i.loose=!i.dataSet,t&&(i.watchingStates=t.watchingStates),!i.loose){var a=i.watchingStates;e.on("statechange",function(o){Wr.isArray(a)?a.indexOf(o)>-1&&i._reExecute():i._reExecute()})}return i}return n.prototype._parseStateExpression=function(e){var t=this.dataSet;if(t!==null){var i=/^\$state\.(\w+)/.exec(e);return i?t.state[i[1]]:e}},n.prototype._preparseOptions=function(e){var t=this,i=Km(e);return this.loose||Wr.forIn(i,function(a,o){Wr.isString(a)&&/^\$state\./.test(a)&&(i[o]=t._parseStateExpression(a))}),i},n.prototype._prepareSource=function(e,t){if(this._source={source:e,options:t},t){var i=this._preparseOptions(t);this.origin=n.DataSet.getConnector(i.type)(e,i,this)}else if(e instanceof n||Wr.isString(e))this.origin=n.DataSet.getConnector("default")(e,this.dataSet);else if(Wr.isArray(e))this.origin=e;else if(Wr.isObject(e)&&e.type){var i=this._preparseOptions(e);this.origin=n.DataSet.getConnector(i.type)(i,this)}else throw new TypeError("Invalid source");return this.rows=Wr.deepMix([],this.origin),this},n.prototype.source=function(e,t){return this._prepareSource(e,t)._reExecuteTransforms(),this.trigger("change",[]),this},n.prototype.transform=function(e){return e&&e.type&&(this.transforms.push(e),this._executeTransform(e)),this},n.prototype._executeTransform=function(e){e=this._preparseOptions(e);var t=n.DataSet.getTransform(e.type);t(this,e)},n.prototype._reExecuteTransforms=function(){var e=this;this.transforms.forEach(function(t){e._executeTransform(t)})},n.prototype.addRow=function(e){this.rows.push(e)},n.prototype.removeRow=function(e){this.rows.splice(e,1)},n.prototype.updateRow=function(e,t){Wr.assign(this.rows[e],t)},n.prototype.findRows=function(e){return this.rows.filter(function(t){return Wr.isMatch(t,e)})},n.prototype.findRow=function(e){return Wr.find(this.rows,e)},n.prototype.getColumnNames=function(){var e=this.rows[0];return e?Wr.keys(e):[]},n.prototype.getColumnName=function(e){return this.getColumnNames()[e]},n.prototype.getColumnIndex=function(e){var t=this.getColumnNames();return t.indexOf(e)},n.prototype.getColumn=function(e){return this.rows.map(function(t){return t[e]})},n.prototype.getColumnData=function(e){return this.getColumn(e)},n.prototype.getSubset=function(e,t,i){for(var a=[],o=e;o<=t;o++)a.push(Wr.pick(this.rows[o],i));return a},n.prototype.toString=function(e){return e===void 0&&(e=!1),e?JSON.stringify(this.rows,null,2):JSON.stringify(this.rows)},n.prototype._reExecute=function(){var e=this._source,t=e.source,i=e.options;this._prepareSource(t,i),this._reExecuteTransforms(),this.trigger("change",[])},n}(Jm.default);Te.View=Qm;var Eo={},q=1e-6;class Zm{constructor(){this._partials=new Float64Array(32),this._n=0}add(n){const e=this._partials;let t=0;for(let i=0;i<this._n&&i<32;i++){const a=e[i],o=n+a,s=Math.abs(n)<Math.abs(a)?n-(o-a):a-(o-n);s&&(e[t++]=s),n=o}return e[t]=n,this._n=t+1,this}valueOf(){const n=this._partials;let e=this._n,t,i,a,o=0;if(e>0){for(o=n[--e];e>0&&(t=o,i=n[--e],o=t+i,a=i-(o-t),!a););e>0&&(a<0&&n[e-1]<0||a>0&&n[e-1]>0)&&(i=a*2,t=o+i,i==t-o&&(o=t))}return o}}function*r5(r){for(const n of r)yield*n}function Hv(r){return Array.from(r5(r))}var _r=1e-6,n5=1e-12,cr=Math.PI,Br=cr/2,bc=cr/4,kn=cr*2,nn=180/cr,zr=cr/180,Ir=Math.abs,gi=Math.atan,ee=Math.atan2,Sr=Math.cos,Yv=Math.exp,Ba=Math.log,Zo=Math.pow,Er=Math.sin,Pn=Math.sign||function(r){return r>0?1:r<0?-1:0},te=Math.sqrt,Yl=Math.tan;function e5(r){return r>1?0:r<-1?cr:Math.acos(r)}function we(r){return r>1?Br:r<-1?-Br:Math.asin(r)}function zt(){}function Ga(r,n){r&&Rc.hasOwnProperty(r.type)&&Rc[r.type](r,n)}var Pc={Feature:function(r,n){Ga(r.geometry,n)},FeatureCollection:function(r,n){for(var e=r.features,t=-1,i=e.length;++t<i;)Ga(e[t].geometry,n)}},Rc={Sphere:function(r,n){n.sphere()},Point:function(r,n){r=r.coordinates,n.point(r[0],r[1],r[2])},MultiPoint:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)r=e[t],n.point(r[0],r[1],r[2])},LineString:function(r,n){lf(r.coordinates,n,0)},MultiLineString:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)lf(e[t],n,0)},Polygon:function(r,n){kc(r.coordinates,n)},MultiPolygon:function(r,n){for(var e=r.coordinates,t=-1,i=e.length;++t<i;)kc(e[t],n)},GeometryCollection:function(r,n){for(var e=r.geometries,t=-1,i=e.length;++t<i;)Ga(e[t],n)}};function lf(r,n,e){var t=-1,i=r.length-e,a;for(n.lineStart();++t<i;)a=r[t],n.point(a[0],a[1],a[2]);n.lineEnd()}function kc(r,n){var e=-1,t=r.length;for(n.polygonStart();++e<t;)lf(r[e],n,1);n.polygonEnd()}function Vv(r,n){r&&Pc.hasOwnProperty(r.type)?Pc[r.type](r,n):Ga(r,n)}function sf(r){return[ee(r[1],r[0]),we(r[2])]}function ft(r){var n=r[0],e=r[1],t=Sr(e);return[t*Sr(n),t*Er(n),Er(e)]}function Li(r,n){return r[0]*n[0]+r[1]*n[1]+r[2]*n[2]}function Ua(r,n){return[r[1]*n[2]-r[2]*n[1],r[2]*n[0]-r[0]*n[2],r[0]*n[1]-r[1]*n[0]]}function ru(r,n){r[0]+=n[0],r[1]+=n[1],r[2]+=n[2]}function Ii(r,n){return[r[0]*n,r[1]*n,r[2]*n]}function cf(r){var n=te(r[0]*r[0]+r[1]*r[1]+r[2]*r[2]);r[0]/=n,r[1]/=n,r[2]/=n}function vf(r,n){function e(t,i){return t=r(t,i),n(t[0],t[1])}return r.invert&&n.invert&&(e.invert=function(t,i){return t=n.invert(t,i),t&&r.invert(t[0],t[1])}),e}function hf(r,n){return[Ir(r)>cr?r+Math.round(-r/kn)*kn:r,n]}hf.invert=hf;function Xv(r,n,e){return(r%=kn)?n||e?vf(Lc(r),Ic(n,e)):Lc(r):n||e?Ic(n,e):hf}function Cc(r){return function(n,e){return n+=r,[n>cr?n-kn:n<-cr?n+kn:n,e]}}function Lc(r){var n=Cc(r);return n.invert=Cc(-r),n}function Ic(r,n){var e=Sr(r),t=Er(r),i=Sr(n),a=Er(n);function o(s,l){var c=Sr(l),u=Sr(s)*c,v=Er(s)*c,f=Er(l),h=f*e+u*t;return[ee(v*i-h*a,u*e-f*t),we(h*i+v*a)]}return o.invert=function(s,l){var c=Sr(l),u=Sr(s)*c,v=Er(s)*c,f=Er(l),h=f*i-v*a;return[ee(v*i+f*a,u*e+h*t),we(h*e-u*t)]},o}function t5(r){r=Xv(r[0]*zr,r[1]*zr,r.length>2?r[2]*zr:0);function n(e){return e=r(e[0]*zr,e[1]*zr),e[0]*=nn,e[1]*=nn,e}return n.invert=function(e){return e=r.invert(e[0]*zr,e[1]*zr),e[0]*=nn,e[1]*=nn,e},n}function i5(r,n,e,t,i,a){if(!!e){var o=Sr(n),s=Er(n),l=t*e;i==null?(i=n+t*kn,a=n-l/2):(i=Nc(o,i),a=Nc(o,a),(t>0?i<a:i>a)&&(i+=t*kn));for(var c,u=i;t>0?u>a:u<a;u-=l)c=sf([o,-s*Sr(u),-s*Er(u)]),r.point(c[0],c[1])}}function Nc(r,n){n=ft(n),n[0]-=r,cf(n);var e=e5(-n[1]);return((-n[2]<0?-e:e)+kn-_r)%kn}function jv(){var r=[],n;return{point:function(e,t,i){n.push([e,t,i])},lineStart:function(){r.push(n=[])},lineEnd:zt,rejoin:function(){r.length>1&&r.push(r.pop().concat(r.shift()))},result:function(){var e=r;return r=[],n=null,e}}}function ta(r,n){return Ir(r[0]-n[0])<_r&&Ir(r[1]-n[1])<_r}function Ni(r,n,e,t){this.x=r,this.z=n,this.o=e,this.e=t,this.v=!1,this.n=this.p=null}function Wv(r,n,e,t,i){var a=[],o=[],s,l;if(r.forEach(function(g){if(!((p=g.length-1)<=0)){var p,d=g[0],m=g[p],y;if(ta(d,m)){if(!d[2]&&!m[2]){for(i.lineStart(),s=0;s<p;++s)i.point((d=g[s])[0],d[1]);i.lineEnd();return}m[0]+=2*_r}a.push(y=new Ni(d,g,null,!0)),o.push(y.o=new Ni(d,null,y,!1)),a.push(y=new Ni(m,g,null,!1)),o.push(y.o=new Ni(m,null,y,!0))}}),!!a.length){for(o.sort(n),Ac(a),Ac(o),s=0,l=o.length;s<l;++s)o[s].e=e=!e;for(var c=a[0],u,v;;){for(var f=c,h=!0;f.v;)if((f=f.n)===c)return;u=f.z,i.lineStart();do{if(f.v=f.o.v=!0,f.e){if(h)for(s=0,l=u.length;s<l;++s)i.point((v=u[s])[0],v[1]);else t(f.x,f.n.x,1,i);f=f.n}else{if(h)for(u=f.p.z,s=u.length-1;s>=0;--s)i.point((v=u[s])[0],v[1]);else t(f.x,f.p.x,-1,i);f=f.p}f=f.o,u=f.z,h=!h}while(!f.v);i.lineEnd()}}}function Ac(r){if(!!(n=r.length)){for(var n,e=0,t=r[0],i;++e<n;)t.n=i=r[e],i.p=t,t=i;t.n=i=r[0],i.p=t}}function nu(r){return Ir(r[0])<=cr?r[0]:Pn(r[0])*((Ir(r[0])+cr)%kn-cr)}function a5(r,n){var e=nu(n),t=n[1],i=Er(t),a=[Er(e),-Sr(e),0],o=0,s=0,l=new Zm;i===1?t=Br+_r:i===-1&&(t=-Br-_r);for(var c=0,u=r.length;c<u;++c)if(!!(f=(v=r[c]).length))for(var v,f,h=v[f-1],g=nu(h),p=h[1]/2+bc,d=Er(p),m=Sr(p),y=0;y<f;++y,g=S,d=x,m=I,h=E){var E=v[y],S=nu(E),$=E[1]/2+bc,x=Er($),I=Sr($),C=S-g,L=C>=0?1:-1,O=L*C,A=O>cr,k=d*x;if(l.add(ee(k*L*Er(O),m*I+k*Sr(O))),o+=A?C+L*kn:C,A^g>=e^S>=e){var U=Ua(ft(h),ft(E));cf(U);var _=Ua(a,U);cf(_);var w=(A^C>=0?-1:1)*we(_[2]);(t>w||t===w&&(U[0]||U[1]))&&(s+=A^C>=0?1:-1)}}return(o<-_r||o<_r&&l<-n5)^s&1}function Jv(r,n,e,t){return function(i){var a=n(i),o=jv(),s=n(o),l=!1,c,u,v,f={point:h,lineStart:p,lineEnd:d,polygonStart:function(){f.point=m,f.lineStart=y,f.lineEnd=E,u=[],c=[]},polygonEnd:function(){f.point=h,f.lineStart=p,f.lineEnd=d,u=Hv(u);var S=a5(c,t);u.length?(l||(i.polygonStart(),l=!0),Wv(u,u5,S,e,i)):S&&(l||(i.polygonStart(),l=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),l&&(i.polygonEnd(),l=!1),u=c=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function h(S,$){r(S,$)&&i.point(S,$)}function g(S,$){a.point(S,$)}function p(){f.point=g,a.lineStart()}function d(){f.point=h,a.lineEnd()}function m(S,$){v.push([S,$]),s.point(S,$)}function y(){s.lineStart(),v=[]}function E(){m(v[0][0],v[0][1]),s.lineEnd();var S=s.clean(),$=o.result(),x,I=$.length,C,L,O;if(v.pop(),c.push(v),v=null,!!I){if(S&1){if(L=$[0],(C=L.length-1)>0){for(l||(i.polygonStart(),l=!0),i.lineStart(),x=0;x<C;++x)i.point((O=L[x])[0],O[1]);i.lineEnd()}return}I>1&&S&2&&$.push($.pop().concat($.shift())),u.push($.filter(o5))}}return f}}function o5(r){return r.length>1}function u5(r,n){return((r=r.x)[0]<0?r[1]-Br-_r:Br-r[1])-((n=n.x)[0]<0?n[1]-Br-_r:Br-n[1])}var Dc=Jv(function(){return!0},f5,s5,[-cr,-Br]);function f5(r){var n=NaN,e=NaN,t=NaN,i;return{lineStart:function(){r.lineStart(),i=1},point:function(a,o){var s=a>0?cr:-cr,l=Ir(a-n);Ir(l-cr)<_r?(r.point(n,e=(e+o)/2>0?Br:-Br),r.point(t,e),r.lineEnd(),r.lineStart(),r.point(s,e),r.point(a,e),i=0):t!==s&&l>=cr&&(Ir(n-t)<_r&&(n-=t*_r),Ir(a-s)<_r&&(a-=s*_r),e=l5(n,e,a,o),r.point(t,e),r.lineEnd(),r.lineStart(),r.point(s,e),i=0),r.point(n=a,e=o),t=s},lineEnd:function(){r.lineEnd(),n=e=NaN},clean:function(){return 2-i}}}function l5(r,n,e,t){var i,a,o=Er(r-e);return Ir(o)>_r?gi((Er(n)*(a=Sr(t))*Er(e)-Er(t)*(i=Sr(n))*Er(r))/(i*a*o)):(n+t)/2}function s5(r,n,e,t){var i;if(r==null)i=e*Br,t.point(-cr,i),t.point(0,i),t.point(cr,i),t.point(cr,0),t.point(cr,-i),t.point(0,-i),t.point(-cr,-i),t.point(-cr,0),t.point(-cr,i);else if(Ir(r[0]-n[0])>_r){var a=r[0]<n[0]?cr:-cr;i=e*a/2,t.point(-a,i),t.point(0,i),t.point(a,i)}else t.point(n[0],n[1])}function c5(r){var n=Sr(r),e=6*zr,t=n>0,i=Ir(n)>_r;function a(u,v,f,h){i5(h,r,e,f,u,v)}function o(u,v){return Sr(u)*Sr(v)>n}function s(u){var v,f,h,g,p;return{lineStart:function(){g=h=!1,p=1},point:function(d,m){var y=[d,m],E,S=o(d,m),$=t?S?0:c(d,m):S?c(d+(d<0?cr:-cr),m):0;if(!v&&(g=h=S)&&u.lineStart(),S!==h&&(E=l(v,y),(!E||ta(v,E)||ta(y,E))&&(y[2]=1)),S!==h)p=0,S?(u.lineStart(),E=l(y,v),u.point(E[0],E[1])):(E=l(v,y),u.point(E[0],E[1],2),u.lineEnd()),v=E;else if(i&&v&&t^S){var x;!($&f)&&(x=l(y,v,!0))&&(p=0,t?(u.lineStart(),u.point(x[0][0],x[0][1]),u.point(x[1][0],x[1][1]),u.lineEnd()):(u.point(x[1][0],x[1][1]),u.lineEnd(),u.lineStart(),u.point(x[0][0],x[0][1],3)))}S&&(!v||!ta(v,y))&&u.point(y[0],y[1]),v=y,h=S,f=$},lineEnd:function(){h&&u.lineEnd(),v=null},clean:function(){return p|(g&&h)<<1}}}function l(u,v,f){var h=ft(u),g=ft(v),p=[1,0,0],d=Ua(h,g),m=Li(d,d),y=d[0],E=m-y*y;if(!E)return!f&&u;var S=n*m/E,$=-n*y/E,x=Ua(p,d),I=Ii(p,S),C=Ii(d,$);ru(I,C);var L=x,O=Li(I,L),A=Li(L,L),k=O*O-A*(Li(I,I)-1);if(!(k<0)){var U=te(k),_=Ii(L,(-O-U)/A);if(ru(_,I),_=sf(_),!f)return _;var w=u[0],b=v[0],P=u[1],D=v[1],rr;b<w&&(rr=w,w=b,b=rr);var hr=b-w,ur=Ir(hr-cr)<_r,gr=ur||hr<_r;if(!ur&&D<P&&(rr=P,P=D,D=rr),gr?ur?P+D>0^_[1]<(Ir(_[0]-w)<_r?P:D):P<=_[1]&&_[1]<=D:hr>cr^(w<=_[0]&&_[0]<=b)){var xr=Ii(L,(-O+U)/A);return ru(xr,I),[_,sf(xr)]}}}function c(u,v){var f=t?r:cr-r,h=0;return u<-f?h|=1:u>f&&(h|=2),v<-f?h|=4:v>f&&(h|=8),h}return Jv(o,s,a,t?[0,-r]:[-cr,r-cr])}function v5(r,n,e,t,i,a){var o=r[0],s=r[1],l=n[0],c=n[1],u=0,v=1,f=l-o,h=c-s,g;if(g=e-o,!(!f&&g>0)){if(g/=f,f<0){if(g<u)return;g<v&&(v=g)}else if(f>0){if(g>v)return;g>u&&(u=g)}if(g=i-o,!(!f&&g<0)){if(g/=f,f<0){if(g>v)return;g>u&&(u=g)}else if(f>0){if(g<u)return;g<v&&(v=g)}if(g=t-s,!(!h&&g>0)){if(g/=h,h<0){if(g<u)return;g<v&&(v=g)}else if(h>0){if(g>v)return;g>u&&(u=g)}if(g=a-s,!(!h&&g<0)){if(g/=h,h<0){if(g>v)return;g>u&&(u=g)}else if(h>0){if(g<u)return;g<v&&(v=g)}return u>0&&(r[0]=o+u*f,r[1]=s+u*h),v<1&&(n[0]=o+v*f,n[1]=s+v*h),!0}}}}}var Bt=1e9,Ai=-Bt;function h5(r,n,e,t){function i(c,u){return r<=c&&c<=e&&n<=u&&u<=t}function a(c,u,v,f){var h=0,g=0;if(c==null||(h=o(c,v))!==(g=o(u,v))||l(c,u)<0^v>0)do f.point(h===0||h===3?r:e,h>1?t:n);while((h=(h+v+4)%4)!==g);else f.point(u[0],u[1])}function o(c,u){return Ir(c[0]-r)<_r?u>0?0:3:Ir(c[0]-e)<_r?u>0?2:1:Ir(c[1]-n)<_r?u>0?1:0:u>0?3:2}function s(c,u){return l(c.x,u.x)}function l(c,u){var v=o(c,1),f=o(u,1);return v!==f?v-f:v===0?u[1]-c[1]:v===1?c[0]-u[0]:v===2?c[1]-u[1]:u[0]-c[0]}return function(c){var u=c,v=jv(),f,h,g,p,d,m,y,E,S,$,x,I={point:C,lineStart:k,lineEnd:U,polygonStart:O,polygonEnd:A};function C(w,b){i(w,b)&&u.point(w,b)}function L(){for(var w=0,b=0,P=h.length;b<P;++b)for(var D=h[b],rr=1,hr=D.length,ur=D[0],gr,xr,Ur=ur[0],jr=ur[1];rr<hr;++rr)gr=Ur,xr=jr,ur=D[rr],Ur=ur[0],jr=ur[1],xr<=t?jr>t&&(Ur-gr)*(t-xr)>(jr-xr)*(r-gr)&&++w:jr<=t&&(Ur-gr)*(t-xr)<(jr-xr)*(r-gr)&&--w;return w}function O(){u=v,f=[],h=[],x=!0}function A(){var w=L(),b=x&&w,P=(f=Hv(f)).length;(b||P)&&(c.polygonStart(),b&&(c.lineStart(),a(null,null,1,c),c.lineEnd()),P&&Wv(f,s,w,a,c),c.polygonEnd()),u=c,f=h=g=null}function k(){I.point=_,h&&h.push(g=[]),$=!0,S=!1,y=E=NaN}function U(){f&&(_(p,d),m&&S&&v.rejoin(),f.push(v.result())),I.point=C,S&&u.lineEnd()}function _(w,b){var P=i(w,b);if(h&&g.push([w,b]),$)p=w,d=b,m=P,$=!1,P&&(u.lineStart(),u.point(w,b));else if(P&&S)u.point(w,b);else{var D=[y=Math.max(Ai,Math.min(Bt,y)),E=Math.max(Ai,Math.min(Bt,E))],rr=[w=Math.max(Ai,Math.min(Bt,w)),b=Math.max(Ai,Math.min(Bt,b))];v5(D,rr,r,n,e,t)?(S||(u.lineStart(),u.point(D[0],D[1])),u.point(rr[0],rr[1]),P||u.lineEnd(),x=!1):P&&(u.lineStart(),u.point(w,b),x=!1)}y=w,E=b,S=P}return I}}var Oc=r=>r,lt=1/0,Ha=lt,ai=-lt,Ya=ai,g5={point:p5,lineStart:zt,lineEnd:zt,polygonStart:zt,polygonEnd:zt,result:function(){var r=[[lt,Ha],[ai,Ya]];return ai=Ya=-(Ha=lt=1/0),r}};function p5(r,n){r<lt&&(lt=r),r>ai&&(ai=r),n<Ha&&(Ha=n),n>Ya&&(Ya=n)}var qc=g5;function Vl(r){return function(n){var e=new gf;for(var t in r)e[t]=r[t];return e.stream=n,e}}function gf(){}gf.prototype={constructor:gf,point:function(r,n){this.stream.point(r,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function Xl(r,n,e){var t=r.clipExtent&&r.clipExtent();return r.scale(150).translate([0,0]),t!=null&&r.clipExtent(null),Vv(e,r.stream(qc)),n(qc.result()),t!=null&&r.clipExtent(t),r}function Kv(r,n,e){return Xl(r,function(t){var i=n[1][0]-n[0][0],a=n[1][1]-n[0][1],o=Math.min(i/(t[1][0]-t[0][0]),a/(t[1][1]-t[0][1])),s=+n[0][0]+(i-o*(t[1][0]+t[0][0]))/2,l=+n[0][1]+(a-o*(t[1][1]+t[0][1]))/2;r.scale(150*o).translate([s,l])},e)}function d5(r,n,e){return Kv(r,[[0,0],n],e)}function m5(r,n,e){return Xl(r,function(t){var i=+n,a=i/(t[1][0]-t[0][0]),o=(i-a*(t[1][0]+t[0][0]))/2,s=-a*t[0][1];r.scale(150*a).translate([o,s])},e)}function y5(r,n,e){return Xl(r,function(t){var i=+n,a=i/(t[1][1]-t[0][1]),o=-a*t[0][0],s=(i-a*(t[1][1]+t[0][1]))/2;r.scale(150*a).translate([o,s])},e)}var Fc=16,w5=Sr(30*zr);function zc(r,n){return+n?S5(r,n):E5(r)}function E5(r){return Vl({point:function(n,e){n=r(n,e),this.stream.point(n[0],n[1])}})}function S5(r,n){function e(t,i,a,o,s,l,c,u,v,f,h,g,p,d){var m=c-t,y=u-i,E=m*m+y*y;if(E>4*n&&p--){var S=o+f,$=s+h,x=l+g,I=te(S*S+$*$+x*x),C=we(x/=I),L=Ir(Ir(x)-1)<_r||Ir(a-v)<_r?(a+v)/2:ee($,S),O=r(L,C),A=O[0],k=O[1],U=A-t,_=k-i,w=y*U-m*_;(w*w/E>n||Ir((m*U+y*_)/E-.5)>.3||o*f+s*h+l*g<w5)&&(e(t,i,a,o,s,l,A,k,L,S/=I,$/=I,x,p,d),d.point(A,k),e(A,k,L,S,$,x,c,u,v,f,h,g,p,d))}}return function(t){var i,a,o,s,l,c,u,v,f,h,g,p,d={point:m,lineStart:y,lineEnd:S,polygonStart:function(){t.polygonStart(),d.lineStart=$},polygonEnd:function(){t.polygonEnd(),d.lineStart=y}};function m(C,L){C=r(C,L),t.point(C[0],C[1])}function y(){v=NaN,d.point=E,t.lineStart()}function E(C,L){var O=ft([C,L]),A=r(C,L);e(v,f,u,h,g,p,v=A[0],f=A[1],u=C,h=O[0],g=O[1],p=O[2],Fc,t),t.point(v,f)}function S(){d.point=m,t.lineEnd()}function $(){y(),d.point=x,d.lineEnd=I}function x(C,L){E(i=C,L),a=v,o=f,s=h,l=g,c=p,d.point=E}function I(){e(v,f,u,h,g,p,a,o,i,s,l,c,Fc,t),d.lineEnd=S,S()}return d}}var _5=Vl({point:function(r,n){this.stream.point(r*zr,n*zr)}});function T5(r){return Vl({point:function(n,e){var t=r(n,e);return this.stream.point(t[0],t[1])}})}function $5(r,n,e,t,i){function a(o,s){return o*=t,s*=i,[n+r*o,e-r*s]}return a.invert=function(o,s){return[(o-n)/r*t,(e-s)/r*i]},a}function Bc(r,n,e,t,i,a){if(!a)return $5(r,n,e,t,i);var o=Sr(a),s=Er(a),l=o*r,c=s*r,u=o/r,v=s/r,f=(s*e-o*n)/r,h=(s*n+o*e)/r;function g(p,d){return p*=t,d*=i,[l*p-c*d+n,e-c*p-l*d]}return g.invert=function(p,d){return[t*(u*p-v*d+f),i*(h-v*p-u*d)]},g}function jl(r){return Qv(function(){return r})()}function Qv(r){var n,e=150,t=480,i=250,a=0,o=0,s=0,l=0,c=0,u,v=0,f=1,h=1,g=null,p=Dc,d=null,m,y,E,S=Oc,$=.5,x,I,C,L,O;function A(w){return C(w[0]*zr,w[1]*zr)}function k(w){return w=C.invert(w[0],w[1]),w&&[w[0]*nn,w[1]*nn]}A.stream=function(w){return L&&O===w?L:L=_5(T5(u)(p(x(S(O=w)))))},A.preclip=function(w){return arguments.length?(p=w,g=void 0,_()):p},A.postclip=function(w){return arguments.length?(S=w,d=m=y=E=null,_()):S},A.clipAngle=function(w){return arguments.length?(p=+w?c5(g=w*zr):(g=null,Dc),_()):g*nn},A.clipExtent=function(w){return arguments.length?(S=w==null?(d=m=y=E=null,Oc):h5(d=+w[0][0],m=+w[0][1],y=+w[1][0],E=+w[1][1]),_()):d==null?null:[[d,m],[y,E]]},A.scale=function(w){return arguments.length?(e=+w,U()):e},A.translate=function(w){return arguments.length?(t=+w[0],i=+w[1],U()):[t,i]},A.center=function(w){return arguments.length?(a=w[0]%360*zr,o=w[1]%360*zr,U()):[a*nn,o*nn]},A.rotate=function(w){return arguments.length?(s=w[0]%360*zr,l=w[1]%360*zr,c=w.length>2?w[2]%360*zr:0,U()):[s*nn,l*nn,c*nn]},A.angle=function(w){return arguments.length?(v=w%360*zr,U()):v*nn},A.reflectX=function(w){return arguments.length?(f=w?-1:1,U()):f<0},A.reflectY=function(w){return arguments.length?(h=w?-1:1,U()):h<0},A.precision=function(w){return arguments.length?(x=zc(I,$=w*w),_()):te($)},A.fitExtent=function(w,b){return Kv(A,w,b)},A.fitSize=function(w,b){return d5(A,w,b)},A.fitWidth=function(w,b){return m5(A,w,b)},A.fitHeight=function(w,b){return y5(A,w,b)};function U(){var w=Bc(e,0,0,f,h,v).apply(null,n(a,o)),b=Bc(e,t-w[0],i-w[1],f,h,v);return u=Xv(s,l,c),I=vf(n,b),C=vf(u,I),x=zc(I,$),_()}function _(){return L=O=null,A}return function(){return n=r.apply(this,arguments),A.invert=n.invert&&k,U()}}function Wl(r){var n=0,e=cr/3,t=Qv(r),i=t(n,e);return i.parallels=function(a){return arguments.length?t(n=a[0]*zr,e=a[1]*zr):[n*nn,e*nn]},i}function M5(r){var n=Sr(r);function e(t,i){return[t*n,Er(i)/n]}return e.invert=function(t,i){return[t/n,we(i*n)]},e}function x5(r,n){var e=Er(r),t=(e+Er(n))/2;if(Ir(t)<_r)return M5(r);var i=1+e*(2*t-e),a=te(i)/t;function o(s,l){var c=te(i-2*t*Er(l))/t;return[c*Er(s*=t),a-c*Sr(s)]}return o.invert=function(s,l){var c=a-l,u=ee(s,Ir(c))*Pn(c);return c*t<0&&(u-=cr*Pn(s)*Pn(c)),[u/t,we((i-(s*s+c*c)*t*t)/(2*t))]},o}function Ze(){return Wl(x5).scale(155.424).center([0,33.6442])}function Va(){return Ze().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function b5(r){return function(n,e){var t=te(n*n+e*e),i=r(t),a=Er(i),o=Sr(i);return[ee(n*a,t*o),we(t&&e*a/t)]}}function So(r,n){return[r,Ba(Yl((Br+n)/2))]}So.invert=function(r,n){return[r,2*gi(Yv(n))-Br]};function Cr(){return Zv(So).scale(961/kn)}function Zv(r){var n=jl(r),e=n.center,t=n.scale,i=n.translate,a=n.clipExtent,o=null,s,l,c;n.scale=function(v){return arguments.length?(t(v),u()):t()},n.translate=function(v){return arguments.length?(i(v),u()):i()},n.center=function(v){return arguments.length?(e(v),u()):e()},n.clipExtent=function(v){return arguments.length?(v==null?o=s=l=c=null:(o=+v[0][0],s=+v[0][1],l=+v[1][0],c=+v[1][1]),u()):o==null?null:[[o,s],[l,c]]};function u(){var v=cr*t(),f=n(t5(n.rotate()).invert([0,0]));return a(o==null?[[f[0]-v,f[1]-v],[f[0]+v,f[1]+v]]:r===So?[[Math.max(f[0]-v,o),s],[Math.min(f[0]+v,l),c]]:[[o,Math.max(f[1]-v,s)],[l,Math.min(f[1]+v,c)]])}return u()}function Di(r){return Yl((Br+r)/2)}function P5(r,n){var e=Sr(r),t=r===n?Er(r):Ba(e/Sr(n))/Ba(Di(n)/Di(r)),i=e*Zo(Di(r),t)/t;if(!t)return So;function a(o,s){i>0?s<-Br+_r&&(s=-Br+_r):s>Br-_r&&(s=Br-_r);var l=i/Zo(Di(s),t);return[l*Er(t*o),i-l*Sr(t*o)]}return a.invert=function(o,s){var l=i-s,c=Pn(t)*te(o*o+l*l),u=ee(o,Ir(l))*Pn(l);return l*t<0&&(u-=cr*Pn(o)*Pn(l)),[u/t,2*gi(Zo(i/c,1/t))-Br]},a}function ln(){return Wl(P5).scale(109.5).parallels([30,30])}function Xa(r,n){return[r,n]}Xa.invert=Xa;function Gc(){return jl(Xa).scale(152.63)}function R5(r,n){var e=Sr(r),t=r===n?Er(r):(e-Sr(n))/(n-r),i=e/t+r;if(Ir(t)<_r)return Xa;function a(o,s){var l=i-s,c=t*o;return[l*Er(c),i-l*Sr(c)]}return a.invert=function(o,s){var l=i-s,c=ee(o,Ir(l))*Pn(l);return l*t<0&&(c-=cr*Pn(o)*Pn(l)),[c/t,i-Pn(t)*te(o*o+l*l)]},a}function eu(){return Wl(R5).scale(131.154).center([0,13.9389])}function rh(r,n){var e=Sr(n),t=1+Sr(r)*e;return[e*Er(r)/t,Er(n)/t]}rh.invert=b5(function(r){return 2*gi(r)});function k5(){return jl(rh).scale(250).clipAngle(142)}function nh(r,n){return[Ba(Yl((Br+n)/2)),-r]}nh.invert=function(r,n){return[-n,2*gi(Yv(r))-Br]};function C5(){var r=Zv(nh),n=r.center,e=r.rotate;return r.center=function(t){return arguments.length?n([-t[1],t[0]]):(t=n(),[t[1],-t[0]])},r.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):(t=e(),[t[0],t[1],t[2]-90])},e([0,0,90]).scale(159.155)}function Oi(){}var st=1/0,ja=st,oi=-st,Wa=oi,Uc={point:L5,lineStart:Oi,lineEnd:Oi,polygonStart:Oi,polygonEnd:Oi,result:function(){var r=[[st,ja],[oi,Wa]];return oi=Wa=-(ja=st=1/0),r}};function L5(r,n){r<st&&(st=r),r>oi&&(oi=r),n<ja&&(ja=n),n>Wa&&(Wa=n)}function gn(r,n,e){var t=n[1][0]-n[0][0],i=n[1][1]-n[0][1],a=r.clipExtent&&r.clipExtent();r.scale(150).translate([0,0]),a!=null&&r.clipExtent(null),Vv(e,r.stream(Uc));var o=Uc.result(),s=Math.min(t/(o[1][0]-o[0][0]),i/(o[1][1]-o[0][1])),l=+n[0][0]+(t-s*(o[1][0]+o[0][0]))/2,c=+n[0][1]+(i-s*(o[1][1]+o[0][1]))/2;return a!=null&&r.clipExtent(a),r.scale(s*150).translate([l,c])}function _n(r,n,e){return gn(r,[[0,0],n],e)}const pf=Math.PI,df=2*pf,ke=1e-6,I5=df-ke;function mf(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function pn(){return new mf}mf.prototype=pn.prototype={constructor:mf,moveTo:function(r,n){this._+="M"+(this._x0=this._x1=+r)+","+(this._y0=this._y1=+n)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(r,n){this._+="L"+(this._x1=+r)+","+(this._y1=+n)},quadraticCurveTo:function(r,n,e,t){this._+="Q"+ +r+","+ +n+","+(this._x1=+e)+","+(this._y1=+t)},bezierCurveTo:function(r,n,e,t,i,a){this._+="C"+ +r+","+ +n+","+ +e+","+ +t+","+(this._x1=+i)+","+(this._y1=+a)},arcTo:function(r,n,e,t,i){r=+r,n=+n,e=+e,t=+t,i=+i;var a=this._x1,o=this._y1,s=e-r,l=t-n,c=a-r,u=o-n,v=c*c+u*u;if(i<0)throw new Error("negative radius: "+i);if(this._x1===null)this._+="M"+(this._x1=r)+","+(this._y1=n);else if(v>ke)if(!(Math.abs(u*s-l*c)>ke)||!i)this._+="L"+(this._x1=r)+","+(this._y1=n);else{var f=e-a,h=t-o,g=s*s+l*l,p=f*f+h*h,d=Math.sqrt(g),m=Math.sqrt(v),y=i*Math.tan((pf-Math.acos((g+v-p)/(2*d*m)))/2),E=y/m,S=y/d;Math.abs(E-1)>ke&&(this._+="L"+(r+E*c)+","+(n+E*u)),this._+="A"+i+","+i+",0,0,"+ +(u*f>c*h)+","+(this._x1=r+S*s)+","+(this._y1=n+S*l)}},arc:function(r,n,e,t,i,a){r=+r,n=+n,e=+e,a=!!a;var o=e*Math.cos(t),s=e*Math.sin(t),l=r+o,c=n+s,u=1^a,v=a?t-i:i-t;if(e<0)throw new Error("negative radius: "+e);this._x1===null?this._+="M"+l+","+c:(Math.abs(this._x1-l)>ke||Math.abs(this._y1-c)>ke)&&(this._+="L"+l+","+c),e&&(v<0&&(v=v%df+df),v>I5?this._+="A"+e+","+e+",0,1,"+u+","+(r-o)+","+(n-s)+"A"+e+","+e+",0,1,"+u+","+(this._x1=l)+","+(this._y1=c):v>ke&&(this._+="A"+e+","+e+",0,"+ +(v>=pf)+","+u+","+(this._x1=r+e*Math.cos(i))+","+(this._y1=n+e*Math.sin(i))))},rect:function(r,n,e,t){this._+="M"+(this._x0=this._x1=+r)+","+(this._y0=this._y1=+n)+"h"+ +e+"v"+ +t+"h"+-e+"Z"},toString:function(){return this._}};function N5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function A5(){var r,n,e=Va(),t,i=Ze().rotate([154,0]).center([-2,58.5]).parallels([55,65]),a,o=Ze().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s,l,c={point:function(f,h){l=[f,h]}};function u(f){var h=f[0],g=f[1];return l=null,t.point(h,g),l||(a.point(h,g),l)||(s.point(h,g),l)}u.invert=function(f){var h=e.scale(),g=e.translate(),p=(f[0]-g[0])/h,d=(f[1]-g[1])/h;return(d>=.12&&d<.234&&p>=-.425&&p<-.214?i:d>=.166&&d<.234&&p>=-.214&&p<-.115?o:e).invert(f)},u.stream=function(f){return r&&n===f?r:r=N5([e.stream(n=f),i.stream(f),o.stream(f)])},u.precision=function(f){return arguments.length?(e.precision(f),i.precision(f),o.precision(f),v()):e.precision()},u.scale=function(f){return arguments.length?(e.scale(f),i.scale(f*.35),o.scale(f),u.translate(e.translate())):e.scale()},u.translate=function(f){if(!arguments.length)return e.translate();var h=e.scale(),g=+f[0],p=+f[1];return t=e.translate(f).clipExtent([[g-.455*h,p-.238*h],[g+.455*h,p+.238*h]]).stream(c),a=i.translate([g-.307*h,p+.201*h]).clipExtent([[g-.425*h+q,p+.12*h+q],[g-.214*h-q,p+.234*h-q]]).stream(c),s=o.translate([g-.205*h,p+.212*h]).clipExtent([[g-.214*h+q,p+.166*h+q],[g-.115*h-q,p+.234*h-q]]).stream(c),v()},u.fitExtent=function(f,h){return gn(u,f,h)},u.fitSize=function(f,h){return _n(u,f,h)};function v(){return r=n=null,u}return u.drawCompositionBorders=function(f){var h=e([-102.91,26.3]),g=e([-104,27.5]),p=e([-108,29.1]),d=e([-110,29.1]),m=e([-110,26.7]),y=e([-112.8,27.6]),E=e([-114.3,30.6]),S=e([-119.3,30.1]);f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.moveTo(m[0],m[1]),f.lineTo(y[0],y[1]),f.lineTo(E[0],E[1]),f.lineTo(S[0],S[1])},u.getCompositionBorders=function(){var f=pn();return this.drawCompositionBorders(f),f.toString()},u.scale(1070)}function D5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function O5(){var r,n,e=Va(),t,i=Ze().rotate([154,0]).center([-2,58.5]).parallels([55,65]),a,o=Ze().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s,l=Ze().rotate([66,0]).center([0,18]).parallels([8,18]),c,u=Gc().rotate([173,14]),v,f=Gc().rotate([-145,-16.8]),h,g,p={point:function(y,E){g=[y,E]}};function d(y){var E=y[0],S=y[1];return g=null,t.point(E,S),g||(a.point(E,S),g)||(s.point(E,S),g)||(c.point(E,S),g)||(v.point(E,S),g)||(h.point(E,S),g)}d.invert=function(y){var E=e.scale(),S=e.translate(),$=(y[0]-S[0])/E,x=(y[1]-S[1])/E;return(x>=.12&&x<.234&&$>=-.425&&$<-.214?i:x>=.166&&x<.234&&$>=-.214&&$<-.115?o:x>=.2064&&x<.2413&&$>=.312&&$<.385?l:x>=.09&&x<.1197&&$>=-.4243&&$<-.3232?u:x>=-.0518&&x<.0895&&$>=-.4243&&$<-.3824?f:e).invert(y)},d.stream=function(y){return r&&n===y?r:r=D5([e.stream(n=y),i.stream(y),o.stream(y),l.stream(y),u.stream(y),f.stream(y)])},d.precision=function(y){return arguments.length?(e.precision(y),i.precision(y),o.precision(y),l.precision(y),u.precision(y),f.precision(y),m()):e.precision()},d.scale=function(y){return arguments.length?(e.scale(y),i.scale(y*.35),o.scale(y),l.scale(y),u.scale(y*2),f.scale(y),d.translate(e.translate())):e.scale()},d.translate=function(y){if(!arguments.length)return e.translate();var E=e.scale(),S=+y[0],$=+y[1];return t=e.translate(y).clipExtent([[S-.455*E,$-.238*E],[S+.455*E,$+.238*E]]).stream(p),a=i.translate([S-.307*E,$+.201*E]).clipExtent([[S-.425*E+q,$+.12*E+q],[S-.214*E-q,$+.233*E-q]]).stream(p),s=o.translate([S-.205*E,$+.212*E]).clipExtent([[S-.214*E+q,$+.166*E+q],[S-.115*E-q,$+.233*E-q]]).stream(p),c=l.translate([S+.35*E,$+.224*E]).clipExtent([[S+.312*E+q,$+.2064*E+q],[S+.385*E-q,$+.233*E-q]]).stream(p),v=u.translate([S-.492*E,$+.09*E]).clipExtent([[S-.4243*E+q,$+.0903*E+q],[S-.3233*E-q,$+.1197*E-q]]).stream(p),h=f.translate([S-.408*E,$+.018*E]).clipExtent([[S-.4244*E+q,$-.0519*E+q],[S-.3824*E-q,$+.0895*E-q]]).stream(p),m()},d.fitExtent=function(y,E){return gn(d,y,E)},d.fitSize=function(y,E){return _n(d,y,E)};function m(){return r=n=null,d}return d.drawCompositionBorders=function(y){var E=e([-110.4641,28.2805]),S=e([-104.0597,28.9528]),$=e([-103.7049,25.1031]),x=e([-109.8337,24.4531]),I=e([-124.4745,28.1407]),C=e([-110.931,30.8844]),L=e([-109.8337,24.4531]),O=e([-122.4628,21.8562]),A=e([-76.8579,25.1544]),k=e([-72.429,24.2097]),U=e([-72.8265,22.7056]),_=e([-77.1852,23.6392]),w=e([-125.0093,29.7791]),b=e([-118.5193,31.3262]),P=e([-118.064,29.6912]),D=e([-124.4369,28.169]),rr=e([-128.1314,37.4582]),hr=e([-125.2132,38.214]),ur=e([-122.3616,30.5115]),gr=e([-125.0315,29.8211]);y.moveTo(E[0],E[1]),y.lineTo(S[0],S[1]),y.lineTo($[0],$[1]),y.lineTo($[0],$[1]),y.lineTo(x[0],x[1]),y.closePath(),y.moveTo(I[0],I[1]),y.lineTo(C[0],C[1]),y.lineTo(L[0],L[1]),y.lineTo(L[0],L[1]),y.lineTo(O[0],O[1]),y.closePath(),y.moveTo(A[0],A[1]),y.lineTo(k[0],k[1]),y.lineTo(U[0],U[1]),y.lineTo(U[0],U[1]),y.lineTo(_[0],_[1]),y.closePath(),y.moveTo(w[0],w[1]),y.lineTo(b[0],b[1]),y.lineTo(P[0],P[1]),y.lineTo(P[0],P[1]),y.lineTo(D[0],D[1]),y.closePath(),y.moveTo(rr[0],rr[1]),y.lineTo(hr[0],hr[1]),y.lineTo(ur[0],ur[1]),y.lineTo(ur[0],ur[1]),y.lineTo(gr[0],gr[1]),y.closePath()},d.getCompositionBorders=function(){var y=pn();return this.drawCompositionBorders(y),y.toString()},d.scale(1070)}function q5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function F5(){var r,n,e=ln().rotate([5,-38.6]).parallels([0,60]),t,i=ln().rotate([5,-38.6]).parallels([0,60]),a,o,s={point:function(u,v){o=[u,v]}};function l(u){var v=u[0],f=u[1];return o=null,t.point(v,f),o||(a.point(v,f),o)}l.invert=function(u){var v=e.scale(),f=e.translate(),h=(u[0]-f[0])/v,g=(u[1]-f[1])/v;return(g>=.05346&&g<.0897&&h>=-.13388&&h<-.0322?i:e).invert(u)},l.stream=function(u){return r&&n===u?r:r=q5([e.stream(n=u),i.stream(u)])},l.precision=function(u){return arguments.length?(e.precision(u),i.precision(u),c()):e.precision()},l.scale=function(u){return arguments.length?(e.scale(u),i.scale(u),l.translate(e.translate())):e.scale()},l.translate=function(u){if(!arguments.length)return e.translate();var v=e.scale(),f=+u[0],h=+u[1];return t=e.translate(u).clipExtent([[f-.06857*v,h-.1288*v],[f+.13249*v,h+.06*v]]).stream(s),a=i.translate([f+.1*v,h-.094*v]).clipExtent([[f-.1331*v+q,h+.053457*v+q],[f-.0354*v-q,h+.08969*v-q]]).stream(s),c()},l.fitExtent=function(u,v){return gn(l,u,v)},l.fitSize=function(u,v){return _n(l,u,v)};function c(){return r=n=null,l}return l.drawCompositionBorders=function(u){var v=e([-14.034675,34.965007]),f=e([-7.4208899,35.536988]),h=e([-7.3148275,33.54359]);u.moveTo(v[0],v[1]),u.lineTo(f[0],f[1]),u.lineTo(h[0],h[1])},l.getCompositionBorders=function(){var u=pn();return this.drawCompositionBorders(u),u.toString()},l.scale(2700)}function z5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function B5(){var r,n,e=ln().rotate([10,-39.3]).parallels([0,60]),t,i=ln().rotate([17,-32.7]).parallels([0,60]),a,o=ln().rotate([27.8,-38.6]).parallels([0,60]),s,l,c={point:function(f,h){l=[f,h]}};function u(f){var h=f[0],g=f[1];return l=null,t.point(h,g),l||(a.point(h,g),l)||(s.point(h,g),l)}u.invert=function(f){var h=e.scale(),g=e.translate(),p=(f[0]-g[0])/h,d=(f[1]-g[1])/h;return(d>=.0093&&d<.03678&&p>=-.03875&&p<-.0116?i:d>=-.0412&&d<.0091&&p>=-.07782&&p<-.01166?o:e).invert(f)},u.stream=function(f){return r&&n===f?r:r=z5([e.stream(n=f),i.stream(f),o.stream(f)])},u.precision=function(f){return arguments.length?(e.precision(f),i.precision(f),o.precision(f),v()):e.precision()},u.scale=function(f){return arguments.length?(e.scale(f),i.scale(f),o.scale(f*.6),u.translate(e.translate())):e.scale()},u.translate=function(f){if(!arguments.length)return e.translate();var h=e.scale(),g=+f[0],p=+f[1];return t=e.translate(f).clipExtent([[g-.0115*h,p-.1138*h],[g+.2105*h,p+.0673*h]]).stream(c),a=i.translate([g-.0265*h,p+.025*h]).clipExtent([[g-.0388*h+q,p+.0093*h+q],[g-.0116*h-q,p+.0368*h-q]]).stream(c),s=o.translate([g-.045*h,p+-.02*h]).clipExtent([[g-.0778*h+q,p-.0413*h+q],[g-.0117*h-q,p+.0091*h-q]]).stream(c),v()},u.fitExtent=function(f,h){return gn(u,f,h)},u.fitSize=function(f,h){return _n(u,f,h)};function v(){return r=n=null,u}return u.drawCompositionBorders=function(f){var h=e([-12.8351,38.7113]),g=e([-10.8482,38.7633]),p=e([-10.8181,37.2072]),d=e([-12.7345,37.1573]),m=e([-16.0753,41.4436]),y=e([-10.9168,41.6861]),E=e([-10.8557,38.7747]),S=e([-15.6728,38.5505]);f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.closePath(),f.moveTo(m[0],m[1]),f.lineTo(y[0],y[1]),f.lineTo(E[0],E[1]),f.lineTo(E[0],E[1]),f.lineTo(S[0],S[1]),f.closePath()},u.getCompositionBorders=function(){var f=pn();return this.drawCompositionBorders(f),f.toString()},u.scale(4200)}function G5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function U5(){var r,n,e=Cr().rotate([80,1.5]),t,i=Cr().rotate([90.73,1]),a,o,s={point:function(u,v){o=[u,v]}};function l(u){var v=u[0],f=u[1];return o=null,t.point(v,f),o||(a.point(v,f),o)}l.invert=function(u){var v=e.scale(),f=e.translate(),h=(u[0]-f[0])/v,g=(u[1]-f[1])/v;return(g>=-.0676&&g<-.026&&h>=-.0857&&h<-.0263?i:e).invert(u)},l.stream=function(u){return r&&n===u?r:r=G5([e.stream(n=u),i.stream(u)])},l.precision=function(u){return arguments.length?(e.precision(u),i.precision(u),c()):e.precision()},l.scale=function(u){return arguments.length?(e.scale(u),i.scale(u),l.translate(e.translate())):e.scale()},l.translate=function(u){if(!arguments.length)return e.translate();var v=e.scale(),f=+u[0],h=+u[1];return t=e.translate(u).clipExtent([[f-.0262*v,h-.0734*v],[f+.1741*v,h+.079*v]]).stream(s),a=i.translate([f-.06*v,h-.04*v]).clipExtent([[f-.0857*v+q,h-.0676*v+q],[f-.0263*v-q,h-.026*v-q]]).stream(s),c()},l.fitExtent=function(u,v){return gn(l,u,v)},l.fitSize=function(u,v){return _n(l,u,v)};function c(){return r=n=null,l}return l.drawCompositionBorders=function(u){var v=e([-84.9032,2.3757]),f=e([-81.5047,2.3708]),h=e([-81.5063,-.01]),g=e([-84.9086,-.005]);u.moveTo(v[0],v[1]),u.lineTo(f[0],f[1]),u.lineTo(h[0],h[1]),u.lineTo(g[0],g[1]),u.closePath()},l.getCompositionBorders=function(){var u=pn();return this.drawCompositionBorders(u),u.toString()},l.scale(3500)}function H5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function Y5(){var r,n,e=C5().rotate([72,37]),t,i=k5().rotate([72,0]),a,o=Cr().rotate([80,33.5]),s,l=Cr().rotate([110,25]),c,u,v={point:function(g,p){u=[g,p]}};function f(g){var p=g[0],d=g[1];return u=null,t.point(p,d),u||(a.point(p,d),u)||(s.point(p,d),u)||(c.point(p,d),u)}f.invert=function(g){var p=e.scale(),d=e.translate(),m=(g[0]-d[0])/p,y=(g[1]-d[1])/p;return(y>=.2582&&y<.32&&m>=-.1036&&m<-.087?i:y>=-.01298&&y<.0133&&m>=-.11396&&m<-.05944?o:y>=.01539&&y<.03911&&m>=-.089&&m<-.0588?l:e).invert(g)},f.stream=function(g){return r&&n===g?r:r=H5([e.stream(n=g),i.stream(g),o.stream(g),l.stream(g)])},f.precision=function(g){return arguments.length?(e.precision(g),i.precision(g),o.precision(g),l.precision(g),h()):e.precision()},f.scale=function(g){return arguments.length?(e.scale(g),i.scale(g*.15),o.scale(g*1.5),l.scale(g*1.5),f.translate(e.translate())):e.scale()},f.translate=function(g){if(!arguments.length)return e.translate();var p=e.scale(),d=+g[0],m=+g[1];return t=e.translate(g).clipExtent([[d-.059*p,m-.3835*p],[d+.4498*p,m+.3375*p]]).stream(v),a=i.translate([d-.087*p,m+.17*p]).clipExtent([[d-.1166*p+q,m+.2582*p+q],[d-.06*p-q,m+.32*p-q]]).stream(v),s=o.translate([d-.092*p,m-0*p]).clipExtent([[d-.114*p+q,m-.013*p+q],[d-.0594*p-q,m+.0133*p-q]]).stream(v),c=l.translate([d-.089*p,m-.0265*p]).clipExtent([[d-.089*p+q,m+.0154*p+q],[d-.0588*p-q,m+.0391*p-q]]).stream(v),h()},f.fitExtent=function(g,p){return gn(f,g,p)},f.fitSize=function(g,p){return _n(f,g,p)};function h(){return r=n=null,f}return f.drawCompositionBorders=function(g){var p=e([-82.6999,-51.3043]),d=e([-77.5442,-51.6631]),m=e([-78.0254,-55.186]),y=e([-83.6106,-54.7785]),E=e([-80.0638,-35.984]),S=e([-76.2153,-36.1811]),$=e([-76.2994,-37.6839]),x=e([-80.2231,-37.4757]),I=e([-78.442,-37.706]),C=e([-76.263,-37.8054]),L=e([-76.344,-39.1595]),O=e([-78.5638,-39.0559]);g.moveTo(p[0],p[1]),g.lineTo(d[0],d[1]),g.lineTo(m[0],m[1]),g.lineTo(m[0],m[1]),g.lineTo(y[0],y[1]),g.closePath(),g.moveTo(E[0],E[1]),g.lineTo(S[0],S[1]),g.lineTo($[0],$[1]),g.lineTo($[0],$[1]),g.lineTo(x[0],x[1]),g.closePath(),g.moveTo(I[0],I[1]),g.lineTo(C[0],C[1]),g.lineTo(L[0],L[1]),g.lineTo(L[0],L[1]),g.lineTo(O[0],O[1]),g.closePath()},f.getCompositionBorders=function(){var g=pn();return this.drawCompositionBorders(g),g.toString()},f.scale(700)}function V5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function X5(){var r,n,e=eu().rotate([-136,-22]).parallels([40,34]),t,i=eu().rotate([-146,-26]).parallels([40,34]),a,o=eu().rotate([-126,-19]).parallels([40,34]),s,l,c={point:function(f,h){l=[f,h]}};function u(f){var h=f[0],g=f[1];return l=null,t.point(h,g),l||(a.point(h,g),l)||(s.point(h,g),l)}u.invert=function(f){var h=e.scale(),g=e.translate(),p=(f[0]-g[0])/h,d=(f[1]-g[1])/h;return(d>=-.10925&&d<-.02701&&p>=-.135&&p<-.0397?i:d>=.04713&&d<.11138&&p>=-.03986&&p<.051?o:e).invert(f)},u.stream=function(f){return r&&n===f?r:r=V5([e.stream(n=f),i.stream(f),o.stream(f)])},u.precision=function(f){return arguments.length?(e.precision(f),i.precision(f),o.precision(f),v()):e.precision()},u.scale=function(f){return arguments.length?(e.scale(f),i.scale(f),o.scale(f*.7),u.translate(e.translate())):e.scale()},u.translate=function(f){if(!arguments.length)return e.translate();var h=e.scale(),g=+f[0],p=+f[1];return t=e.translate(f).clipExtent([[g-.1352*h,p-.1091*h],[g+.117*h,p+.098*h]]).stream(c),a=i.translate([g-.0425*h,p-.005*h]).clipExtent([[g-.135*h+q,p-.1093*h+q],[g-.0397*h-q,p-.027*h-q]]).stream(c),s=o.translate(f).clipExtent([[g-.0399*h+q,p+.0471*h+q],[g+.051*h-q,p+.1114*h-q]]).stream(c),v()},u.fitExtent=function(f,h){return gn(u,f,h)},u.fitSize=function(f,h){return _n(u,f,h)};function v(){return r=n=null,u}return u.drawCompositionBorders=function(f){var h=e([126.01320483689143,41.621090310215585]),g=e([133.04304387025903,42.15087523707186]),p=e([133.3021766080688,37.43975444725098]),d=e([126.87889168628224,36.95488945159779]),m=e([132.9,29.8]),y=e([134,33]),E=e([139.3,33.2]),S=e([139.16,30.5]);f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.closePath(),f.moveTo(m[0],m[1]),f.lineTo(y[0],y[1]),f.lineTo(E[0],E[1]),f.lineTo(S[0],S[1])},u.getCompositionBorders=function(){var f=pn();return this.drawCompositionBorders(f),f.toString()},u.scale(2200)}function j5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function W5(){var r,n,e=ln().rotate([-3,-46.2]).parallels([0,60]),t,i=Cr().center([-53.2,3.9]),a,o=Cr().center([-61.03,14.67]),s,l=Cr().center([-61.46,16.14]),c,u=Cr().center([-62.85,17.92]),v,f=Cr().center([-56.23,46.93]),h,g=Cr().center([45.16,-12.8]),p,d=Cr().center([55.52,-21.13]),m,y=Cr().center([165.8,-21.07]),E,S=Cr().center([-178.1,-14.3]),$,x=Cr().center([-150.55,-17.11]),I,C=Cr().center([-150.55,-17.11]),L,O,A={point:function(_,w){O=[_,w]}};function k(_){var w=_[0],b=_[1];return O=null,t.point(w,b),O||(a.point(w,b),O)||(s.point(w,b),O)||(c.point(w,b),O)||(v.point(w,b),O)||(h.point(w,b),O)||(p.point(w,b),O)||(m.point(w,b),O)||(E.point(w,b),O)||($.point(w,b),O)||(I.point(w,b),O)||(L.point(w,b),O)}k.invert=function(_){var w=e.scale(),b=e.translate(),P=(_[0]-b[0])/w,D=(_[1]-b[1])/w;return(D>=.029&&D<.0864&&P>=-.14&&P<-.0996?i:D>=0&&D<.029&&P>=-.14&&P<-.0996?o:D>=-.032&&D<0&&P>=-.14&&P<-.0996?l:D>=-.052&&D<-.032&&P>=-.14&&P<-.0996?u:D>=-.076&&D<.052&&P>=-.14&&P<-.0996?f:D>=-.076&&D<-.052&&P>=.0967&&P<.1371?g:D>=-.052&&D<-.02&&P>=.0967&&P<.1371?d:D>=-.02&&D<.012&&P>=.0967&&P<.1371?y:D>=.012&&D<.033&&P>=.0967&&P<.1371?S:D>=.033&&D<.0864&&P>=.0967&&P<.1371?x:e).invert(_)},k.stream=function(_){return r&&n===_?r:r=j5([e.stream(n=_),i.stream(_),o.stream(_),l.stream(_),u.stream(_),f.stream(_),g.stream(_),d.stream(_),y.stream(_),S.stream(_),x.stream(_),C.stream(_)])},k.precision=function(_){return arguments.length?(e.precision(_),i.precision(_),o.precision(_),l.precision(_),u.precision(_),f.precision(_),g.precision(_),d.precision(_),y.precision(_),S.precision(_),x.precision(_),C.precision(_),U()):e.precision()},k.scale=function(_){return arguments.length?(e.scale(_),i.scale(_*.6),o.scale(_*1.6),l.scale(_*1.4),u.scale(_*5),f.scale(_*1.3),g.scale(_*1.6),d.scale(_*1.2),y.scale(_*.3),S.scale(_*2.7),x.scale(_*.5),C.scale(_*.06),k.translate(e.translate())):e.scale()},k.translate=function(_){if(!arguments.length)return e.translate();var w=e.scale(),b=+_[0],P=+_[1];return t=e.translate(_).clipExtent([[b-.0996*w,P-.0908*w],[b+.0967*w,P+.0864*w]]).stream(A),a=i.translate([b-.12*w,P+.0575*w]).clipExtent([[b-.14*w+q,P+.029*w+q],[b-.0996*w-q,P+.0864*w-q]]).stream(A),s=o.translate([b-.12*w,P+.013*w]).clipExtent([[b-.14*w+q,P+0*w+q],[b-.0996*w-q,P+.029*w-q]]).stream(A),c=l.translate([b-.12*w,P-.014*w]).clipExtent([[b-.14*w+q,P-.032*w+q],[b-.0996*w-q,P+0*w-q]]).stream(A),v=u.translate([b-.12*w,P-.044*w]).clipExtent([[b-.14*w+q,P-.052*w+q],[b-.0996*w-q,P-.032*w-q]]).stream(A),h=f.translate([b-.12*w,P-.065*w]).clipExtent([[b-.14*w+q,P-.076*w+q],[b-.0996*w-q,P-.052*w-q]]).stream(A),p=g.translate([b+.117*w,P-.064*w]).clipExtent([[b+.0967*w+q,P-.076*w+q],[b+.1371*w-q,P-.052*w-q]]).stream(A),m=d.translate([b+.116*w,P-.0355*w]).clipExtent([[b+.0967*w+q,P-.052*w+q],[b+.1371*w-q,P-.02*w-q]]).stream(A),E=y.translate([b+.116*w,P-.0048*w]).clipExtent([[b+.0967*w+q,P-.02*w+q],[b+.1371*w-q,P+.012*w-q]]).stream(A),$=S.translate([b+.116*w,P+.022*w]).clipExtent([[b+.0967*w+q,P+.012*w+q],[b+.1371*w-q,P+.033*w-q]]).stream(A),L=C.translate([b+.11*w,P+.045*w]).clipExtent([[b+.0967*w+q,P+.033*w+q],[b+.1371*w-q,P+.06*w-q]]).stream(A),I=x.translate([b+.115*w,P+.075*w]).clipExtent([[b+.0967*w+q,P+.06*w+q],[b+.1371*w-q,P+.0864*w-q]]).stream(A),U()},k.fitExtent=function(_,w){return gn(k,_,w)},k.fitSize=function(_,w){return _n(k,_,w)};function U(){return r=n=null,k}return k.drawCompositionBorders=function(_){var w,b,P,D;w=e([-7.938886725111036,43.7219460918835]),b=e([-4.832080896458295,44.12930268549372]),P=e([-4.205299743793263,40.98096346967365]),D=e([-7.071796453126152,40.610037319181444]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([-8.42751373617692,45.32889452553031]),b=e([-5.18599305777107,45.7566442062976]),P=e([-4.832080905154431,44.129302726751426]),D=e([-7.938886737126192,43.72194613263854]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([-9.012656899657046,47.127733821030176]),b=e([-5.6105244772793155,47.579777861410626]),P=e([-5.185993067168585,45.756644248170346]),D=e([-8.427513749141811,45.32889456686326]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([-9.405747558985553,48.26506375557457]),b=e([-5.896175018439575,48.733352850851624]),P=e([-5.610524487556043,47.57977790393761]),D=e([-9.012656913808351,47.127733862971255]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([-9.908436061346974,49.642448789505856]),b=e([-6.262026716233124,50.131426841787174]),P=e([-5.896175029331232,48.73335289377258]),D=e([-9.40574757396393,48.26506379787767]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([11.996907706504462,50.16039028163579]),b=e([15.649907879773343,49.68279246765253]),P=e([15.156712840526632,48.30371557625831]),D=e([11.64122661754411,48.761078240546816]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([11.641226606955788,48.7610781975889]),b=e([15.156712825832164,48.30371553390465]),P=e([14.549932166241172,46.4866532486199]),D=e([11.204443787952183,46.91899233914248]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([11.204443778297161,46.918992296823646]),b=e([14.549932152815039,46.486653206856396]),P=e([13.994409796764009,44.695833444323256]),D=e([10.805306599253848,45.105133870684924]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([10.805306590412085,45.10513382903308]),b=e([13.99440978444733,44.695833403183606]),P=e([13.654633799024392,43.53552468558152]),D=e([10.561516803980956,43.930671459798624]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([10.561516795617383,43.93067141859757]),b=e([13.654633787361952,43.5355246448671]),P=e([12.867691604239901,40.640701985019405]),D=e([9.997809515987688,41.00288343254471]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([10.8,42.4]),b=e([12.8,42.13]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1])},k.getCompositionBorders=function(){var _=pn();return this.drawCompositionBorders(_),_.toString()},k.scale(2700)}function J5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function K5(){var r,n,e=ln().rotate([-10,-53]).parallels([0,60]),t,i=Cr().center([-61.46,16.14]),a,o=Cr().center([-53.2,3.9]),s,l=ln().rotate([27.8,-38.9]).parallels([0,60]),c,u=ln().rotate([25.43,-37.398]).parallels([0,60]),v,f=ln().rotate([31.17,-39.539]).parallels([0,60]),h,g=ln().rotate([17,-32.7]).parallels([0,60]),p,d=ln().rotate([16,-28.5]).parallels([0,60]),m,y=Cr().center([-61.03,14.67]),E,S=Cr().center([45.16,-12.8]),$,x=Cr().center([55.52,-21.13]),I,C=ln().rotate([-14.4,-35.95]).parallels([0,60]),L,O,A={point:function(_,w){O=[_,w]}};function k(_){var w=_[0],b=_[1];return O=null,t.point(w,b),O||(s.point(w,b),O)||(E.point(w,b),O)||(a.point(w,b),O)||(m.point(w,b),O)||(p.point(w,b),O)||($.point(w,b),O)||(I.point(w,b),O)||(L.point(w,b),O)||(c.point(w,b),O)||(v.point(w,b),O)||(h.point(w,b),O)}k.invert=function(_){var w=e.scale(),b=e.translate(),P=(_[0]-(b[0]+.08*w))/w,D=(_[1]-b[1])/w;return(D>=-.31&&D<-.24&&P>=.14&&P<.24?i:D>=-.24&&D<-.17&&P>=.14&&P<.24?o:D>=-.17&&D<-.12&&P>=.21&&P<.24?u:D>=-.17&&D<-.14&&P>=.14&&P<.165?f:D>=-.17&&D<-.1&&P>=.14&&P<.24?l:D>=-.1&&D<-.03&&P>=.14&&P<.24?g:D>=-.03&&D<.04&&P>=.14&&P<.24?d:D>=-.31&&D<-.24&&P>=.24&&P<.34?y:D>=-.24&&D<-.17&&P>=.24&&P<.34?S:D>=-.17&&D<-.1&&P>=.24&&P<.34?x:D>=-.1&&D<-.03&&P>=.24&&P<.34?C:e).invert(_)},k.stream=function(_){return r&&n===_?r:r=J5([e.stream(n=_),o.stream(_),y.stream(_),i.stream(_),d.stream(_),g.stream(_),S.stream(_),x.stream(_),C.stream(_),l.stream(_),u.stream(_),f.stream(_)])},k.precision=function(_){return arguments.length?(e.precision(_),o.precision(_),y.precision(_),i.precision(_),d.precision(_),g.precision(_),S.precision(_),x.precision(_),C.precision(_),l.precision(_),u.precision(_),f.precision(_),U()):e.precision()},k.scale=function(_){return arguments.length?(e.scale(_),i.scale(_*3),o.scale(_*.8),y.scale(_*3.5),x.scale(_*2.7),l.scale(_*2),u.scale(_*2),f.scale(_*2),g.scale(_*3),d.scale(_),S.scale(_*5.5),C.scale(_*6),k.translate(e.translate())):e.scale()},k.translate=function(_){if(!arguments.length)return e.translate();var w=e.scale(),b=+_[0],P=+_[1];return t=e.translate([b-.08*w,P]).clipExtent([[b-.51*w,P-.33*w],[b+.5*w,P+.33*w]]).stream(A),a=i.translate([b+.19*w,P-.275*w]).clipExtent([[b+.14*w+q,P-.31*w+q],[b+.24*w-q,P-.24*w-q]]).stream(A),s=o.translate([b+.19*w,P-.205*w]).clipExtent([[b+.14*w+q,P-.24*w+q],[b+.24*w-q,P-.17*w-q]]).stream(A),c=l.translate([b+.19*w,P-.135*w]).clipExtent([[b+.14*w+q,P-.17*w+q],[b+.24*w-q,P-.1*w-q]]).stream(A),v=u.translate([b+.225*w,P-.147*w]).clipExtent([[b+.21*w+q,P-.17*w+q],[b+.24*w-q,P-.12*w-q]]).stream(A),h=f.translate([b+.153*w,P-.15*w]).clipExtent([[b+.14*w+q,P-.17*w+q],[b+.165*w-q,P-.14*w-q]]).stream(A),p=g.translate([b+.19*w,P-.065*w]).clipExtent([[b+.14*w+q,P-.1*w+q],[b+.24*w-q,P-.03*w-q]]).stream(A),m=d.translate([b+.19*w,P+.005*w]).clipExtent([[b+.14*w+q,P-.03*w+q],[b+.24*w-q,P+.04*w-q]]).stream(A),E=y.translate([b+.29*w,P-.275*w]).clipExtent([[b+.24*w+q,P-.31*w+q],[b+.34*w-q,P-.24*w-q]]).stream(A),$=S.translate([b+.29*w,P-.205*w]).clipExtent([[b+.24*w+q,P-.24*w+q],[b+.34*w-q,P-.17*w-q]]).stream(A),I=x.translate([b+.29*w,P-.135*w]).clipExtent([[b+.24*w+q,P-.17*w+q],[b+.34*w-q,P-.1*w-q]]).stream(A),L=C.translate([b+.29*w,P-.065*w]).clipExtent([[b+.24*w+q,P-.1*w+q],[b+.34*w-q,P-.03*w-q]]).stream(A),U()},k.fitExtent=function(_,w){return gn(k,_,w)},k.fitSize=function(_,w){return _n(k,_,w)};function U(){return r=n=null,k}return k.drawCompositionBorders=function(_){var w,b,P,D;w=e([42.45755610828648,63.343658547914934]),b=e([52.65837266667029,59.35045080290929]),P=e([47.19754502247785,56.12653496548117]),D=e([37.673034273363044,59.61638268506111]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([59.41110754003403,62.35069727399336]),b=e([66.75050228640794,57.11797303636038]),P=e([60.236065725110436,54.63331433818992]),D=e([52.65837313153311,59.350450804599355]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([48.81091130080243,66.93353402634641]),b=e([59.41110730654679,62.35069740653086]),P=e([52.6583728974441,59.3504509222445]),D=e([42.45755631675751,63.34365868805821]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([31.054198418446475,52.1080673766184]),b=e([39.09869284884117,49.400700047190554]),P=e([36.0580811499175,46.02944174908498]),D=e([28.690508588835726,48.433126979386415]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([33.977877745912025,55.849945501331]),b=e([42.75328432167726,52.78455122462353]),P=e([39.09869297540224,49.400700176148625]),D=e([31.05419851807008,52.10806751810923]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([52.658372900759296,59.35045068526415]),b=e([60.23606549583304,54.63331423800264]),P=e([54.6756370953122,51.892298789399455]),D=e([47.19754524788189,56.126534861222794]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([47.19754506082455,56.126534735591456]),b=e([54.675636900123514,51.892298681337095]),P=e([49.94448648951486,48.98775484983285]),D=e([42.75328468716108,52.78455126060818]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([42.75328453416769,52.78455113209101]),b=e([49.94448632339758,48.98775473706457]),P=e([45.912339990394315,45.99361784987003]),D=e([39.09869317356607,49.40070009378711]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([37.673034114296634,59.61638254183119]),b=e([47.197544835420544,56.126534839849846]),P=e([42.75328447467064,52.78455135314068]),D=e([33.977877870363905,55.849945644671145]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([44.56748486446032,57.26489367845818]),P=e([43.9335791193588,53.746540942601726]),D=e([43,56]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath(),w=e([37.673034114296634,59.61638254183119]),b=e([40.25902691953466,58.83002044222639]),P=e([38.458270492742024,57.26232178028002]),D=e([35.97754948030156,58.00266637992386]),_.moveTo(w[0],w[1]),_.lineTo(b[0],b[1]),_.lineTo(P[0],P[1]),_.lineTo(D[0],D[1]),_.closePath()},k.getCompositionBorders=function(){var _=pn();return this.drawCompositionBorders(_),_.toString()},k.scale(750)}function Q5(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function Z5(){var r,n,e=ln().rotate([-5.5,-52.2]).parallels([0,60]),t,i=Cr().center([-68.25,12.2]),a,o=Cr().center([-63.1,17.5]),s,l,c={point:function(f,h){l=[f,h]}};function u(f){const[h,g]=f;return l=null,t.point(h,g),l||(a.point(h,g),l)||(s.point(h,g),l)}u.invert=function(f){var h=e.scale(),g=e.translate(),p=(f[0]-g[0])/h,d=(f[1]-g[1])/h;return(d>=-.0067&&d<.0015&&p>=-.0232&&p<-.0154?i:d>=-.022&&d<-.014&&p>=-.023&&p<-.014?o:e).invert(f)},u.stream=function(f){return r&&n===f?r:r=Q5([e.stream(n=f),i.stream(f),o.stream(f)])},u.precision=function(f){return arguments.length?(e.precision(f),i.precision(f),o.precision(f),v()):e.precision()},u.scale=function(f){return arguments.length?(e.scale(f),i.scale(f),o.scale(f),u.translate(e.translate())):e.scale()},u.translate=function(f){if(!arguments.length)return e.translate();const h=e.scale(),g=+f[0],p=+f[1];return t=e.translate(f).clipExtent([[g-.0245*h,p-.026*h],[g+.023*h,p+.026*h]]).stream(c),a=i.translate([g-.0186*h,p-.00325*h]).clipExtent([[g-.0232*h+q,p-.0067*h+q],[g-.0154*h-q,p+.0015*h-q]]).stream(c),s=o.translate([g-.0185*h,p-.017*h]).clipExtent([[g-.023*h+q,p-.022*h+q],[g-.014*h-q,p-.014*h-q]]).stream(c),v()},u.fitExtent=function(f,h){return gn(u,f,h)},u.fitSize=function(f,h){return _n(u,f,h)};function v(){return r=n=null,u}return u.drawCompositionBorders=function(f){var h=e([3.30573,52.5562]),g=e([4.043,52.572]),p=e([4.0646,52.1017]),d=e([3.3382,52.0861]),m=e([3.262,53.439]),y=e([4.1373,53.4571]),E=e([4.1574,52.9946]),S=e([3.2951,52.9768]);f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.closePath(),f.moveTo(m[0],m[1]),f.lineTo(y[0],y[1]),f.lineTo(E[0],E[1]),f.lineTo(E[0],E[1]),f.lineTo(S[0],S[1]),f.closePath()},u.getCompositionBorders=function(){var f=pn();return this.drawCompositionBorders(f),f.toString()},u.scale(4200)}function ry(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function ny(){var r,n,e=Cr().center([105.25,4]),t,i=Cr().center([118.65,2.86]),a,o,s={point:function(u,v){o=[u,v]}};function l(u){var v=u[0],f=u[1];return o=null,t.point(v,f),o||(a.point(v,f),o)}l.invert=function(u){var v=e.scale(),f=e.translate(),h=(u[0]-f[0])/v,g=(u[1]-f[1])/v;return(g>=-.0521&&g<.0229&&h>=-.0111&&h<.1?i:e).invert(u)},l.stream=function(u){return r&&n===u?r:r=ry([e.stream(n=u),i.stream(u)])},l.precision=function(u){return arguments.length?(e.precision(u),i.precision(u),c()):e.precision()},l.scale=function(u){return arguments.length?(e.scale(u),i.scale(u*.615),l.translate(e.translate())):e.scale()},l.translate=function(u){if(!arguments.length)return e.translate();var v=e.scale(),f=+u[0],h=+u[1];return t=e.translate(u).clipExtent([[f-.11*v,h-.0521*v],[f-.0111*v,h+.0521*v]]).stream(s),a=i.translate([f+.09*v,h-0*v]).clipExtent([[f-.0111*v+q,h-.0521*v+q],[f+.1*v-q,h+.024*v-q]]).stream(s),c()},l.fitExtent=function(u,v){return gn(l,u,v)},l.fitSize=function(u,v){return _n(l,u,v)};function c(){return r=n=null,l}return l.drawCompositionBorders=function(u){var v=e([106.3214,2.0228]),f=e([105.1843,2.3761]),h=e([104.2151,3.3618]),g=e([104.215,4.5651]);u.moveTo(v[0],v[1]),u.lineTo(f[0],f[1]),u.lineTo(h[0],h[1]),u.lineTo(g[0],g[1])},l.getCompositionBorders=function(){var u=pn();return this.drawCompositionBorders(u),u.toString()},l.scale(4800)}function ey(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function ty(){var r,n,e=Cr().rotate([-9.5,-1.5]),t,i=Cr().rotate([-8.6,-3.5]),a,o=Cr().rotate([-5.6,1.45]),s,l,c={point:function(f,h){l=[f,h]}};function u(f){var h=f[0],g=f[1];return l=null,t.point(h,g),l||(a.point(h,g),l)||(s.point(h,g),l)}u.invert=function(f){var h=e.scale(),g=e.translate(),p=(f[0]-g[0])/h,d=(f[1]-g[1])/h;return(d>=-.02&&d<0&&p>=-.038&&p<-.005?i:d>=0&&d<.02&&p>=-.038&&p<-.005?o:e).invert(f)},u.stream=function(f){return r&&n===f?r:r=ey([e.stream(n=f),i.stream(f),o.stream(f)])},u.precision=function(f){return arguments.length?(e.precision(f),i.precision(f),o.precision(f),v()):e.precision()},u.scale=function(f){return arguments.length?(e.scale(f),i.scale(f*1.5),o.scale(f*4),u.translate(e.translate())):e.scale()},u.translate=function(f){if(!arguments.length)return e.translate();var h=e.scale(),g=+f[0],p=+f[1];return t=e.translate(f).clipExtent([[g-.005*h,p-.02*h],[g+.038*h,p+.02*h]]).stream(c),a=i.translate([g-.025*h,p-.01*h]).clipExtent([[g-.038*h+q,p-.02*h+q],[g-.005*h-q,p+0*h-q]]).stream(c),s=o.translate([g-.025*h,p+.01*h]).clipExtent([[g-.038*h+q,p-0*h+q],[g-.005*h-q,p+.02*h-q]]).stream(c),v()},u.fitExtent=function(f,h){return gn(u,f,h)},u.fitSize=function(f,h){return _n(u,f,h)};function v(){return r=n=null,u}return u.drawCompositionBorders=function(f){var h,g,p,d;h=e([9.21327272751682,2.645820439454123]),g=e([11.679126293239872,2.644755519268689]),p=e([11.676845389029227,.35307824637606433]),d=e([9.213572917774014,.35414205204417754]),f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.closePath(),h=e([7.320873711543669,2.64475551449975]),g=e([9.213272722738658,2.645820434679803]),p=e([9.213422896480349,1.4999812505283054]),d=e([7.322014760520787,1.4989168878985566]),f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.closePath(),h=e([7.3220147605302905,1.4989168783492766]),g=e([9.213422896481598,1.499981240979021]),p=e([9.213572912999604,.354142056817247]),d=e([7.323154615739809,.353078251154504]),f.moveTo(h[0],h[1]),f.lineTo(g[0],g[1]),f.lineTo(p[0],p[1]),f.lineTo(d[0],d[1]),f.closePath()},u.getCompositionBorders=function(){var f=pn();return this.drawCompositionBorders(f),f.toString()},u.scale(12e3)}function iy(r){var n=r.length;return{point:function(e,t){for(var i=-1;++i<n;)r[i].point(e,t)},sphere:function(){for(var e=-1;++e<n;)r[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)r[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)r[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)r[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)r[e].polygonEnd()}}}function ay(){var r,n,e=Va().rotate([4.4,.8]).center([0,55.4]).parallels([50,60]),t,i=Va().rotate([4.4,.8]).center([0,55.4]).parallels([50,60]),a,o,s={point:function(u,v){o=[u,v]}};function l(u){var v=u[0],f=u[1];return o=null,t.point(v,f),o||(a.point(v,f),o)}l.invert=function(u){var v=e.scale(),f=e.translate(),h=(u[0]-f[0])/v,g=(u[1]-f[1])/v;return(g>=-.089&&g<.06&&h>=.029&&h<.046?i:e).invert(u)},l.stream=function(u){return r&&n===u?r:r=iy([e.stream(n=u),i.stream(u)])},l.precision=function(u){return arguments.length?(e.precision(u),i.precision(u),c()):e.precision()},l.scale=function(u){return arguments.length?(e.scale(u),i.scale(u),l.translate(e.translate())):e.scale()},l.translate=function(u){if(!arguments.length)return e.translate();var v=e.scale(),f=+u[0],h=+u[1];return t=e.translate(u).clipExtent([[f-.065*v,h-.089*v],[f+.075*v,h+.089*v]]).stream(s),a=i.translate([f+.01*v,h+.025*v]).clipExtent([[f+.029*v+q,h-.089*v+q],[f+.046*v-q,h-.06*v-q]]).stream(s),c()},l.fitExtent=function(u,v){return gn(l,u,v)},l.fitSize=function(u,v){return _n(l,u,v)};function c(){return r=n=null,l}return l.drawCompositionBorders=function(u){var v,f,h,g;v=e([-1.113205870242365,59.64920050773357]),f=e([.807899092399606,59.59085836472269]),h=e([.5778611961420386,57.93467822832577]),g=e([-1.25867782078448,57.99029450085142]),u.moveTo(v[0],v[1]),u.lineTo(f[0],f[1]),u.lineTo(h[0],h[1]),u.lineTo(g[0],g[1]),u.closePath()},l.getCompositionBorders=function(){var u=pn();return this.drawCompositionBorders(u),u.toString()},l.scale(2800)}var oy=Object.freeze(Object.defineProperty({__proto__:null,geoAlbersUsa:A5,geoAlbersUsaTerritories:O5,geoConicConformalSpain:F5,geoConicConformalPortugal:B5,geoMercatorEcuador:U5,geoTransverseMercatorChile:Y5,geoConicEquidistantJapan:X5,geoConicConformalFrance:W5,geoConicConformalEurope:K5,geoConicConformalNetherlands:Z5,geoMercatorMalaysia:ny,geoMercatorEquatorialGuinea:ty,geoAlbersUk:ay},Symbol.toStringTag,{value:"Module"})),uy=oe(oy);Object.defineProperty(Eo,"__esModule",{value:!0});var Jl=pr,Hc=or,tu=Jl.__importStar(hi),iu=Jl.__importStar(Gv),au=Jl.__importStar(uy);Eo.default=function(r,n){if(n===void 0&&(n=!1),Hc.isFunction(r))return n?r:r();if(Hc.isString(r)){if(tu[r])return n?tu[r]:tu[r]();if(iu[r])return n?iu[r]:iu[r]();if(au[r])return n?au[r]:au[r]()}return null};var fy=pr,ly=or,Wn=hi,Yc=Gv,sy=Te,Mt=fy.__importDefault(Eo),cy={geoArea:function(r){return Wn.geoArea(r)},geoAreaByName:function(r){return Wn.geoArea(this.geoFeatureByName(r))},geoCentroid:function(r){return Wn.geoCentroid(r)},geoCentroidByName:function(r){return Wn.geoCentroid(this.geoFeatureByName(r))},geoDistance:function(r,n){return Wn.geoDistance(r,n)},geoLength:function(r){return Wn.geoLength(r)},geoLengthByName:function(r){return Wn.geoLength(this.geoFeatureByName(r))},geoContains:function(r,n){return Wn.geoContains(r,n)},geoFeatureByName:function(r){var n=this.rows,e;return n.some(function(t){return t.name===r?(e=t,!0):!1}),e},geoFeatureByPosition:function(r){var n=this.rows,e;return n.some(function(t){return Wn.geoContains(t,r)?(e=t,!0):!1}),e},geoNameByPosition:function(r){var n=this.geoFeatureByPosition(r);if(n)return n.name},getGeoProjection:Mt.default,geoProject:function(r,n,e){return n=Mt.default(n,e),Yc.geoProject(r,n)},geoProjectByName:function(r,n,e){return n=Mt.default(n,e),Yc.geoProject(this.geoFeatureByName(r),n)},geoProjectPosition:function(r,n,e){var t=Mt.default(n,e);return t(r)},geoProjectInvert:function(r,n,e){var t=Mt.default(n,e);return t.invert(r)}};ly.assign(sy.View.prototype,cy);var eh=or,yf=Te;eh.assign(yf.View.prototype,{getAllNodes:function(){var r=[],n=this.root;return n&&n.each?n.each(function(e){r.push(e)}):n&&n.eachNode&&n.eachNode(function(e){r.push(e)}),r},getAllLinks:function(){for(var r=[],n=[this.root],e;e=n.pop();){var t=e.children;t&&t.forEach(function(i){r.push({source:e,target:i}),n.push(i)})}return r}});eh.assign(yf.View.prototype,{getAllEdges:yf.View.prototype.getAllLinks});var Tn={},Kl={};Object.defineProperty(Kl,"__esModule",{value:!0});var ou=or;function vy(r,n){n===void 0&&(n=[]);var e=void 0;return ou.isFunction(n)?e=n:ou.isArray(n)?e=function(t,i){for(var a=0;a<n.length;a++){var o=n[a];if(t[o]<i[o])return-1;if(t[o]>i[o])return 1}return 0}:ou.isString(n)&&(e=function(t,i){return t[n]<i[n]?-1:t[n]>i[n]?1:0}),r.sort(e)}Kl.default=vy;Object.defineProperty(Tn,"__esModule",{value:!0});var hy=pr,qi=or,gy=hy.__importDefault(Kl);Tn.default=function(r,n,e){e===void 0&&(e=[]);var t=r;e&&e.length&&(t=gy.default(r,e));var i;qi.isFunction(n)?i=n:qi.isArray(n)?i=function(o){return"_"+n.map(function(s){return o[s]}).join("-")}:qi.isString(n)&&(i=function(o){return"_"+o[n]});var a=qi.groupBy(t,i);return a};var py=pr,Vc=or,dy=py.__importDefault(Tn),my=Te;Vc.assign(my.View.prototype,{partition:function(r,n){return n===void 0&&(n=[]),dy.default(this.rows,r,n)},group:function(r,n){n===void 0&&(n=[]);var e=this.partition(r,n);return Vc.values(e)},groups:function(r,n){return n===void 0&&(n=[]),this.group(r,n)}});var Dn={exports:{}};(function(r,n){(function(e,t){t(n)})(ao,function(e){function t(T){if(T.length===0)return 0;for(var M,R=T[0],N=0,G=1;G<T.length;G++)M=R+T[G],Math.abs(R)>=Math.abs(T[G])?N+=R-M+T[G]:N+=T[G]-M+R,R=M;return R+N}function i(T){if(T.length===0)throw new Error("mean requires at least one data point");return t(T)/T.length}function a(T,M){var R,N,G=i(T),V=0;if(M===2)for(N=0;N<T.length;N++)V+=(R=T[N]-G)*R;else for(N=0;N<T.length;N++)V+=Math.pow(T[N]-G,M);return V}function o(T){if(T.length===0)throw new Error("variance requires at least one data point");return a(T,2)/T.length}function s(T){if(T.length===1)return 0;var M=o(T);return Math.sqrt(M)}function l(T){if(T.length===0)throw new Error("mode requires at least one data point");if(T.length===1)return T[0];for(var M=T[0],R=NaN,N=0,G=1,V=1;V<T.length+1;V++)T[V]!==M?(N<G&&(N=G,R=M),G=1,M=T[V]):G++;return R}function c(T){return T.slice().sort(function(M,R){return M-R})}function u(T){if(T.length===0)throw new Error("min requires at least one data point");for(var M=T[0],R=1;R<T.length;R++)T[R]<M&&(M=T[R]);return M}function v(T){if(T.length===0)throw new Error("max requires at least one data point");for(var M=T[0],R=1;R<T.length;R++)T[R]>M&&(M=T[R]);return M}function f(T,M){var R=T.length*M;if(T.length===0)throw new Error("quantile requires at least one data point.");if(M<0||1<M)throw new Error("quantiles must be between 0 and 1");return M===1?T[T.length-1]:M===0?T[0]:R%1!=0?T[Math.ceil(R)-1]:T.length%2==0?(T[R-1]+T[R])/2:T[R]}function h(T,M,R,N){for(R=R||0,N=N||T.length-1;R<N;){if(600<N-R){var G=N-R+1,V=M-R+1,J=Math.log(G),W=.5*Math.exp(2*J/3),K=.5*Math.sqrt(J*W*(G-W)/G);V-G/2<0&&(K*=-1),h(T,M,Math.max(R,Math.floor(M-V*W/G+K)),Math.min(N,Math.floor(M+(G-V)*W/G+K)))}var sr=T[M],Ar=R,Rr=N;for(g(T,R,M),T[N]>sr&&g(T,R,N);Ar<Rr;){for(g(T,Ar,Rr),Ar++,Rr--;T[Ar]<sr;)Ar++;for(;T[Rr]>sr;)Rr--}T[R]===sr?g(T,R,Rr):g(T,++Rr,N),Rr<=M&&(R=Rr+1),M<=Rr&&(N=Rr-1)}}function g(T,M,R){var N=T[M];T[M]=T[R],T[R]=N}function p(T,M){var R=T.slice();if(Array.isArray(M)){(function(V,J){for(var W=[0],K=0;K<J.length;K++)W.push(y(V.length,J[K]));W.push(V.length-1),W.sort(m);for(var sr=[0,W.length-1];sr.length;){var Ar=Math.ceil(sr.pop()),Rr=Math.floor(sr.pop());if(!(Ar-Rr<=1)){var Hr=Math.floor((Rr+Ar)/2);d(V,W[Hr],W[Rr],W[Ar]),sr.push(Rr,Hr,Hr,Ar)}}})(R,M);for(var N=[],G=0;G<M.length;G++)N[G]=f(R,M[G]);return N}return d(R,y(R.length,M),0,R.length-1),f(R,M)}function d(T,M,R,N){M%1==0?h(T,M,R,N):(h(T,M=Math.floor(M),R,N),h(T,M+1,M+1,N))}function m(T,M){return T-M}function y(T,M){var R=T*M;return M===1?T-1:M===0?0:R%1!=0?Math.ceil(R)-1:T%2==0?R-.5:R}function E(T,M){if(M<T[0])return 0;if(M>T[T.length-1])return 1;var R=function(V,J){for(var W=0,K=0,sr=V.length;K<sr;)J<=V[W=K+sr>>>1]?sr=W:K=-~W;return K}(T,M);if(T[R]!==M)return R/T.length;R++;var N=function(V,J){for(var W=0,K=0,sr=V.length;K<sr;)J>=V[W=K+sr>>>1]?K=-~W:sr=W;return K}(T,M);if(N===R)return R/T.length;var G=N-R+1;return G*(N+R)/2/G/T.length}function S(T){var M=p(T,.75),R=p(T,.25);if(typeof M=="number"&&typeof R=="number")return M-R}function $(T){return+p(T,.5)}function x(T){for(var M=$(T),R=[],N=0;N<T.length;N++)R.push(Math.abs(T[N]-M));return $(R)}function I(T,M){M=M||Math.random;for(var R,N,G=T.length;0<G;)N=Math.floor(M()*G--),R=T[G],T[G]=T[N],T[N]=R;return T}function C(T,M){return I(T.slice().slice(),M)}function L(T){for(var M,R=0,N=0;N<T.length;N++)N!==0&&T[N]===M||(M=T[N],R++);return R}function O(T,M){for(var R=[],N=0;N<T;N++){for(var G=[],V=0;V<M;V++)G.push(0);R.push(G)}return R}function A(T,M,R,N){var G;if(0<T){var V=(R[M]-R[T-1])/(M-T+1);G=N[M]-N[T-1]-(M-T+1)*V*V}else G=N[M]-R[M]*R[M]/(M+1);return G<0?0:G}function k(T,M,R,N,G,V,J){if(!(M<T)){var W=Math.floor((T+M)/2);N[R][W]=N[R-1][W-1],G[R][W]=W;var K=R;R<T&&(K=Math.max(K,G[R][T-1]||0)),K=Math.max(K,G[R-1][W]||0);var sr,Ar,Rr,Hr=W-1;M<N.length-1&&(Hr=Math.min(Hr,G[R][M+1]||0));for(var qn=Hr;K<=qn&&!((sr=A(qn,W,V,J))+N[R-1][K-1]>=N[R][W]);--qn)(Ar=A(K,W,V,J)+N[R-1][K-1])<N[R][W]&&(N[R][W]=Ar,G[R][W]=K),K++,(Rr=sr+N[R-1][qn-1])<N[R][W]&&(N[R][W]=Rr,G[R][W]=qn);k(T,W-1,R,N,G,V,J),k(W+1,M,R,N,G,V,J)}}function U(T,M){if(T.length!==M.length)throw new Error("sampleCovariance requires samples with equal lengths");if(T.length<2)throw new Error("sampleCovariance requires at least two data points in each sample");for(var R=i(T),N=i(M),G=0,V=0;V<T.length;V++)G+=(T[V]-R)*(M[V]-N);return G/(T.length-1)}function _(T){if(T.length<2)throw new Error("sampleVariance requires at least two data points");return a(T,2)/(T.length-1)}function w(T){var M=_(T);return Math.sqrt(M)}function b(T,M,R,N){return(T*M+R*N)/(M+N)}function P(T){if(T.length===0)throw new Error("rootMeanSquare requires at least one data point");for(var M=0,R=0;R<T.length;R++)M+=Math.pow(T[R],2);return Math.sqrt(M/T.length)}function D(){this.totalCount=0,this.data={}}function rr(){this.weights=[],this.bias=0}D.prototype.train=function(T,M){for(var R in this.data[M]||(this.data[M]={}),T){var N=T[R];this.data[M][R]===void 0&&(this.data[M][R]={}),this.data[M][R][N]===void 0&&(this.data[M][R][N]=0),this.data[M][R][N]++}this.totalCount++},D.prototype.score=function(T){var M,R={};for(var N in T){var G=T[N];for(M in this.data)R[M]={},this.data[M][N]?R[M][N+"_"+G]=(this.data[M][N][G]||0)/this.totalCount:R[M][N+"_"+G]=0}var V={};for(M in R)for(var J in V[M]=0,R[M])V[M]+=R[M][J];return V},rr.prototype.predict=function(T){if(T.length!==this.weights.length)return null;for(var M=0,R=0;R<this.weights.length;R++)M+=this.weights[R]*T[R];return 0<(M+=this.bias)?1:0},rr.prototype.train=function(T,M){if(M!==0&&M!==1)return null;T.length!==this.weights.length&&(this.weights=T,this.bias=1);var R=this.predict(T);if(R!==M){for(var N=M-R,G=0;G<this.weights.length;G++)this.weights[G]+=N*T[G];this.bias+=N}return this};var hr=1e-4;function ur(T){if(T<0)throw new Error("factorial requires a non-negative value");if(Math.floor(T)!==T)throw new Error("factorial requires an integer input");for(var M=1,R=2;R<=T;R++)M*=R;return M}var gr=[.9999999999999971,57.15623566586292,-59.59796035547549,14.136097974741746,-.4919138160976202,3399464998481189e-20,4652362892704858e-20,-9837447530487956e-20,.0001580887032249125,-.00021026444172410488,.00021743961811521265,-.0001643181065367639,8441822398385275e-20,-26190838401581408e-21,36899182659531625e-22],xr=Math.log(Math.sqrt(2*Math.PI)),Ur={1:{.995:0,.99:0,.975:0,.95:0,.9:.02,.5:.45,.1:2.71,.05:3.84,.025:5.02,.01:6.63,.005:7.88},2:{.995:.01,.99:.02,.975:.05,.95:.1,.9:.21,.5:1.39,.1:4.61,.05:5.99,.025:7.38,.01:9.21,.005:10.6},3:{.995:.07,.99:.11,.975:.22,.95:.35,.9:.58,.5:2.37,.1:6.25,.05:7.81,.025:9.35,.01:11.34,.005:12.84},4:{.995:.21,.99:.3,.975:.48,.95:.71,.9:1.06,.5:3.36,.1:7.78,.05:9.49,.025:11.14,.01:13.28,.005:14.86},5:{.995:.41,.99:.55,.975:.83,.95:1.15,.9:1.61,.5:4.35,.1:9.24,.05:11.07,.025:12.83,.01:15.09,.005:16.75},6:{.995:.68,.99:.87,.975:1.24,.95:1.64,.9:2.2,.5:5.35,.1:10.65,.05:12.59,.025:14.45,.01:16.81,.005:18.55},7:{.995:.99,.99:1.25,.975:1.69,.95:2.17,.9:2.83,.5:6.35,.1:12.02,.05:14.07,.025:16.01,.01:18.48,.005:20.28},8:{.995:1.34,.99:1.65,.975:2.18,.95:2.73,.9:3.49,.5:7.34,.1:13.36,.05:15.51,.025:17.53,.01:20.09,.005:21.96},9:{.995:1.73,.99:2.09,.975:2.7,.95:3.33,.9:4.17,.5:8.34,.1:14.68,.05:16.92,.025:19.02,.01:21.67,.005:23.59},10:{.995:2.16,.99:2.56,.975:3.25,.95:3.94,.9:4.87,.5:9.34,.1:15.99,.05:18.31,.025:20.48,.01:23.21,.005:25.19},11:{.995:2.6,.99:3.05,.975:3.82,.95:4.57,.9:5.58,.5:10.34,.1:17.28,.05:19.68,.025:21.92,.01:24.72,.005:26.76},12:{.995:3.07,.99:3.57,.975:4.4,.95:5.23,.9:6.3,.5:11.34,.1:18.55,.05:21.03,.025:23.34,.01:26.22,.005:28.3},13:{.995:3.57,.99:4.11,.975:5.01,.95:5.89,.9:7.04,.5:12.34,.1:19.81,.05:22.36,.025:24.74,.01:27.69,.005:29.82},14:{.995:4.07,.99:4.66,.975:5.63,.95:6.57,.9:7.79,.5:13.34,.1:21.06,.05:23.68,.025:26.12,.01:29.14,.005:31.32},15:{.995:4.6,.99:5.23,.975:6.27,.95:7.26,.9:8.55,.5:14.34,.1:22.31,.05:25,.025:27.49,.01:30.58,.005:32.8},16:{.995:5.14,.99:5.81,.975:6.91,.95:7.96,.9:9.31,.5:15.34,.1:23.54,.05:26.3,.025:28.85,.01:32,.005:34.27},17:{.995:5.7,.99:6.41,.975:7.56,.95:8.67,.9:10.09,.5:16.34,.1:24.77,.05:27.59,.025:30.19,.01:33.41,.005:35.72},18:{.995:6.26,.99:7.01,.975:8.23,.95:9.39,.9:10.87,.5:17.34,.1:25.99,.05:28.87,.025:31.53,.01:34.81,.005:37.16},19:{.995:6.84,.99:7.63,.975:8.91,.95:10.12,.9:11.65,.5:18.34,.1:27.2,.05:30.14,.025:32.85,.01:36.19,.005:38.58},20:{.995:7.43,.99:8.26,.975:9.59,.95:10.85,.9:12.44,.5:19.34,.1:28.41,.05:31.41,.025:34.17,.01:37.57,.005:40},21:{.995:8.03,.99:8.9,.975:10.28,.95:11.59,.9:13.24,.5:20.34,.1:29.62,.05:32.67,.025:35.48,.01:38.93,.005:41.4},22:{.995:8.64,.99:9.54,.975:10.98,.95:12.34,.9:14.04,.5:21.34,.1:30.81,.05:33.92,.025:36.78,.01:40.29,.005:42.8},23:{.995:9.26,.99:10.2,.975:11.69,.95:13.09,.9:14.85,.5:22.34,.1:32.01,.05:35.17,.025:38.08,.01:41.64,.005:44.18},24:{.995:9.89,.99:10.86,.975:12.4,.95:13.85,.9:15.66,.5:23.34,.1:33.2,.05:36.42,.025:39.36,.01:42.98,.005:45.56},25:{.995:10.52,.99:11.52,.975:13.12,.95:14.61,.9:16.47,.5:24.34,.1:34.28,.05:37.65,.025:40.65,.01:44.31,.005:46.93},26:{.995:11.16,.99:12.2,.975:13.84,.95:15.38,.9:17.29,.5:25.34,.1:35.56,.05:38.89,.025:41.92,.01:45.64,.005:48.29},27:{.995:11.81,.99:12.88,.975:14.57,.95:16.15,.9:18.11,.5:26.34,.1:36.74,.05:40.11,.025:43.19,.01:46.96,.005:49.65},28:{.995:12.46,.99:13.57,.975:15.31,.95:16.93,.9:18.94,.5:27.34,.1:37.92,.05:41.34,.025:44.46,.01:48.28,.005:50.99},29:{.995:13.12,.99:14.26,.975:16.05,.95:17.71,.9:19.77,.5:28.34,.1:39.09,.05:42.56,.025:45.72,.01:49.59,.005:52.34},30:{.995:13.79,.99:14.95,.975:16.79,.95:18.49,.9:20.6,.5:29.34,.1:40.26,.05:43.77,.025:46.98,.01:50.89,.005:53.67},40:{.995:20.71,.99:22.16,.975:24.43,.95:26.51,.9:29.05,.5:39.34,.1:51.81,.05:55.76,.025:59.34,.01:63.69,.005:66.77},50:{.995:27.99,.99:29.71,.975:32.36,.95:34.76,.9:37.69,.5:49.33,.1:63.17,.05:67.5,.025:71.42,.01:76.15,.005:79.49},60:{.995:35.53,.99:37.48,.975:40.48,.95:43.19,.9:46.46,.5:59.33,.1:74.4,.05:79.08,.025:83.3,.01:88.38,.005:91.95},70:{.995:43.28,.99:45.44,.975:48.76,.95:51.74,.9:55.33,.5:69.33,.1:85.53,.05:90.53,.025:95.02,.01:100.42,.005:104.22},80:{.995:51.17,.99:53.54,.975:57.15,.95:60.39,.9:64.28,.5:79.33,.1:96.58,.05:101.88,.025:106.63,.01:112.33,.005:116.32},90:{.995:59.2,.99:61.75,.975:65.65,.95:69.13,.9:73.29,.5:89.33,.1:107.57,.05:113.14,.025:118.14,.01:124.12,.005:128.3},100:{.995:67.33,.99:70.06,.975:74.22,.95:77.93,.9:82.36,.5:99.33,.1:118.5,.05:124.34,.025:129.56,.01:135.81,.005:140.17}},jr=Math.sqrt(2*Math.PI),$e={gaussian:function(T){return Math.exp(-.5*T*T)/jr}},No={nrd:function(T){var M=w(T),R=S(T);return typeof R=="number"&&(M=Math.min(M,R/1.34)),1.06*M*Math.pow(T.length,-.2)}};function ks(T,M,R){var N,G;if(M===void 0)N=$e.gaussian;else if(typeof M=="string"){if(!$e[M])throw new Error('Unknown kernel "'+M+'"');N=$e[M]}else N=M;if(R===void 0)G=No.nrd(T);else if(typeof R=="string"){if(!No[R])throw new Error('Unknown bandwidth method "'+R+'"');G=No[R](T)}else G=R;return function(V){var J=0,W=0;for(J=0;J<T.length;J++)W+=N((V-T[J])/G);return W/G/T.length}}var np=Math.sqrt(2*Math.PI);function ep(T){for(var M=T,R=T,N=1;N<15;N++)M+=R*=T*T/(2*N+1);return Math.round(1e4*(.5+M/np*Math.exp(-T*T/2)))/1e4}for(var St=[],Ao=0;Ao<=3.09;Ao+=.01)St.push(ep(Ao));function Cs(T){var M=1/(1+.5*Math.abs(T)),R=M*Math.exp(-Math.pow(T,2)-1.26551223+1.00002368*M+.37409196*Math.pow(M,2)+.09678418*Math.pow(M,3)-.18628806*Math.pow(M,4)+.27886807*Math.pow(M,5)-1.13520398*Math.pow(M,6)+1.48851587*Math.pow(M,7)-.82215223*Math.pow(M,8)+.17087277*Math.pow(M,9));return 0<=T?1-R:R-1}function Ls(T){var M=8*(Math.PI-3)/(3*Math.PI*(4-Math.PI)),R=Math.sqrt(Math.sqrt(Math.pow(2/(Math.PI*M)+Math.log(1-T*T)/2,2)-Math.log(1-T*T)/M)-(2/(Math.PI*M)+Math.log(1-T*T)/2));return 0<=T?R:-R}function Do(T){if(typeof T=="number")return T<0?-1:T===0?0:1;throw new TypeError("not a number")}e.linearRegression=function(T){var M,R,N=T.length;if(N===1)R=T[M=0][1];else{for(var G,V,J,W=0,K=0,sr=0,Ar=0,Rr=0;Rr<N;Rr++)W+=V=(G=T[Rr])[0],K+=J=G[1],sr+=V*V,Ar+=V*J;R=K/N-(M=(N*Ar-W*K)/(N*sr-W*W))*W/N}return{m:M,b:R}},e.linearRegressionLine=function(T){return function(M){return T.b+T.m*M}},e.standardDeviation=s,e.rSquared=function(T,M){if(T.length<2)return 1;for(var R,N=0,G=0;G<T.length;G++)N+=T[G][1];R=N/T.length;for(var V=0,J=0;J<T.length;J++)V+=Math.pow(R-T[J][1],2);for(var W=0,K=0;K<T.length;K++)W+=Math.pow(T[K][1]-M(T[K][0]),2);return 1-W/V},e.mode=function(T){return l(c(T))},e.modeFast=function(T){for(var M,R=new Map,N=0,G=0;G<T.length;G++){var V=R.get(T[G]);V===void 0?V=1:V++,N<V&&(M=T[G],N=V),R.set(T[G],V)}if(N===0)throw new Error("mode requires at last one data point");return M},e.modeSorted=l,e.min=u,e.max=v,e.extent=function(T){if(T.length===0)throw new Error("extent requires at least one data point");for(var M=T[0],R=T[0],N=1;N<T.length;N++)T[N]>R&&(R=T[N]),T[N]<M&&(M=T[N]);return[M,R]},e.minSorted=function(T){return T[0]},e.maxSorted=function(T){return T[T.length-1]},e.extentSorted=function(T){return[T[0],T[T.length-1]]},e.sum=t,e.sumSimple=function(T){for(var M=0,R=0;R<T.length;R++)M+=T[R];return M},e.product=function(T){for(var M=1,R=0;R<T.length;R++)M*=T[R];return M},e.quantile=p,e.quantileSorted=f,e.quantileRank=function(T,M){return E(c(T),M)},e.quantileRankSorted=E,e.interquartileRange=S,e.iqr=S,e.medianAbsoluteDeviation=x,e.mad=x,e.chunk=function(T,M){var R=[];if(M<1)throw new Error("chunk size must be a positive number");if(Math.floor(M)!==M)throw new Error("chunk size must be an integer");for(var N=0;N<T.length;N+=M)R.push(T.slice(N,N+M));return R},e.sampleWithReplacement=function(T,M,R){if(T.length===0)return[];R=R||Math.random;for(var N=T.length,G=[],V=0;V<M;V++){var J=Math.floor(R()*N);G.push(T[J])}return G},e.shuffle=C,e.shuffleInPlace=I,e.sample=function(T,M,R){return C(T,R).slice(0,M)},e.ckmeans=function(T,M){if(M>T.length)throw new Error("cannot generate more classes than there are data values");var R=c(T);if(L(R)===1)return[R];var N=O(M,R.length),G=O(M,R.length);(function(sr,Ar,Rr){for(var Hr,qn=Ar[0].length,tp=sr[Math.floor(qn/2)],_t=[],Tt=[],jn=0;jn<qn;++jn)Hr=sr[jn]-tp,jn===0?(_t.push(Hr),Tt.push(Hr*Hr)):(_t.push(_t[jn-1]+Hr),Tt.push(Tt[jn-1]+Hr*Hr)),Ar[0][jn]=A(0,jn,_t,Tt),Rr[0][jn]=0;for(var $t=1;$t<Ar.length;++$t)k($t<Ar.length-1?$t:qn-1,qn-1,$t,Ar,Rr,_t,Tt)})(R,N,G);for(var V=[],J=G[0].length-1,W=G.length-1;0<=W;W--){var K=G[W][J];V[W]=R.slice(K,J+1),0<W&&(J=K-1)}return V},e.uniqueCountSorted=L,e.sumNthPowerDeviations=a,e.equalIntervalBreaks=function(T,M){if(T.length<2)return T;for(var R=u(T),N=v(T),G=[R],V=(N-R)/M,J=1;J<M;J++)G.push(G[0]+V*J);return G.push(N),G},e.sampleCovariance=U,e.sampleCorrelation=function(T,M){return U(T,M)/w(T)/w(M)},e.sampleVariance=_,e.sampleStandardDeviation=w,e.sampleSkewness=function(T){if(T.length<3)throw new Error("sampleSkewness requires at least three data points");for(var M,R=i(T),N=0,G=0,V=0;V<T.length;V++)N+=(M=T[V]-R)*M,G+=M*M*M;var J=T.length-1,W=Math.sqrt(N/J),K=T.length;return K*G/((K-1)*(K-2)*Math.pow(W,3))},e.sampleKurtosis=function(T){var M=T.length;if(M<4)throw new Error("sampleKurtosis requires at least four data points");for(var R,N=i(T),G=0,V=0,J=0;J<M;J++)G+=(R=T[J]-N)*R,V+=R*R*R*R;return(M-1)/((M-2)*(M-3))*(M*(M+1)*V/(G*G)-3*(M-1))},e.permutationsHeap=function(T){for(var M=new Array(T.length),R=[T.slice()],N=0;N<T.length;N++)M[N]=0;for(N=0;N<T.length;)if(M[N]<N){var G=0;N%2!=0&&(G=M[N]);var V=T[G];T[G]=T[N],T[N]=V,R.push(T.slice()),M[N]++,N=0}else M[N]=0,N++;return R},e.combinations=function T(M,R){var N,G,V,J,W=[];for(N=0;N<M.length;N++)if(R===1)W.push([M[N]]);else for(V=T(M.slice(N+1,M.length),R-1),G=0;G<V.length;G++)(J=V[G]).unshift(M[N]),W.push(J);return W},e.combinationsReplacement=function T(M,R){for(var N=[],G=0;G<M.length;G++)if(R===1)N.push([M[G]]);else for(var V=T(M.slice(G,M.length),R-1),J=0;J<V.length;J++)N.push([M[G]].concat(V[J]));return N},e.addToMean=function(T,M,R){return T+(R-T)/(M+1)},e.combineMeans=b,e.combineVariances=function(T,M,R,N,G,V){var J=b(M,R,G,V);return(R*(T+Math.pow(M-J,2))+V*(N+Math.pow(G-J,2)))/(R+V)},e.geometricMean=function(T){if(T.length===0)throw new Error("geometricMean requires at least one data point");for(var M=1,R=0;R<T.length;R++){if(T[R]<=0)throw new Error("geometricMean requires only positive numbers as input");M*=T[R]}return Math.pow(M,1/T.length)},e.harmonicMean=function(T){if(T.length===0)throw new Error("harmonicMean requires at least one data point");for(var M=0,R=0;R<T.length;R++){if(T[R]<=0)throw new Error("harmonicMean requires only positive numbers as input");M+=1/T[R]}return T.length/M},e.average=i,e.mean=i,e.median=$,e.medianSorted=function(T){return f(T,.5)},e.subtractFromMean=function(T,M,R){return(T*M-R)/(M-1)},e.rootMeanSquare=P,e.rms=P,e.variance=o,e.tTest=function(T,M){return(i(T)-M)/(s(T)/Math.sqrt(T.length))},e.tTestTwoSample=function(T,M,R){var N=T.length,G=M.length;if(!N||!G)return null;R||(R=0);var V=i(T),J=i(M),W=_(T),K=_(M);if(typeof V=="number"&&typeof J=="number"&&typeof W=="number"&&typeof K=="number"){var sr=((N-1)*W+(G-1)*K)/(N+G-2);return(V-J-R)/Math.sqrt(sr*(1/N+1/G))}},e.BayesianClassifier=D,e.bayesian=D,e.PerceptronModel=rr,e.perceptron=rr,e.epsilon=hr,e.factorial=ur,e.gamma=function T(M){if(typeof(R=M)=="number"&&isFinite(R)&&Math.floor(R)===R)return M<=0?NaN:ur(M-1);var R;if(--M<0)return Math.PI/(Math.sin(Math.PI*-M)*T(-M));var N=M+.25;return Math.pow(M/Math.E,M)*Math.sqrt(2*Math.PI*(M+1/6))*(1+1/144/Math.pow(N,2)-1/12960/Math.pow(N,3)-257/207360/Math.pow(N,4)-52/2612736/Math.pow(N,5)+5741173/9405849600/Math.pow(N,6)+37529/18811699200/Math.pow(N,7))},e.gammaln=function(T){if(T<=0)return 1/0;T--;for(var M=gr[0],R=1;R<15;R++)M+=gr[R]/(T+R);var N=5.2421875+T;return xr+Math.log(M)-N+(T+.5)*Math.log(N)},e.bernoulliDistribution=function(T){if(T<0||1<T)throw new Error("bernoulliDistribution requires probability to be between 0 and 1 inclusive");return[1-T,T]},e.binomialDistribution=function(T,M){if(!(M<0||1<M||T<=0||T%1!=0)){for(var R=0,N=0,G=[],V=1;G[R]=V*Math.pow(M,R)*Math.pow(1-M,T-R),N+=G[R],V=V*(T-++R+1)/R,N<1-hr;);return G}},e.poissonDistribution=function(T){if(!(T<=0)){for(var M=0,R=0,N=[],G=1;N[M]=Math.exp(-T)*Math.pow(T,M)/G,R+=N[M],G*=++M,R<1-hr;);return N}},e.chiSquaredDistributionTable=Ur,e.chiSquaredGoodnessOfFit=function(T,M,R){for(var N,G,V=0,J=M(i(T)),W=[],K=[],sr=0;sr<T.length;sr++)W[T[sr]]===void 0&&(W[T[sr]]=0),W[T[sr]]++;for(sr=0;sr<W.length;sr++)W[sr]===void 0&&(W[sr]=0);for(G in J)G in W&&(K[+G]=J[G]*T.length);for(G=K.length-1;0<=G;G--)K[G]<3&&(K[G-1]+=K[G],K.pop(),W[G-1]+=W[G],W.pop());for(G=0;G<W.length;G++)V+=Math.pow(W[G]-K[G],2)/K[G];return N=W.length-1-1,Ur[N][R]<V},e.kernelDensityEstimation=ks,e.kde=ks,e.zScore=function(T,M,R){return(T-M)/R},e.cumulativeStdNormalProbability=function(T){var M=Math.abs(T),R=Math.min(Math.round(100*M),St.length-1);return 0<=T?St[R]:+(1-St[R]).toFixed(4)},e.standardNormalTable=St,e.errorFunction=Cs,e.erf=Cs,e.inverseErrorFunction=Ls,e.probit=function(T){return T===0?T=hr:1<=T&&(T=1-hr),Math.sqrt(2)*Ls(2*T-1)},e.permutationTest=function(T,M,R,N){if(N===void 0&&(N=1e4),R===void 0&&(R="two_side"),R!=="two_side"&&R!=="greater"&&R!=="less")throw new Error("`alternative` must be either 'two_side', 'greater', or 'less'");for(var G=i(T)-i(M),V=new Array(N),J=T.concat(M),W=Math.floor(J.length/2),K=0;K<N;K++){I(J);var sr=J.slice(0,W),Ar=J.slice(W,J.length),Rr=i(sr)-i(Ar);V[K]=Rr}var Hr=0;if(R==="two_side")for(K=0;K<=N;K++)Math.abs(V[K])>=Math.abs(G)&&(Hr+=1);else if(R==="greater")for(K=0;K<=N;K++)V[K]>=G&&(Hr+=1);else for(K=0;K<=N;K++)V[K]<=G&&(Hr+=1);return Hr/N},e.bisect=function(T,M,R,N,G){if(typeof T!="function")throw new TypeError("func must be a function");for(var V=0;V<N;V++){var J=(M+R)/2;if(T(J)===0||Math.abs((R-M)/2)<G)return J;Do(T(J))===Do(T(M))?M=J:R=J}throw new Error("maximum number of iterations exceeded")},e.quickselect=h,e.sign=Do,e.numericSort=c,Object.defineProperty(e,"__esModule",{value:!0})})})(Dn,Dn.exports);var _o={};Object.defineProperty(_o,"__esModule",{value:!0});_o.default=function(r){for(var n=1/r,e=[],t=0;t<=1;t=t+n)e.push(t);return e};var To={};Object.defineProperty(To,"__esModule",{value:!0});To.default={HIERARCHY:"hierarchy",GEO:"geo",HEX:"hex",GRAPH:"graph",TABLE:"table",GEO_GRATICULE:"geo-graticule",STATISTICS_METHODS:["max","mean","median","min","mode","product","standardDeviation","sum","sumSimple","variance"]};var Ql=pr,th=Ql.__importStar(Dn.exports),ia=or,wf=Te,yy=Ql.__importDefault(_o),wy=Ql.__importDefault(To),Ey=wy.default.STATISTICS_METHODS;function Ef(r,n){var e=r.getColumn(n);return ia.isArray(e)&&ia.isArray(e[0])&&(e=ia.flattenDeep(e)),e}Ey.forEach(function(r){wf.View.prototype[r]=function(n){return th[r](Ef(this,n))}});var Xc=th.quantile;ia.assign(wf.View.prototype,{average:wf.View.prototype.mean,quantile:function(r,n){return Xc(Ef(this,r),n)},quantiles:function(r,n){var e=Ef(this,r);return n.map(function(t){return Xc(e,t)})},quantilesByFraction:function(r,n){return this.quantiles(r,yy.default(n))},range:function(r){return[this.min(r),this.max(r)]},extent:function(r){return this.range(r)}});var ir={};Object.defineProperty(ir,"__esModule",{value:!0});ir.DataSet=void 0;var Zl=pr,je=or,Sy=Zl.__importDefault(Hl.exports),aa=Te,ih=Zl.__importDefault(To),ui=function(r){Zl.__extends(n,r);function n(e){e===void 0&&(e={state:{}});var t=r.call(this)||this;return t.isDataSet=!0,t._onChangeTimer=null,t.views={},t.state={},t.state=e.state,t}return n.registerConnector=function(e,t){n.connectors[e]=t},n.getConnector=function(e){return n.connectors[e]||n.connectors.default},n.registerTransform=function(e,t){n.transforms[e]=t},n.getTransform=function(e){return n.transforms[e]||n.transforms.default},n.prototype._getUniqueViewName=function(){for(var e=je.uniqueId("view_");this.views[e];)e=je.uniqueId("view_");return e},n.prototype.createView=function(e,t){if(je.isNil(e)&&(e=this._getUniqueViewName()),je.isObject(e)&&(t=e,e=this._getUniqueViewName()),this.views[e])throw new Error("data view exists: "+e);var i=new aa.View(this,t);return this.views[e]=i,i},n.prototype.getView=function(e){return this.views[e]},n.prototype.setView=function(e,t){this.views[e]=t},n.prototype.setState=function(e,t){var i=this;this.state[e]=t,this._onChangeTimer&&(window.clearTimeout(this._onChangeTimer),this._onChangeTimer=null),this._onChangeTimer=window.setTimeout(function(){i.emit("statechange",e,t)},16)},n.CONSTANTS=ih.default,n.connectors={},n.transforms={},n.DataSet=n,n.DataView=aa.View,n.View=aa.View,n.version="____DATASET_VERSION____",n}(Sy.default);ir.DataSet=ui;je.assign(ui,ih.default);je.assign(ui.prototype,{view:ui.prototype.createView});aa.View.DataSet=ui;var jc=or,_y=ir;_y.DataSet.registerConnector("default",function(r,n){var e;if(jc.isString(r)?e=n.getView(r):e=r,!e)throw new TypeError("Invalid dataView");return jc.deepMix([],e.rows)});var Wc={},uu={},fu=34,xt=10,lu=13;function ah(r){return new Function("d","return {"+r.map(function(n,e){return JSON.stringify(n)+": d["+e+'] || ""'}).join(",")+"}")}function Ty(r,n){var e=ah(r);return function(t,i){return n(e(t),i,r)}}function Jc(r){var n=Object.create(null),e=[];return r.forEach(function(t){for(var i in t)i in n||e.push(n[i]=i)}),e}function fn(r,n){var e=r+"",t=e.length;return t<n?new Array(n-t+1).join(0)+e:e}function $y(r){return r<0?"-"+fn(-r,6):r>9999?"+"+fn(r,6):fn(r,4)}function My(r){var n=r.getUTCHours(),e=r.getUTCMinutes(),t=r.getUTCSeconds(),i=r.getUTCMilliseconds();return isNaN(r)?"Invalid Date":$y(r.getUTCFullYear())+"-"+fn(r.getUTCMonth()+1,2)+"-"+fn(r.getUTCDate(),2)+(i?"T"+fn(n,2)+":"+fn(e,2)+":"+fn(t,2)+"."+fn(i,3)+"Z":t?"T"+fn(n,2)+":"+fn(e,2)+":"+fn(t,2)+"Z":e||n?"T"+fn(n,2)+":"+fn(e,2)+"Z":"")}function rs(r){var n=new RegExp('["'+r+`
\r]`),e=r.charCodeAt(0);function t(v,f){var h,g,p=i(v,function(d,m){if(h)return h(d,m-1);g=d,h=f?Ty(d,f):ah(d)});return p.columns=g||[],p}function i(v,f){var h=[],g=v.length,p=0,d=0,m,y=g<=0,E=!1;v.charCodeAt(g-1)===xt&&--g,v.charCodeAt(g-1)===lu&&--g;function S(){if(y)return uu;if(E)return E=!1,Wc;var x,I=p,C;if(v.charCodeAt(I)===fu){for(;p++<g&&v.charCodeAt(p)!==fu||v.charCodeAt(++p)===fu;);return(x=p)>=g?y=!0:(C=v.charCodeAt(p++))===xt?E=!0:C===lu&&(E=!0,v.charCodeAt(p)===xt&&++p),v.slice(I+1,x-1).replace(/""/g,'"')}for(;p<g;){if((C=v.charCodeAt(x=p++))===xt)E=!0;else if(C===lu)E=!0,v.charCodeAt(p)===xt&&++p;else if(C!==e)continue;return v.slice(I,x)}return y=!0,v.slice(I,g)}for(;(m=S())!==uu;){for(var $=[];m!==Wc&&m!==uu;)$.push(m),m=S();f&&($=f($,d++))==null||h.push($)}return h}function a(v,f){return v.map(function(h){return f.map(function(g){return u(h[g])}).join(r)})}function o(v,f){return f==null&&(f=Jc(v)),[f.map(u).join(r)].concat(a(v,f)).join(`
`)}function s(v,f){return f==null&&(f=Jc(v)),a(v,f).join(`
`)}function l(v){return v.map(c).join(`
`)}function c(v){return v.map(u).join(r)}function u(v){return v==null?"":v instanceof Date?My(v):n.test(v+="")?'"'+v.replace(/"/g,'""')+'"':v}return{parse:t,parseRows:i,format:o,formatBody:s,formatRows:l,formatRow:c,formatValue:u}}var ze=rs(","),xy=ze.parse,by=ze.parseRows,Py=ze.format,Ry=ze.formatBody,ky=ze.formatRows,Cy=ze.formatRow,Ly=ze.formatValue,Be=rs("	"),Iy=Be.parse,Ny=Be.parseRows,Ay=Be.format,Dy=Be.formatBody,Oy=Be.formatRows,qy=Be.formatRow,Fy=Be.formatValue;function zy(r){for(var n in r){var e=r[n].trim(),t,i;if(!e)e=null;else if(e==="true")e=!0;else if(e==="false")e=!1;else if(e==="NaN")e=NaN;else if(!isNaN(t=+e))e=t;else if(i=e.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/))By&&!!i[4]&&!i[7]&&(e=e.replace(/-/g,"/").replace(/T/," ")),e=new Date(e);else continue;r[n]=e}return r}var By=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours(),Gy=Object.freeze(Object.defineProperty({__proto__:null,dsvFormat:rs,csvParse:xy,csvParseRows:by,csvFormat:Py,csvFormatBody:Ry,csvFormatRows:ky,csvFormatRow:Cy,csvFormatValue:Ly,tsvParse:Iy,tsvParseRows:Ny,tsvFormat:Ay,tsvFormatBody:Dy,tsvFormatRows:Oy,tsvFormatRow:qy,tsvFormatValue:Fy,autoType:zy},Symbol.toStringTag,{value:"Module"})),Uy=oe(Gy),Hy=or,ns=Uy,es=ir;es.DataSet.registerConnector("dsv",function(r,n){n===void 0&&(n={});var e=n.delimiter||",";if(!Hy.isString(e))throw new TypeError("Invalid delimiter: must be a string!");return ns.dsvFormat(e).parse(r)});es.DataSet.registerConnector("csv",function(r){return ns.csvParse(r)});es.DataSet.registerConnector("tsv",function(r){return ns.tsvParse(r)});var oh={};Object.defineProperty(oh,"__esModule",{value:!0});var Yy=hi,Vy=ir;function uh(r,n){n.dataType="geo-graticule";var e=Yy.geoGraticule().lines();return e.map(function(t,i){return t.index=""+i,t}),n.rows=e,e}oh.default=uh;Vy.DataSet.registerConnector("geo-graticule",uh);var ts={},Xy=Wy,su={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},jy=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function Wy(r){var n=[];return r.replace(jy,function(e,t,i){var a=t.toLowerCase();for(i=Ky(i),a=="m"&&i.length>2&&(n.push([t].concat(i.splice(0,2))),a="l",t=t=="m"?"l":"L");;){if(i.length==su[a])return i.unshift(t),n.push(i);if(i.length<su[a])throw new Error("malformed path data");n.push([t].concat(i.splice(0,su[a])))}}),n}var Jy=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function Ky(r){var n=r.match(Jy);return n?n.map(Number):[]}var Qy=Zy;function Zy(r){var n=0,e=0,t=0,i=0;return r.map(function(a){a=a.slice();var o=a[0],s=o.toUpperCase();if(o!=s)switch(a[0]=s,o){case"a":a[6]+=t,a[7]+=i;break;case"v":a[1]+=i;break;case"h":a[1]+=t;break;default:for(var l=1;l<a.length;)a[l++]+=t,a[l++]+=i}switch(s){case"Z":t=n,i=e;break;case"H":t=a[1];break;case"V":i=a[1];break;case"M":t=n=a[1],i=e=a[2];break;default:t=a[a.length-2],i=a[a.length-1]}return a})}var rw=Xy,nw=ip,ew=Qy,fh=ct;function ct(r){if(!(this instanceof ct))return new ct(r);this._path=nw(r)?r:rw(r),this._path=ew(this._path),this._path=iw(this._path),this._path=tw(this._path)}ct.prototype.at=function(r,n){return this._walk(r,n).pos};ct.prototype.length=function(){return this._walk(null).length};ct.prototype._walk=function(r,n){for(var e=[0,0],t=[0,0,0],i=[0,0],a=0,o=0;o<this._path.length;o++){var s=this._path[o];if(s[0]==="M"){if(e[0]=s[1],e[1]=s[2],r===0)return{length:a,pos:e}}else if(s[0]==="C"){t[0]=i[0]=e[0],t[1]=i[1]=e[1],t[2]=a;for(var l=100,c=0;c<=l;c++){var u=c/l,v=p(s,u),f=d(s,u);if(a+=cu(e[0],e[1],v,f),e[0]=v,e[1]=f,typeof r=="number"&&a>=r){var h=(a-r)/(a-t[2]),g=[e[0]*(1-h)+t[0]*h,e[1]*(1-h)+t[1]*h];return{length:a,pos:g}}t[0]=e[0],t[1]=e[1],t[2]=a}}else if(s[0]==="Q"){t[0]=i[0]=e[0],t[1]=i[1]=e[1],t[2]=a;for(var l=100,c=0;c<=l;c++){var u=c/l,v=m(s,u),f=y(s,u);if(a+=cu(e[0],e[1],v,f),e[0]=v,e[1]=f,typeof r=="number"&&a>=r){var h=(a-r)/(a-t[2]),g=[e[0]*(1-h)+t[0]*h,e[1]*(1-h)+t[1]*h];return{length:a,pos:g}}t[0]=e[0],t[1]=e[1],t[2]=a}}else if(s[0]==="L"){if(t[0]=e[0],t[1]=e[1],t[2]=a,a+=cu(e[0],e[1],s[1],s[2]),e[0]=s[1],e[1]=s[2],typeof r=="number"&&a>=r){var h=(a-r)/(a-t[2]),g=[e[0]*(1-h)+t[0]*h,e[1]*(1-h)+t[1]*h];return{length:a,pos:g}}t[0]=e[0],t[1]=e[1],t[2]=a}}return{length:a,pos:e};function p(E,S){return Math.pow(1-S,3)*i[0]+3*Math.pow(1-S,2)*S*E[1]+3*(1-S)*Math.pow(S,2)*E[3]+Math.pow(S,3)*E[5]}function d(E,S){return Math.pow(1-S,3)*i[1]+3*Math.pow(1-S,2)*S*E[2]+3*(1-S)*Math.pow(S,2)*E[4]+Math.pow(S,3)*E[6]}function m(E,S){return Math.pow(1-S,2)*i[0]+2*(1-S)*S*E[1]+Math.pow(S,2)*E[3]}function y(E,S){return Math.pow(1-S,2)*i[1]+2*(1-S)*S*E[2]+Math.pow(S,2)*E[4]}};function cu(r,n,e,t){var i=r-e,a=n-t;return Math.sqrt(i*i+a*a)}function tw(r){for(var n,e=0,t=0,i={S:{to:"C",x:3},T:{to:"Q",x:1}},a=0,o=r.length;a<o;a++){var s=r[a],l=i[s[0]];l&&(s[0]=l.to,n&&(n[0]===l.to?(e=2*n[l.x+2]-n[l.x],t=2*n[l.x+3]-n[l.x+1]):(e=n[n.length-2],t=n[n.length-1])),s.splice(1,0,e,t)),n=s}return r}function iw(r){for(var n=[],e=["L",0,0],t,i=0,a=r.length;i<a;i++){var o=r[i];switch(o[0]){case"M":e=["L",o[1],o[2]],n.push(o);break;case"Z":n.push(e);break;case"H":t=n[n.length-1]||["L",0,0],n.push(["L",o[1],t[t.length-1]]);break;case"V":t=n[n.length-1]||["L",0,0],n.push(["L",t[t.length-2],o[1]]);break;default:n.push(o)}}return n}Object.defineProperty(ts,"__esModule",{value:!0});var aw=pr,ow=aw.__importDefault(fh),uw=or,fw=hi,$o=ir,Kc=fw.geoPath();function Mo(r,n,e){e.dataType=$o.DataSet.CONSTANTS.GEO;var t=uw.deepMix([],r.features);return t.forEach(function(i){i.name=i.properties.name,i.longitude=[],i.latitude=[];var a=i.pathData=Kc(i),o=ow.default(a);o._path.forEach(function(l){i.longitude.push(l[1]),i.latitude.push(l[2])});var s=Kc.centroid(i);i.centroidX=s[0],i.centroidY=s[1]}),t}$o.DataSet.registerConnector("geo",Mo);$o.DataSet.registerConnector("geojson",Mo);$o.DataSet.registerConnector("GeoJSON",Mo);ts.default=Mo;var Fi=or,is=ir,lw={nodes:function(r){return r.nodes},edges:function(r){return r.edges}};function lh(r,n,e){n=Fi.assign({},lw,n),e.dataType=is.DataSet.CONSTANTS.GRAPH;var t=n.nodes,i=n.edges;if(t&&!Fi.isFunction(t))throw new TypeError("Invalid nodes: must be a function!");if(i&&!Fi.isFunction(i))throw new TypeError("Invalid edges: must be a function!");return e.rows=e.graph={nodes:t(r),edges:i(r)},Fi.assign(e,e.graph),e.rows}is.DataSet.registerConnector("graph",lh);is.DataSet.registerConnector("diagram",lh);var sh={};function sw(r,n,e){var t=r.layout,i=[],a=0;Object.keys(r.hexes).forEach(function(p){r.hexes[p].key=p,i.push(r.hexes[p])});var o=me(i,function(p){return+p.q}),s=rn(i,function(p){return+p.q}),l=me(i,function(p){return+p.r}),c=rn(i,function(p){return+p.r}),u=o-s+1,v=l-c+1;t==="odd-r"||t==="even-r"?a=rn([n/((u+.5)*Math.sqrt(3)),e/((v+1/3)*1.5)]):a=rn([e/((v+.5)*Math.sqrt(3)),n/((u+1/3)*1.5)]);var f=a*Math.sqrt(3),h=cw(t,f,a),g=vw(h);return i.forEach(function(p){p.qc=p.q-s,p.rc=l-p.r,p.x=as(p,t,f,a),p.y=os(p,t,f,a),p.vertices=h,p.points=g}),i}function as(r,n,e,t){var i=0,a=0;switch(n){case"odd-r":a=r.rc%2===1?e:e/2,i=r.qc*e+a;break;case"even-r":a=r.rc%2===0?e:e/2,i=r.qc*e+a;break;case"odd-q":case"even-q":i=r.qc*t*1.5+t;break}return i}function os(r,n,e,t){var i=0,a=0;switch(n){case"odd-r":case"even-r":i=r.rc*t*1.5+t;break;case"odd-q":a=r.qc%2===1?e:e/2,i=r.rc*e+a;break;case"even-q":a=r.qc%2===0?e:e/2,i=r.rc*e+a;break}return i}function cw(r,n,e){var t=[];switch(r){case"odd-r":case"even-r":t.push({x:0,y:0-e}),t.push({x:0+n*.5,y:0-.5*e}),t.push({x:0+n*.5,y:0+.5*e}),t.push({x:0,y:0+e}),t.push({x:0-n*.5,y:0+.5*e}),t.push({x:0-n*.5,y:0-.5*e});break;case"odd-q":case"even-q":t.push({x:0-e,y:0}),t.push({x:0-.5*e,y:0-n*.5}),t.push({x:0+.5*e,y:0-n*.5}),t.push({x:0+e,y:0}),t.push({x:0+.5*e,y:0+n*.5}),t.push({x:0-.5*e,y:0+n*.5});break}return t}function vw(r){var n="";return r.forEach(function(e){n+=e.x+","+e.y+" "}),n.substring(0,n.length-1)}function hw(r){var n={};n.layout=r.layout,n.hexes={};var e=[];Object.keys(r.hexes).forEach(function(u){e.push(r.hexes[u])});var t=me(e,function(u){return+u.q}),i=rn(e,function(u){return+u.q}),a=me(e,function(u){return+u.r}),o=rn(e,function(u){return+u.r}),s,l,c;for(s=i;s<=t;s++)for(l=o;l<=a;l++)c="Q"+s+"R"+l,n.hexes[c]={q:s,r:l};return n}function gw(r,n,e,t){var i=[],a=r.layout;Object.keys(r.hexes).forEach(function(C){i.push(r.hexes[C])});var o=me(i,function(C){return+C.q}),s=rn(i,function(C){return+C.q}),l=me(i,function(C){return+C.r}),c=rn(i,function(C){return+C.r}),u=o-s+1,v=l-c+1,f;a==="odd-r"||a==="even-r"?f=rn([n/((u+.5)*Math.sqrt(3)),e/((v+1/3)*1.5)]):f=rn([e/((v+.5)*Math.sqrt(3)),n/((u+1/3)*1.5)]);var h=f*Math.sqrt(3),g=[],p=f*f*4,d=i.length;if(d>1){i.forEach(function(C){C.qc=C.q-s,C.rc=l-C.r,C.x=as(C,a,h,f),C.y=os(C,a,h,f)});for(var m=0;m<d-1;m++)for(var y=m+1;y<d;y++){var E=i[m],S=i[y];if(E[t]!==S[t]&&Math.abs(E.q-S.q)<=1&&Math.abs(E.r-S.r)<=1&&(E.x-S.x)*(E.x-S.x)+(E.y-S.y)*(E.y-S.y)<p){var $={};$.x=S.x+(E.x-S.x)/2,$.y=S.y+(E.y-S.y)/2;var x={},I=Math.sqrt(3)*4;x.dx=(E.y-S.y)/I,x.dy=-(E.x-S.x)/I,g.push({x:$.x-2*x.dx,y:$.y-2*x.dy}),g.push({x:$.x-x.dx,y:$.y-x.dy}),g.push({x:$.x,y:$.y}),g.push({x:$.x+x.dx,y:$.y+x.dy}),g.push({x:$.x+2*x.dx,y:$.y+2*x.dy})}}}return g}function pw(r,n,e,t){var i=[],a=r.layout;Object.keys(r.hexes).forEach(function(L){i.push(r.hexes[L])});var o=me(i,function(L){return+L.q}),s=rn(i,function(L){return+L.q}),l=me(i,function(L){return+L.r}),c=rn(i,function(L){return+L.r}),u=o-s+1,v=l-c+1,f;a==="odd-r"||a==="even-r"?f=rn([n/((u+.5)*Math.sqrt(3)),e/((v+1/3)*1.5)]):f=rn([e/((v+.5)*Math.sqrt(3)),n/((u+1/3)*1.5)]);var h=f*Math.sqrt(3),g=[],p=f*f*4,d=i.length;if(d>1){i.forEach(function(L){L.qc=L.q-s,L.rc=l-L.r,L.x=as(L,a,h,f),L.y=os(L,a,h,f)});for(var m=0;m<d-1;m++)for(var y=m+1;y<d;y++){var E=i[m],S=i[y];if(E[t]!==S[t]&&Math.abs(E.q-S.q)<=1&&Math.abs(E.r-S.r)<=1&&(E.x-S.x)*(E.x-S.x)+(E.y-S.y)*(E.y-S.y)<p){var $={};$.x=S.x+(E.x-S.x)/2,$.y=S.y+(E.y-S.y)/2;var x={},I=1;E[t]<S[t]&&(I=-1);var C=Math.sqrt(3)*2*I;x.dx=(E.y-S.y)/C,x.dy=-(E.x-S.x)/C,g.push({x1:$.x-x.dx,y1:$.y-x.dy,x2:$.x+x.dx,y2:$.y+x.dy})}}}return g}var dw=Object.freeze(Object.defineProperty({__proto__:null,renderHexJSON:sw,getGridForHexJSON:hw,getBoundaryDotsForHexJSON:gw,getBoundarySegmentsForHexJSON:pw},Symbol.toStringTag,{value:"Module"})),mw=oe(dw);Object.defineProperty(sh,"__esModule",{value:!0});var Qc=or,vu=mw,pi=ir,yw={width:1,height:1};function Zc(r){return r.cx=r.x,r.cy=r.y,r.x=[],r.y=[],r.vertices.forEach(function(n){r.x.push(n.x+r.cx),r.y.push(n.y+r.cy)}),r}function di(r,n,e){e.dataType=pi.DataSet.CONSTANTS.HEX,n=Qc.assign({},yw,n);var t=n.width,i=n.height,a=Qc.deepMix([],r);e._HexJSON=a;var o=e._GridHexJSON=vu.getGridForHexJSON(a),s=e.rows=vu.renderHexJSON(a,t,i).map(Zc);return e._gridRows=vu.renderHexJSON(o,t,i).map(Zc),s}pi.DataSet.registerConnector("hex",di);pi.DataSet.registerConnector("hexjson",di);pi.DataSet.registerConnector("hex-json",di);pi.DataSet.registerConnector("HexJSON",di);sh.default=di;function ww(r,n){return r.parent===n.parent?1:2}function Ew(r){return r.reduce(Sw,0)/r.length}function Sw(r,n){return r+n.x}function _w(r){return 1+r.reduce(Tw,0)}function Tw(r,n){return Math.max(r,n.y)}function $w(r){for(var n;n=r.children;)r=n[0];return r}function Mw(r){for(var n;n=r.children;)r=n[n.length-1];return r}function xw(){var r=ww,n=1,e=1,t=!1;function i(a){var o,s=0;a.eachAfter(function(f){var h=f.children;h?(f.x=Ew(h),f.y=_w(h)):(f.x=o?s+=r(f,o):0,f.y=0,o=f)});var l=$w(a),c=Mw(a),u=l.x-r(l,c)/2,v=c.x+r(c,l)/2;return a.eachAfter(t?function(f){f.x=(f.x-a.x)*n,f.y=(a.y-f.y)*e}:function(f){f.x=(f.x-u)/(v-u)*n,f.y=(1-(a.y?f.y/a.y:1))*e})}return i.separation=function(a){return arguments.length?(r=a,i):r},i.size=function(a){return arguments.length?(t=!1,n=+a[0],e=+a[1],i):t?null:[n,e]},i.nodeSize=function(a){return arguments.length?(t=!0,n=+a[0],e=+a[1],i):t?[n,e]:null},i}function bw(r){var n=0,e=r.children,t=e&&e.length;if(!t)n=1;else for(;--t>=0;)n+=e[t].value;r.value=n}function Pw(){return this.eachAfter(bw)}function Rw(r){var n=this,e,t=[n],i,a,o;do for(e=t.reverse(),t=[];n=e.pop();)if(r(n),i=n.children,i)for(a=0,o=i.length;a<o;++a)t.push(i[a]);while(t.length);return this}function kw(r){for(var n=this,e=[n],t,i;n=e.pop();)if(r(n),t=n.children,t)for(i=t.length-1;i>=0;--i)e.push(t[i]);return this}function Cw(r){for(var n=this,e=[n],t=[],i,a,o;n=e.pop();)if(t.push(n),i=n.children,i)for(a=0,o=i.length;a<o;++a)e.push(i[a]);for(;n=t.pop();)r(n);return this}function Lw(r){return this.eachAfter(function(n){for(var e=+r(n.data)||0,t=n.children,i=t&&t.length;--i>=0;)e+=t[i].value;n.value=e})}function Iw(r){return this.eachBefore(function(n){n.children&&n.children.sort(r)})}function Nw(r){for(var n=this,e=Aw(n,r),t=[n];n!==e;)n=n.parent,t.push(n);for(var i=t.length;r!==e;)t.splice(i,0,r),r=r.parent;return t}function Aw(r,n){if(r===n)return r;var e=r.ancestors(),t=n.ancestors(),i=null;for(r=e.pop(),n=t.pop();r===n;)i=r,r=e.pop(),n=t.pop();return i}function Dw(){for(var r=this,n=[r];r=r.parent;)n.push(r);return n}function Ow(){var r=[];return this.each(function(n){r.push(n)}),r}function qw(){var r=[];return this.eachBefore(function(n){n.children||r.push(n)}),r}function Fw(){var r=this,n=[];return r.each(function(e){e!==r&&n.push({source:e.parent,target:e})}),n}function us(r,n){var e=new vt(r),t=+r.value&&(e.value=r.value),i,a=[e],o,s,l,c;for(n==null&&(n=Bw);i=a.pop();)if(t&&(i.value=+i.data.value),(s=n(i.data))&&(c=s.length))for(i.children=new Array(c),l=c-1;l>=0;--l)a.push(o=i.children[l]=new vt(s[l])),o.parent=i,o.depth=i.depth+1;return e.eachBefore(ch)}function zw(){return us(this).eachBefore(Gw)}function Bw(r){return r.children}function Gw(r){r.data=r.data.data}function ch(r){var n=0;do r.height=n;while((r=r.parent)&&r.height<++n)}function vt(r){this.data=r,this.depth=this.height=0,this.parent=null}vt.prototype=us.prototype={constructor:vt,count:Pw,each:Rw,eachAfter:Cw,eachBefore:kw,sum:Lw,sort:Iw,path:Nw,ancestors:Dw,descendants:Ow,leaves:qw,links:Fw,copy:zw};var Uw=Array.prototype.slice;function Hw(r){for(var n=r.length,e,t;n;)t=Math.random()*n--|0,e=r[n],r[n]=r[t],r[t]=e;return r}function vh(r){for(var n=0,e=(r=Hw(Uw.call(r))).length,t=[],i,a;n<e;)i=r[n],a&&hh(a,i)?++n:(a=Vw(t=Yw(t,i)),n=0);return a}function Yw(r,n){var e,t;if(hu(n,r))return[n];for(e=0;e<r.length;++e)if(zi(n,r[e])&&hu(Gt(r[e],n),r))return[r[e],n];for(e=0;e<r.length-1;++e)for(t=e+1;t<r.length;++t)if(zi(Gt(r[e],r[t]),n)&&zi(Gt(r[e],n),r[t])&&zi(Gt(r[t],n),r[e])&&hu(gh(r[e],r[t],n),r))return[r[e],r[t],n];throw new Error}function zi(r,n){var e=r.r-n.r,t=n.x-r.x,i=n.y-r.y;return e<0||e*e<t*t+i*i}function hh(r,n){var e=r.r-n.r+1e-6,t=n.x-r.x,i=n.y-r.y;return e>0&&e*e>t*t+i*i}function hu(r,n){for(var e=0;e<n.length;++e)if(!hh(r,n[e]))return!1;return!0}function Vw(r){switch(r.length){case 1:return Xw(r[0]);case 2:return Gt(r[0],r[1]);case 3:return gh(r[0],r[1],r[2])}}function Xw(r){return{x:r.x,y:r.y,r:r.r}}function Gt(r,n){var e=r.x,t=r.y,i=r.r,a=n.x,o=n.y,s=n.r,l=a-e,c=o-t,u=s-i,v=Math.sqrt(l*l+c*c);return{x:(e+a+l/v*u)/2,y:(t+o+c/v*u)/2,r:(v+i+s)/2}}function gh(r,n,e){var t=r.x,i=r.y,a=r.r,o=n.x,s=n.y,l=n.r,c=e.x,u=e.y,v=e.r,f=t-o,h=t-c,g=i-s,p=i-u,d=l-a,m=v-a,y=t*t+i*i-a*a,E=y-o*o-s*s+l*l,S=y-c*c-u*u+v*v,$=h*g-f*p,x=(g*S-p*E)/($*2)-t,I=(p*d-g*m)/$,C=(h*E-f*S)/($*2)-i,L=(f*m-h*d)/$,O=I*I+L*L-1,A=2*(a+x*I+C*L),k=x*x+C*C-a*a,U=-(O?(A+Math.sqrt(A*A-4*O*k))/(2*O):k/A);return{x:t+x+I*U,y:i+C+L*U,r:U}}function r0(r,n,e){var t=r.x-n.x,i,a,o=r.y-n.y,s,l,c=t*t+o*o;c?(a=n.r+e.r,a*=a,l=r.r+e.r,l*=l,a>l?(i=(c+l-a)/(2*c),s=Math.sqrt(Math.max(0,l/c-i*i)),e.x=r.x-i*t-s*o,e.y=r.y-i*o+s*t):(i=(c+a-l)/(2*c),s=Math.sqrt(Math.max(0,a/c-i*i)),e.x=n.x+i*t-s*o,e.y=n.y+i*o+s*t)):(e.x=n.x+e.r,e.y=n.y)}function n0(r,n){var e=r.r+n.r-1e-6,t=n.x-r.x,i=n.y-r.y;return e>0&&e*e>t*t+i*i}function e0(r){var n=r._,e=r.next._,t=n.r+e.r,i=(n.x*e.r+e.x*n.r)/t,a=(n.y*e.r+e.y*n.r)/t;return i*i+a*a}function Bi(r){this._=r,this.next=null,this.previous=null}function ph(r){if(!(i=r.length))return 0;var n,e,t,i,a,o,s,l,c,u,v;if(n=r[0],n.x=0,n.y=0,!(i>1))return n.r;if(e=r[1],n.x=-e.r,e.x=n.r,e.y=0,!(i>2))return n.r+e.r;r0(e,n,t=r[2]),n=new Bi(n),e=new Bi(e),t=new Bi(t),n.next=t.previous=e,e.next=n.previous=t,t.next=e.previous=n;r:for(s=3;s<i;++s){r0(n._,e._,t=r[s]),t=new Bi(t),l=e.next,c=n.previous,u=e._.r,v=n._.r;do if(u<=v){if(n0(l._,t._)){e=l,n.next=e,e.previous=n,--s;continue r}u+=l._.r,l=l.next}else{if(n0(c._,t._)){n=c,n.next=e,e.previous=n,--s;continue r}v+=c._.r,c=c.previous}while(l!==c.next);for(t.previous=n,t.next=e,n.next=e.previous=e=t,a=e0(n);(t=t.next)!==e;)(o=e0(t))<a&&(n=t,a=o);e=n.next}for(n=[e._],t=e;(t=t.next)!==e;)n.push(t._);for(t=vh(n),s=0;s<i;++s)n=r[s],n.x-=t.x,n.y-=t.y;return t.r}function jw(r){return ph(r),r}function Ww(r){return r==null?null:Ja(r)}function Ja(r){if(typeof r!="function")throw new Error;return r}function Ie(){return 0}function Ve(r){return function(){return r}}function Jw(r){return Math.sqrt(r.value)}function Kw(){var r=null,n=1,e=1,t=Ie;function i(a){return a.x=n/2,a.y=e/2,r?a.eachBefore(t0(r)).eachAfter(gu(t,.5)).eachBefore(i0(1)):a.eachBefore(t0(Jw)).eachAfter(gu(Ie,1)).eachAfter(gu(t,a.r/Math.min(n,e))).eachBefore(i0(Math.min(n,e)/(2*a.r))),a}return i.radius=function(a){return arguments.length?(r=Ww(a),i):r},i.size=function(a){return arguments.length?(n=+a[0],e=+a[1],i):[n,e]},i.padding=function(a){return arguments.length?(t=typeof a=="function"?a:Ve(+a),i):t},i}function t0(r){return function(n){n.children||(n.r=Math.max(0,+r(n)||0))}}function gu(r,n){return function(e){if(t=e.children){var t,i,a=t.length,o=r(e)*n||0,s;if(o)for(i=0;i<a;++i)t[i].r+=o;if(s=ph(t),o)for(i=0;i<a;++i)t[i].r-=o;e.r=s+o}}}function i0(r){return function(n){var e=n.parent;n.r*=r,e&&(n.x=e.x+r*n.x,n.y=e.y+r*n.y)}}function dh(r){r.x0=Math.round(r.x0),r.y0=Math.round(r.y0),r.x1=Math.round(r.x1),r.y1=Math.round(r.y1)}function mi(r,n,e,t,i){for(var a=r.children,o,s=-1,l=a.length,c=r.value&&(t-n)/r.value;++s<l;)o=a[s],o.y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*c}function Qw(){var r=1,n=1,e=0,t=!1;function i(o){var s=o.height+1;return o.x0=o.y0=e,o.x1=r,o.y1=n/s,o.eachBefore(a(n,s)),t&&o.eachBefore(dh),o}function a(o,s){return function(l){l.children&&mi(l,l.x0,o*(l.depth+1)/s,l.x1,o*(l.depth+2)/s);var c=l.x0,u=l.y0,v=l.x1-e,f=l.y1-e;v<c&&(c=v=(c+v)/2),f<u&&(u=f=(u+f)/2),l.x0=c,l.y0=u,l.x1=v,l.y1=f}}return i.round=function(o){return arguments.length?(t=!!o,i):t},i.size=function(o){return arguments.length?(r=+o[0],n=+o[1],i):[r,n]},i.padding=function(o){return arguments.length?(e=+o,i):e},i}var a0="$",Zw={depth:-1},o0={};function r3(r){return r.id}function n3(r){return r.parentId}function e3(){var r=r3,n=n3;function e(t){var i,a,o=t.length,s,l,c,u=new Array(o),v,f,h={};for(a=0;a<o;++a)i=t[a],c=u[a]=new vt(i),(v=r(i,a,t))!=null&&(v+="")&&(f=a0+(c.id=v),h[f]=f in h?o0:c);for(a=0;a<o;++a)if(c=u[a],v=n(t[a],a,t),v==null||!(v+="")){if(s)throw new Error("multiple roots");s=c}else{if(l=h[a0+v],!l)throw new Error("missing: "+v);if(l===o0)throw new Error("ambiguous: "+v);l.children?l.children.push(c):l.children=[c],c.parent=l}if(!s)throw new Error("no root");if(s.parent=Zw,s.eachBefore(function(g){g.depth=g.parent.depth+1,--o}).eachBefore(ch),s.parent=null,o>0)throw new Error("cycle");return s}return e.id=function(t){return arguments.length?(r=Ja(t),e):r},e.parentId=function(t){return arguments.length?(n=Ja(t),e):n},e}function t3(r,n){return r.parent===n.parent?1:2}function pu(r){var n=r.children;return n?n[0]:r.t}function du(r){var n=r.children;return n?n[n.length-1]:r.t}function i3(r,n,e){var t=e/(n.i-r.i);n.c-=t,n.s+=e,r.c+=t,n.z+=e,n.m+=e}function a3(r){for(var n=0,e=0,t=r.children,i=t.length,a;--i>=0;)a=t[i],a.z+=n,a.m+=n,n+=a.s+(e+=a.c)}function o3(r,n,e){return r.a.parent===n.parent?r.a:e}function oa(r,n){this._=r,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}oa.prototype=Object.create(vt.prototype);function u3(r){for(var n=new oa(r,0),e,t=[n],i,a,o,s;e=t.pop();)if(a=e._.children)for(e.children=new Array(s=a.length),o=s-1;o>=0;--o)t.push(i=e.children[o]=new oa(a[o],o)),i.parent=e;return(n.parent=new oa(null,0)).children=[n],n}function f3(){var r=t3,n=1,e=1,t=null;function i(c){var u=u3(c);if(u.eachAfter(a),u.parent.m=-u.z,u.eachBefore(o),t)c.eachBefore(l);else{var v=c,f=c,h=c;c.eachBefore(function(y){y.x<v.x&&(v=y),y.x>f.x&&(f=y),y.depth>h.depth&&(h=y)});var g=v===f?1:r(v,f)/2,p=g-v.x,d=n/(f.x+g+p),m=e/(h.depth||1);c.eachBefore(function(y){y.x=(y.x+p)*d,y.y=y.depth*m})}return c}function a(c){var u=c.children,v=c.parent.children,f=c.i?v[c.i-1]:null;if(u){a3(c);var h=(u[0].z+u[u.length-1].z)/2;f?(c.z=f.z+r(c._,f._),c.m=c.z-h):c.z=h}else f&&(c.z=f.z+r(c._,f._));c.parent.A=s(c,f,c.parent.A||v[0])}function o(c){c._.x=c.z+c.parent.m,c.m+=c.parent.m}function s(c,u,v){if(u){for(var f=c,h=c,g=u,p=f.parent.children[0],d=f.m,m=h.m,y=g.m,E=p.m,S;g=du(g),f=pu(f),g&&f;)p=pu(p),h=du(h),h.a=c,S=g.z+y-f.z-d+r(g._,f._),S>0&&(i3(o3(g,c,v),c,S),d+=S,m+=S),y+=g.m,d+=f.m,E+=p.m,m+=h.m;g&&!du(h)&&(h.t=g,h.m+=y-m),f&&!pu(p)&&(p.t=f,p.m+=d-E,v=c)}return v}function l(c){c.x*=n,c.y=c.depth*e}return i.separation=function(c){return arguments.length?(r=c,i):r},i.size=function(c){return arguments.length?(t=!1,n=+c[0],e=+c[1],i):t?null:[n,e]},i.nodeSize=function(c){return arguments.length?(t=!0,n=+c[0],e=+c[1],i):t?[n,e]:null},i}function xo(r,n,e,t,i){for(var a=r.children,o,s=-1,l=a.length,c=r.value&&(i-e)/r.value;++s<l;)o=a[s],o.x0=n,o.x1=t,o.y0=e,o.y1=e+=o.value*c}var mh=(1+Math.sqrt(5))/2;function yh(r,n,e,t,i,a){for(var o=[],s=n.children,l,c,u=0,v=0,f=s.length,h,g,p=n.value,d,m,y,E,S,$,x;u<f;){h=i-e,g=a-t;do d=s[v++].value;while(!d&&v<f);for(m=y=d,$=Math.max(g/h,h/g)/(p*r),x=d*d*$,S=Math.max(y/x,x/m);v<f;++v){if(d+=c=s[v].value,c<m&&(m=c),c>y&&(y=c),x=d*d*$,E=Math.max(y/x,x/m),E>S){d-=c;break}S=E}o.push(l={value:d,dice:h<g,children:s.slice(u,v)}),l.dice?mi(l,e,t,i,p?t+=g*d/p:a):xo(l,e,t,p?e+=h*d/p:i,a),p-=d,u=v}return o}var wh=function r(n){function e(t,i,a,o,s){yh(n,t,i,a,o,s)}return e.ratio=function(t){return r((t=+t)>1?t:1)},e}(mh);function l3(){var r=wh,n=!1,e=1,t=1,i=[0],a=Ie,o=Ie,s=Ie,l=Ie,c=Ie;function u(f){return f.x0=f.y0=0,f.x1=e,f.y1=t,f.eachBefore(v),i=[0],n&&f.eachBefore(dh),f}function v(f){var h=i[f.depth],g=f.x0+h,p=f.y0+h,d=f.x1-h,m=f.y1-h;d<g&&(g=d=(g+d)/2),m<p&&(p=m=(p+m)/2),f.x0=g,f.y0=p,f.x1=d,f.y1=m,f.children&&(h=i[f.depth+1]=a(f)/2,g+=c(f)-h,p+=o(f)-h,d-=s(f)-h,m-=l(f)-h,d<g&&(g=d=(g+d)/2),m<p&&(p=m=(p+m)/2),r(f,g,p,d,m))}return u.round=function(f){return arguments.length?(n=!!f,u):n},u.size=function(f){return arguments.length?(e=+f[0],t=+f[1],u):[e,t]},u.tile=function(f){return arguments.length?(r=Ja(f),u):r},u.padding=function(f){return arguments.length?u.paddingInner(f).paddingOuter(f):u.paddingInner()},u.paddingInner=function(f){return arguments.length?(a=typeof f=="function"?f:Ve(+f),u):a},u.paddingOuter=function(f){return arguments.length?u.paddingTop(f).paddingRight(f).paddingBottom(f).paddingLeft(f):u.paddingTop()},u.paddingTop=function(f){return arguments.length?(o=typeof f=="function"?f:Ve(+f),u):o},u.paddingRight=function(f){return arguments.length?(s=typeof f=="function"?f:Ve(+f),u):s},u.paddingBottom=function(f){return arguments.length?(l=typeof f=="function"?f:Ve(+f),u):l},u.paddingLeft=function(f){return arguments.length?(c=typeof f=="function"?f:Ve(+f),u):c},u}function s3(r,n,e,t,i){var a=r.children,o,s=a.length,l,c=new Array(s+1);for(c[0]=l=o=0;o<s;++o)c[o+1]=l+=a[o].value;u(0,s,r.value,n,e,t,i);function u(v,f,h,g,p,d,m){if(v>=f-1){var y=a[v];y.x0=g,y.y0=p,y.x1=d,y.y1=m;return}for(var E=c[v],S=h/2+E,$=v+1,x=f-1;$<x;){var I=$+x>>>1;c[I]<S?$=I+1:x=I}S-c[$-1]<c[$]-S&&v+1<$&&--$;var C=c[$]-E,L=h-C;if(d-g>m-p){var O=(g*L+d*C)/h;u(v,$,C,g,p,O,m),u($,f,L,O,p,d,m)}else{var A=(p*L+m*C)/h;u(v,$,C,g,p,d,A),u($,f,L,g,A,d,m)}}}function c3(r,n,e,t,i){(r.depth&1?xo:mi)(r,n,e,t,i)}var v3=function r(n){function e(t,i,a,o,s){if((l=t._squarify)&&l.ratio===n)for(var l,c,u,v,f=-1,h,g=l.length,p=t.value;++f<g;){for(c=l[f],u=c.children,v=c.value=0,h=u.length;v<h;++v)c.value+=u[v].value;c.dice?mi(c,i,a,o,a+=(s-a)*c.value/p):xo(c,i,a,i+=(o-i)*c.value/p,s),p-=c.value}else t._squarify=l=yh(n,t,i,a,o,s),l.ratio=n}return e.ratio=function(t){return r((t=+t)>1?t:1)},e}(mh),h3=Object.freeze(Object.defineProperty({__proto__:null,cluster:xw,hierarchy:us,pack:Kw,packSiblings:jw,packEnclose:vh,partition:Qw,stratify:e3,tree:f3,treemap:l3,treemapBinary:s3,treemapDice:mi,treemapSlice:xo,treemapSliceDice:c3,treemapSquarify:wh,treemapResquarify:v3},Symbol.toStringTag,{value:"Module"})),wt=oe(h3),g3=or,p3=wt,fs=ir;function Eh(r,n,e){e.dataType=fs.DataSet.CONSTANTS.HIERARCHY;var t=n&&n.children?n.children:null;if(t&&!g3.isFunction(t))throw new TypeError("Invalid children: must be a function!");return n.pureData?e.rows=e.root=r:e.rows=e.root=p3.hierarchy(r,t),r}fs.DataSet.registerConnector("hierarchy",Eh);fs.DataSet.registerConnector("tree",Eh);function Sh(r){return r}function ls(r){if(r==null)return Sh;var n,e,t=r.scale[0],i=r.scale[1],a=r.translate[0],o=r.translate[1];return function(s,l){l||(n=e=0);var c=2,u=s.length,v=new Array(u);for(v[0]=(n+=s[0])*t+a,v[1]=(e+=s[1])*i+o;c<u;)v[c]=s[c],++c;return v}}function _h(r){var n=ls(r.transform),e,t=1/0,i=t,a=-t,o=-t;function s(c){c=n(c),c[0]<t&&(t=c[0]),c[0]>a&&(a=c[0]),c[1]<i&&(i=c[1]),c[1]>o&&(o=c[1])}function l(c){switch(c.type){case"GeometryCollection":c.geometries.forEach(l);break;case"Point":s(c.coordinates);break;case"MultiPoint":c.coordinates.forEach(s);break}}r.arcs.forEach(function(c){for(var u=-1,v=c.length,f;++u<v;)f=n(c[u],u),f[0]<t&&(t=f[0]),f[0]>a&&(a=f[0]),f[1]<i&&(i=f[1]),f[1]>o&&(o=f[1])});for(e in r.objects)l(r.objects[e]);return[t,i,a,o]}function d3(r,n){for(var e,t=r.length,i=t-n;i<--t;)e=r[i],r[i++]=r[t],r[t]=e}function m3(r,n){return typeof n=="string"&&(n=r.objects[n]),n.type==="GeometryCollection"?{type:"FeatureCollection",features:n.geometries.map(function(e){return u0(r,e)})}:u0(r,n)}function u0(r,n){var e=n.id,t=n.bbox,i=n.properties==null?{}:n.properties,a=bo(r,n);return e==null&&t==null?{type:"Feature",properties:i,geometry:a}:t==null?{type:"Feature",id:e,properties:i,geometry:a}:{type:"Feature",id:e,bbox:t,properties:i,geometry:a}}function bo(r,n){var e=ls(r.transform),t=r.arcs;function i(u,v){v.length&&v.pop();for(var f=t[u<0?~u:u],h=0,g=f.length;h<g;++h)v.push(e(f[h],h));u<0&&d3(v,g)}function a(u){return e(u)}function o(u){for(var v=[],f=0,h=u.length;f<h;++f)i(u[f],v);return v.length<2&&v.push(v[0]),v}function s(u){for(var v=o(u);v.length<4;)v.push(v[0]);return v}function l(u){return u.map(s)}function c(u){var v=u.type,f;switch(v){case"GeometryCollection":return{type:v,geometries:u.geometries.map(c)};case"Point":f=a(u.coordinates);break;case"MultiPoint":f=u.coordinates.map(a);break;case"LineString":f=o(u.arcs);break;case"MultiLineString":f=u.arcs.map(o);break;case"Polygon":f=l(u.arcs);break;case"MultiPolygon":f=u.arcs.map(l);break;default:return null}return{type:v,coordinates:f}}return c(n)}function Th(r,n){var e={},t={},i={},a=[],o=-1;n.forEach(function(c,u){var v=r.arcs[c<0?~c:c],f;v.length<3&&!v[1][0]&&!v[1][1]&&(f=n[++o],n[o]=c,n[u]=f)}),n.forEach(function(c){var u=s(c),v=u[0],f=u[1],h,g;if(h=i[v])if(delete i[h.end],h.push(c),h.end=f,g=t[f]){delete t[g.start];var p=g===h?h:h.concat(g);t[p.start=h.start]=i[p.end=g.end]=p}else t[h.start]=i[h.end]=h;else if(h=t[f])if(delete t[h.start],h.unshift(c),h.start=v,g=i[v]){delete i[g.end];var d=g===h?h:g.concat(h);t[d.start=g.start]=i[d.end=h.end]=d}else t[h.start]=i[h.end]=h;else h=[c],t[h.start=v]=i[h.end=f]=h});function s(c){var u=r.arcs[c<0?~c:c],v=u[0],f;return r.transform?(f=[0,0],u.forEach(function(h){f[0]+=h[0],f[1]+=h[1]})):f=u[u.length-1],c<0?[f,v]:[v,f]}function l(c,u){for(var v in c){var f=c[v];delete u[f.start],delete f.start,delete f.end,f.forEach(function(h){e[h<0?~h:h]=1}),a.push(f)}}return l(i,t),l(t,i),n.forEach(function(c){e[c<0?~c:c]||a.push([c])}),a}function y3(r){return bo(r,$h.apply(this,arguments))}function $h(r,n,e){var t,i,a;if(arguments.length>1)t=w3(r,n,e);else for(i=0,t=new Array(a=r.arcs.length);i<a;++i)t[i]=i;return{type:"MultiLineString",arcs:Th(r,t)}}function w3(r,n,e){var t=[],i=[],a;function o(v){var f=v<0?~v:v;(i[f]||(i[f]=[])).push({i:v,g:a})}function s(v){v.forEach(o)}function l(v){v.forEach(s)}function c(v){v.forEach(l)}function u(v){switch(a=v,v.type){case"GeometryCollection":v.geometries.forEach(u);break;case"LineString":s(v.arcs);break;case"MultiLineString":case"Polygon":l(v.arcs);break;case"MultiPolygon":c(v.arcs);break}}return u(n),i.forEach(e==null?function(v){t.push(v[0].i)}:function(v){e(v[0].g,v[v.length-1].g)&&t.push(v[0].i)}),t}function E3(r){for(var n=-1,e=r.length,t,i=r[e-1],a=0;++n<e;)t=i,i=r[n],a+=t[0]*i[1]-t[1]*i[0];return Math.abs(a)}function S3(r){return bo(r,Mh.apply(this,arguments))}function Mh(r,n){var e={},t=[],i=[];n.forEach(a);function a(l){switch(l.type){case"GeometryCollection":l.geometries.forEach(a);break;case"Polygon":o(l.arcs);break;case"MultiPolygon":l.arcs.forEach(o);break}}function o(l){l.forEach(function(c){c.forEach(function(u){(e[u=u<0?~u:u]||(e[u]=[])).push(l)})}),t.push(l)}function s(l){return E3(bo(r,{type:"Polygon",arcs:[l]}).coordinates[0])}return t.forEach(function(l){if(!l._){var c=[],u=[l];for(l._=1,i.push(c);l=u.pop();)c.push(l),l.forEach(function(v){v.forEach(function(f){e[f<0?~f:f].forEach(function(h){h._||(h._=1,u.push(h))})})})}}),t.forEach(function(l){delete l._}),{type:"MultiPolygon",arcs:i.map(function(l){var c=[],u;if(l.forEach(function(p){p.forEach(function(d){d.forEach(function(m){e[m<0?~m:m].length<2&&c.push(m)})})}),c=Th(r,c),(u=c.length)>1)for(var v=1,f=s(c[0]),h,g;v<u;++v)(h=s(c[v]))>f&&(g=c[0],c[0]=c[v],c[v]=g,f=h);return c}).filter(function(l){return l.length>0})}}function f0(r,n){for(var e=0,t=r.length;e<t;){var i=e+t>>>1;r[i]<n?e=i+1:t=i}return e}function _3(r){var n={},e=r.map(function(){return[]});function t(p,d){p.forEach(function(m){m<0&&(m=~m);var y=n[m];y?y.push(d):n[m]=[d]})}function i(p,d){p.forEach(function(m){t(m,d)})}function a(p,d){p.type==="GeometryCollection"?p.geometries.forEach(function(m){a(m,d)}):p.type in o&&o[p.type](p.arcs,d)}var o={LineString:t,MultiLineString:i,Polygon:i,MultiPolygon:function(p,d){p.forEach(function(m){i(m,d)})}};r.forEach(a);for(var s in n)for(var l=n[s],c=l.length,u=0;u<c;++u)for(var v=u+1;v<c;++v){var f=l[u],h=l[v],g;(g=e[f])[s=f0(g,h)]!==h&&g.splice(s,0,h),(g=e[h])[s=f0(g,f)]!==f&&g.splice(s,0,f)}return e}function xh(r){if(r==null)return Sh;var n,e,t=r.scale[0],i=r.scale[1],a=r.translate[0],o=r.translate[1];return function(s,l){l||(n=e=0);var c=2,u=s.length,v=new Array(u),f=Math.round((s[0]-a)/t),h=Math.round((s[1]-o)/i);for(v[0]=f-n,n=f,v[1]=h-e,e=h;c<u;)v[c]=s[c],++c;return v}}function T3(r,n){if(r.transform)throw new Error("already quantized");if(!n||!n.scale){if(!((o=Math.floor(n))>=2))throw new Error("n must be \u22652");l=r.bbox||_h(r);var e=l[0],t=l[1],i=l[2],a=l[3],o;n={scale:[i-e?(i-e)/(o-1):1,a-t?(a-t)/(o-1):1],translate:[e,t]}}else l=r.bbox;var s=xh(n),l,c,u=r.objects,v={};function f(p){return s(p)}function h(p){var d;switch(p.type){case"GeometryCollection":d={type:"GeometryCollection",geometries:p.geometries.map(h)};break;case"Point":d={type:"Point",coordinates:f(p.coordinates)};break;case"MultiPoint":d={type:"MultiPoint",coordinates:p.coordinates.map(f)};break;default:return p}return p.id!=null&&(d.id=p.id),p.bbox!=null&&(d.bbox=p.bbox),p.properties!=null&&(d.properties=p.properties),d}function g(p){var d=0,m=1,y=p.length,E,S=new Array(y);for(S[0]=s(p[0],0);++d<y;)((E=s(p[d],d))[0]||E[1])&&(S[m++]=E);return m===1&&(S[m++]=[0,0]),S.length=m,S}for(c in u)v[c]=h(u[c]);return{type:"Topology",bbox:l,transform:n,objects:v,arcs:r.arcs.map(g)}}var $3=Object.freeze(Object.defineProperty({__proto__:null,bbox:_h,feature:m3,mesh:y3,meshArcs:$h,merge:S3,mergeArcs:Mh,neighbors:_3,quantize:T3,transform:ls,untransform:xh},Symbol.toStringTag,{value:"Module"})),M3=oe($3),x3=pr,b3=or,P3=M3,R3=x3.__importDefault(ts),bh=ir;function Ph(r,n,e){var t=n.object;if(!b3.isString(t))throw new TypeError("Invalid object: must be a string!");var i=P3.feature(r,r.objects[t]);return R3.default(i,void 0,e)}bh.DataSet.registerConnector("topojson",Ph);bh.DataSet.registerConnector("TopoJSON",Ph);var k3=ir;k3.DataSet.registerTransform("default",function(r){return r});var C3=ir;function L3(r){return!!r}C3.DataSet.registerTransform("filter",function(r,n){r.rows=r.rows.filter(n.callback||L3)});var Mr={};Object.defineProperty(Mr,"__esModule",{value:!0});Mr.getFields=Mr.getField=void 0;var pe=or,mu="Invalid field: it must be a string!",bt="Invalid fields: it must be an array!";function I3(r,n){var e=r.field,t=r.fields;if(pe.isString(e))return e;if(pe.isArray(e))return console.warn(mu),e[0];if(console.warn(mu+" will try to get fields instead."),pe.isString(t))return t;if(pe.isArray(t)&&t.length)return t[0];if(n)return n;throw new TypeError(mu)}Mr.getField=I3;function N3(r,n){var e=r.field,t=r.fields;if(pe.isArray(t))return t;if(pe.isString(t))return console.warn(bt),[t];if(console.warn(bt+" will try to get field instead."),pe.isString(e))return console.warn(bt),[e];if(pe.isArray(e)&&e.length)return console.warn(bt),e;if(n)return n;throw new TypeError(bt)}Mr.getFields=N3;var yu=or,A3=ir,D3=Mr,O3={fields:[],key:"key",retains:[],value:"value"};A3.DataSet.registerTransform("fold",function(r,n){var e=r.getColumnNames();n=yu.assign({},O3,n);var t=D3.getFields(n);t.length===0&&(console.warn("warning: option fields is not specified, will fold all columns."),t=e);var i=n.key,a=n.value,o=n.retains;(!o||o.length===0)&&(o=yu.difference(e,t));var s=[];r.rows.forEach(function(l){t.forEach(function(c){var u=yu.pick(l,o);u[i]=c,u[a]=l[c],s.push(u)})}),r.rows=s});var q3=ir;function F3(r){return r}q3.DataSet.registerTransform("map",function(r,n){r.rows=r.rows.map(n.callback||F3)});var z3=pr,Sf=or,Rh=z3.__importDefault(Tn),ss=ir,kh={groupBy:[],orderBy:[]};ss.DataSet.registerTransform("partition",function(r,n){n=Sf.assign({},kh,n),r.rows=Rh.default(r.rows,n.groupBy,n.orderBy)});function Ch(r,n){n=Sf.assign({},kh,n),r.rows=Sf.values(Rh.default(r.rows,n.groupBy,n.orderBy))}ss.DataSet.registerTransform("group",Ch);ss.DataSet.registerTransform("groups",Ch);var B3=pr,He=or,l0=Dn.exports,s0=B3.__importDefault(Tn),G3=ir,U3=Mr,H3={groupBy:[],as:"_percent"};function Y3(r,n){n=He.assign({},H3,n);var e=U3.getField(n),t=n.dimension,i=n.groupBy,a=n.as;if(!He.isString(t))throw new TypeError("Invalid dimension: must be a string!");if(He.isArray(a)&&(console.warn("Invalid as: must be a string, will use the first element of the array specified."),a=a[0]),!He.isString(a))throw new TypeError("Invalid as: must be a string!");var o=r.rows,s=[],l=s0.default(o,i);He.forIn(l,function(c){var u=l0.sum(c.map(function(f){return f[e]})),v=s0.default(c,[t]);He.forIn(v,function(f){var h=l0.sum(f.map(function(d){return d[e]})),g=f[0],p=g[t];g[e]=h,g[t]=p,u===0?g[a]=0:g[a]=h/u,s.push(g)})}),r.rows=s}G3.DataSet.registerTransform("percent",Y3);var V3=or,X3=ir,j3=Mr;X3.DataSet.registerTransform("pick",function(r,n){var e=j3.getFields(n,r.getColumnNames());r.rows=r.rows.map(function(t){return V3.pick(t,e)})});var W3=pr,Ye=or,c0=W3.__importDefault(Tn),J3=ir,K3=Mr,Q3={groupBy:[],as:"_proportion"};function Z3(r,n){n=Ye.assign({},Q3,n);var e=K3.getField(n),t=n.dimension,i=n.groupBy,a=n.as;if(!Ye.isString(t))throw new TypeError("Invalid dimension: must be a string!");if(Ye.isArray(a)&&(console.warn("Invalid as: must be a string, will use the first element of the array specified."),a=a[0]),!Ye.isString(a))throw new TypeError("Invalid as: must be a string!");var o=r.rows,s=[],l=c0.default(o,i);Ye.forIn(l,function(c){var u=c.length,v=c0.default(c,[t]);Ye.forIn(v,function(f){var h=f.length,g=f[0],p=g[t];g[e]=h,g[t]=p,g[a]=h/u,s.push(g)})}),r.rows=s}J3.DataSet.registerTransform("proportion",Z3);var Pt=or,Lh=ir;function Ih(r,n){var e=n.map||{},t={};Pt.isPlainObject(e)&&Pt.forIn(e,function(i,a){Pt.isString(i)&&Pt.isString(a)&&(t[a]=i)}),r.rows.forEach(function(i){Pt.forIn(t,function(a,o){var s=i[o];delete i[o],i[a]=s})})}Lh.DataSet.registerTransform("rename",Ih);Lh.DataSet.registerTransform("rename-fields",Ih);var r4=ir;r4.DataSet.registerTransform("reverse",function(r){r.rows.reverse()});var n4=ir;n4.DataSet.registerTransform("sort",function(r,n){var e=r.getColumnName(0);r.rows.sort(n.callback||function(t,i){return t[e]-i[e]})});var v0=or,Nh=ir,e4=Mr,h0=["ASC","DESC"];function Ah(r,n){var e=e4.getFields(n,[r.getColumnName(0)]);if(!v0.isArray(e))throw new TypeError("Invalid fields: must be an array with strings!");r.rows=v0.sortBy(r.rows,e);var t=n.order;if(t&&h0.indexOf(t)===-1)throw new TypeError("Invalid order: "+t+" must be one of "+h0.join(", "));t==="DESC"&&r.rows.reverse()}Nh.DataSet.registerTransform("sort-by",Ah);Nh.DataSet.registerTransform("sortBy",Ah);var t4=ir,i4=Mr;t4.DataSet.registerTransform("subset",function(r,n){var e=n.startRowIndex||0,t=n.endRowIndex||r.rows.length-1,i=i4.getFields(n,r.getColumnNames());r.rows=r.getSubset(e,t,i)});var a4=pr,wu=or,o4=a4.__importDefault(Tn),Dh=ir,u4={fillBy:"group",groupBy:[],orderBy:[]};function g0(r,n){var e=r.map(function(t){return t});return n.forEach(function(t){var i=e.indexOf(t);i>-1&&e.splice(i,1)}),e}function Oh(r,n){n=wu.assign({},u4,n);var e=r.rows,t=n.groupBy,i=n.orderBy,a=o4.default(e,t,i),o=0,s=[];wu.forIn(a,function(g){g.length>o&&(o=g.length,s=g)});var l=[],c={};if(s.forEach(function(g){var p=i.map(function(d){return g[d]}).join("-");l.push(p),c[p]=g}),n.fillBy==="order"){var u=s[0],v=[],f={};e.forEach(function(g){var p=i.map(function(d){return g[d]}).join("-");v.indexOf(p)===-1&&(v.push(p),f[p]=g)});var h=g0(v,l);h.forEach(function(g){var p={};t.forEach(function(d){p[d]=u[d]}),i.forEach(function(d){p[d]=f[g][d]}),e.push(p),s.push(p),l.push(g),c[g]=p}),o=s.length}wu.forIn(a,function(g){if(g!==s&&g.length<o){var p=g[0],d=[];g.forEach(function(y){d.push(i.map(function(E){return y[E]}).join("-"))});var m=g0(l,d);m.some(function(y,E){if(E>=o-g.length)return!0;var S=c[y],$={};return t.forEach(function(x){$[x]=p[x]}),i.forEach(function(x){$[x]=S[x]}),e.push($),!1})}})}Dh.DataSet.registerTransform("fill-rows",Oh);Dh.DataSet.registerTransform("fillRows",Oh);var qh=pr,Ce=or,f4=qh.__importStar(Dn.exports),l4=qh.__importDefault(Tn),s4=ir,c4=Mr,v4={groupBy:[]};function p0(r){return r.filter(function(n){return!Ce.isUndefined(n)})}var Fh=["mean","median","max","min"],cs={};Fh.forEach(function(r){cs[r]=function(n,e){return f4[r](e)}});cs.value=function(r,n,e){return e};function h4(r,n){n=Ce.assign({},v4,n);var e=c4.getField(n),t=n.method;if(!t)throw new TypeError("Invalid method!");if(t==="value"&&!Ce.has(n,"value"))throw new TypeError("Invalid value: it is nil.");var i=p0(r.getColumn(e)),a=l4.default(r.rows,n.groupBy);Ce.forIn(a,function(o){var s=p0(o.map(function(l){return l[e]}));s.length===0&&(s=i),o.forEach(function(l){if(Ce.isUndefined(l[e]))if(Ce.isFunction(t))l[e]=t(l,s,n.value,o);else if(Ce.isString(t))l[e]=cs[t](l,s,n.value);else throw new TypeError("Invalid method: must be a function or one of "+Fh.join(", "))})})}s4.DataSet.registerTransform("impute",h4);var zh={};Object.defineProperty(zh,"__esModule",{value:!0});var Bh=pr,In=or,g4=Bh.__importStar(Dn.exports),p4=Bh.__importDefault(Tn),Gh=ir,Uh=Gh.DataSet.registerTransform,d4=Mr,m4={as:[],fields:[],groupBy:[],operations:[]},d0="count",fi={count:function(r){return r.length},distinct:function(r,n){var e=In.uniq(r.map(function(t){return t[n]}));return e.length}};Gh.DataSet.CONSTANTS.STATISTICS_METHODS.forEach(function(r){fi[r]=function(n,e){var t=n.map(function(i){return i[e]});return In.isArray(t)&&In.isArray(t[0])&&(t=In.flattenDeep(t)),g4[r](t)}});fi.average=fi.mean;function Hh(r,n){n=In.assign({},m4,n);var e=d4.getFields(n);if(!In.isArray(e))throw new TypeError("Invalid fields: it must be an array with one or more strings!");var t=n.as||[];In.isString(t)&&(t=[t]);var i=n.operations;In.isString(i)&&(i=[i]);var a=[d0];if((!In.isArray(i)||!i.length)&&(console.warn('operations is not defined, will use [ "count" ] directly.'),i=a,t=i),!(i.length===1&&i[0]===d0)){if(i.length!==e.length)throw new TypeError("Invalid operations: it's length must be the same as fields!");if(t.length!==e.length)throw new TypeError("Invalid as: it's length must be the same as fields!")}var o=p4.default(r.rows,n.groupBy),s=[];In.forIn(o,function(l){var c=l[0];i.forEach(function(u,v){var f=t[v],h=e[v];c[f]=fi[u](l,h)}),s.push(c)}),r.rows=s}Uh("aggregate",Hh);Uh("summary",Hh);zh.default={VALID_AGGREGATES:In.keys(fi)};var Yh={},Vh={exports:{}};(function(r,n){(function(e,t){t(r)})(ao,function(e){function t(f,h,g){return h in f?Object.defineProperty(f,h,{value:g,enumerable:!0,configurable:!0,writable:!0}):f[h]=g,f}var i=Object.assign||function(f){for(var h=1;h<arguments.length;h++){var g=arguments[h];for(var p in g)Object.prototype.hasOwnProperty.call(g,p)&&(f[p]=g[p])}return f};function a(f){if(Array.isArray(f)){for(var h=0,g=Array(f.length);h<f.length;h++)g[h]=f[h];return g}else return Array.from(f)}var o={order:2,precision:2,period:null};function s(f,h){var g=[],p=[];f.forEach(function(S,$){S[1]!==null&&(p.push(S),g.push(h[$]))});var d=p.reduce(function(S,$){return S+$[1]},0),m=d/p.length,y=p.reduce(function(S,$){var x=$[1]-m;return S+x*x},0),E=p.reduce(function(S,$,x){var I=g[x],C=$[1]-I[1];return S+C*C},0);return 1-E/y}function l(f,h){for(var g=f,p=f.length-1,d=[h],m=0;m<p;m++){for(var y=m,E=m+1;E<p;E++)Math.abs(g[m][E])>Math.abs(g[m][y])&&(y=E);for(var S=m;S<p+1;S++){var $=g[S][m];g[S][m]=g[S][y],g[S][y]=$}for(var x=m+1;x<p;x++)for(var I=p;I>=m;I--)g[I][x]-=g[I][m]*g[m][x]/g[m][m]}for(var C=p-1;C>=0;C--){for(var L=0,O=C+1;O<p;O++)L+=g[O][C]*d[O];d[C]=(g[p][C]-L)/g[C][C]}return d}function c(f,h){var g=Math.pow(10,h);return Math.round(f*g)/g}var u={linear:function(h,g){for(var p=[0,0,0,0,0],d=0,m=0;m<h.length;m++)h[m][1]!==null&&(d++,p[0]+=h[m][0],p[1]+=h[m][1],p[2]+=h[m][0]*h[m][0],p[3]+=h[m][0]*h[m][1],p[4]+=h[m][1]*h[m][1]);var y=d*p[2]-p[0]*p[0],E=d*p[3]-p[0]*p[1],S=y===0?0:c(E/y,g.precision),$=c(p[1]/d-S*p[0]/d,g.precision),x=function(L){return[c(L,g.precision),c(S*L+$,g.precision)]},I=h.map(function(C){return x(C[0])});return{points:I,predict:x,equation:[S,$],r2:c(s(h,I),g.precision),string:$===0?"y = "+S+"x":"y = "+S+"x + "+$}},exponential:function(h,g){for(var p=[0,0,0,0,0,0],d=0;d<h.length;d++)h[d][1]!==null&&(p[0]+=h[d][0],p[1]+=h[d][1],p[2]+=h[d][0]*h[d][0]*h[d][1],p[3]+=h[d][1]*Math.log(h[d][1]),p[4]+=h[d][0]*h[d][1]*Math.log(h[d][1]),p[5]+=h[d][0]*h[d][1]);var m=p[1]*p[2]-p[5]*p[5],y=Math.exp((p[2]*p[3]-p[5]*p[4])/m),E=(p[1]*p[4]-p[5]*p[3])/m,S=c(y,g.precision),$=c(E,g.precision),x=function(L){return[c(L,g.precision),c(S*Math.exp($*L),g.precision)]},I=h.map(function(C){return x(C[0])});return{points:I,predict:x,equation:[S,$],string:"y = "+S+"e^("+$+"x)",r2:c(s(h,I),g.precision)}},logarithmic:function(h,g){for(var p=[0,0,0,0],d=h.length,m=0;m<d;m++)h[m][1]!==null&&(p[0]+=Math.log(h[m][0]),p[1]+=h[m][1]*Math.log(h[m][0]),p[2]+=h[m][1],p[3]+=Math.pow(Math.log(h[m][0]),2));var y=(d*p[1]-p[2]*p[0])/(d*p[3]-p[0]*p[0]),E=c(y,g.precision),S=c((p[2]-E*p[0])/d,g.precision),$=function(C){return[c(C,g.precision),c(c(S+E*Math.log(C),g.precision),g.precision)]},x=h.map(function(I){return $(I[0])});return{points:x,predict:$,equation:[S,E],string:"y = "+S+" + "+E+" ln(x)",r2:c(s(h,x),g.precision)}},power:function(h,g){for(var p=[0,0,0,0,0],d=h.length,m=0;m<d;m++)h[m][1]!==null&&(p[0]+=Math.log(h[m][0]),p[1]+=Math.log(h[m][1])*Math.log(h[m][0]),p[2]+=Math.log(h[m][1]),p[3]+=Math.pow(Math.log(h[m][0]),2));var y=(d*p[1]-p[0]*p[2])/(d*p[3]-Math.pow(p[0],2)),E=(p[2]-y*p[0])/d,S=c(Math.exp(E),g.precision),$=c(y,g.precision),x=function(L){return[c(L,g.precision),c(c(S*Math.pow(L,$),g.precision),g.precision)]},I=h.map(function(C){return x(C[0])});return{points:I,predict:x,equation:[S,$],string:"y = "+S+"x^"+$,r2:c(s(h,I),g.precision)}},polynomial:function(h,g){for(var p=[],d=[],m=0,y=0,E=h.length,S=g.order+1,$=0;$<S;$++){for(var x=0;x<E;x++)h[x][1]!==null&&(m+=Math.pow(h[x][0],$)*h[x][1]);p.push(m),m=0;for(var I=[],C=0;C<S;C++){for(var L=0;L<E;L++)h[L][1]!==null&&(y+=Math.pow(h[L][0],$+C));I.push(y),y=0}d.push(I)}d.push(p);for(var O=l(d,S).map(function(w){return c(w,g.precision)}),A=function(b){return[c(b,g.precision),c(O.reduce(function(P,D,rr){return P+D*Math.pow(b,rr)},0),g.precision)]},k=h.map(function(w){return A(w[0])}),U="y = ",_=O.length-1;_>=0;_--)_>1?U+=O[_]+"x^"+_+" + ":_===1?U+=O[_]+"x + ":U+=O[_];return{string:U,points:k,predict:A,equation:[].concat(a(O)).reverse(),r2:c(s(h,k),g.precision)}}};function v(){var f=function(g,p){return i({_round:c},g,t({},p,function(d,m){return u[p](d,i({},o,m))}))};return Object.keys(u).reduce(f,{})}e.exports=v()})})(Vh);var Et={};Object.defineProperty(Et,"__esModule",{value:!0});var y4=pr;Et.default=function(r,n){for(var e=n||1,t=y4.__read(r,2),i=t[0],a=t[1],o=[],s=i;s<a;)o.push(s),s+=e;return o.push(a),o};var ie={};Object.defineProperty(ie,"__esModule",{value:!0});ie.nrd=ie.silverman=void 0;var _f=Dn.exports;function w4(r){var n=_f.standardDeviation(r),e=4*Math.pow(n,5),t=3*r.length;return Math.pow(e/t,.2)}ie.silverman=w4;function E4(r){var n=_f.standardDeviation(r),e=_f.interquartileRange(r);return typeof e=="number"&&(n=Math.min(n,e/1.34)),1.06*n*Math.pow(r.length,-.2)}ie.nrd=E4;Object.defineProperty(Yh,"__esModule",{value:!0});var Qt=pr,S4=Qt.__importDefault(Vh.exports),Gi=or,_4=Qt.__importDefault(Et),T4=ir,$4=Mr,M4=ie,x4={as:["x","y"],method:"linear",order:2,precision:2},Tf=["linear","exponential","logarithmic","power","polynomial"];function b4(r,n){n=Gi.assign({},x4,n);var e=$4.getFields(n);if(!Gi.isArray(e)||e.length!==2)throw new TypeError("invalid fields: must be an array of 2 strings.");var t=Qt.__read(e,2),i=t[0],a=t[1],o=n.method;if(Tf.indexOf(o)===-1)throw new TypeError("invalid method: "+o+". Must be one of "+Tf.join(", "));var s=r.rows.map(function(d){return[d[i],d[a]]}),l=S4.default[o](s,n),c=n.extent;(!Gi.isArray(c)||c.length!==2)&&(c=r.range(i));var u=n.bandwidth;(!Gi.isNumber(u)||u<=0)&&(u=M4.silverman(r.getColumn(i)));var v=_4.default(c,u),f=[],h=Qt.__read(n.as,2),g=h[0],p=h[1];v.forEach(function(d){var m={},y=Qt.__read(l.predict(d),2),E=y[0],S=y[1];m[g]=E,m[p]=S,isFinite(S)&&f.push(m)}),r.rows=f}T4.DataSet.registerTransform("regression",b4);Yh.default={REGRESSION_METHODS:Tf};var Xh={},yi={};Object.defineProperty(yi,"__esModule",{value:!0});function m0(r){return Math.abs(r)<=1?.5:0}function P4(r){var n=1-Math.pow(Math.abs(r),3);return Math.pow(n,3)}yi.default={boxcar:m0,cosine:function(r){return Math.abs(r)<=1?Math.PI/4*Math.cos(Math.PI/2*r):0},epanechnikov:function(r){return Math.abs(r)<1?.75*(1-r*r):0},gaussian:function(r){return .3989422804*Math.exp(-.5*r*r)},quartic:function(r){if(Math.abs(r)<1){var n=1-r*r;return 15/16*n*n}return 0},triangular:function(r){var n=Math.abs(r);return n<1?1-n:0},tricube:function(r){return Math.abs(r)<1?70/81*P4(r):0},triweight:function(r){if(Math.abs(r)<1){var n=1-r*r;return 35/32*n*n*n}return 0},uniform:m0};Object.defineProperty(Xh,"__esModule",{value:!0});var De=pr,Kr=or,R4=De.__importDefault(Et),jh=De.__importDefault(yi),ua=De.__importStar(ie),k4=De.__importDefault(Tn),vs=ir,C4=Mr,L4=Dn.exports,I4={minSize:.01,as:["key","y","size"],extent:[],method:"gaussian",bandwidth:"nrd",step:0,groupBy:[]},$f=Kr.keys(jh.default),N4=Kr.keys(ua);function hs(r,n){n=Kr.assign({},I4,n);var e=C4.getFields(n);if(!Kr.isArray(e)||e.length<1)throw new TypeError("invalid fields: must be an array of at least 1 strings!");var t=n.as;if(!Kr.isArray(t)||t.length!==3)throw new TypeError("invalid as: must be an array of 3 strings!");var i=n.method;if(Kr.isString(i)){if($f.indexOf(i)===-1)throw new TypeError("invalid method: "+i+". Must be one of "+$f.join(", "));i=jh.default[i]}if(!Kr.isFunction(i))throw new TypeError("invalid method: kernel method must be a function!");var a=n.extent;if(!Kr.isArray(a)||a.length===0){var o=[];Kr.each(e,function(f){var h=r.range(f);o=o.concat(h)}),a=[Math.min.apply(Math,De.__spread(o)),Math.max.apply(Math,De.__spread(o))]}var s=n.bandwidth;Kr.isString(s)&&ua[s]?s=ua[s](r.getColumn(e[0])):Kr.isFunction(s)?s=s(r.getColumn(e[0])):(!Kr.isNumber(s)||s<=0)&&(s=ua.nrd(r.getColumn(e[0])));var l=R4.default(a,n.step?n.step:s),c=[],u=n.groupBy,v=k4.default(r.rows,u);Kr.forIn(v,function(f){var h={};Kr.each(e,function(g){var p=Kr.pick(f[0],u);h[g]=L4.kernelDensityEstimation(f.map(function(S){return S[g]}),i,s);var d=De.__read(t,3),m=d[0],y=d[1],E=d[2];p[m]=g,p[y]=[],p[E]=[],Kr.each(l,function(S){var $=h[g](S);$>=n.minSize&&(p[y].push(S),p[E].push($))}),c.push(p)})}),r.rows=c}vs.DataSet.registerTransform("kernel-density-estimation",hs);vs.DataSet.registerTransform("kde",hs);vs.DataSet.registerTransform("KDE",hs);Xh.default={KERNEL_METHODS:$f,BANDWIDTH_METHODS:N4};var Ln=pr,Ui=or,gs=ir,A4=Mr,D4={as:["x","y","count"],bins:[30,30],offset:[0,0],sizeByCount:!1},y0=Math.sqrt(3),Rt=Math.PI/3,O4=[0,Rt,2*Rt,3*Rt,4*Rt,5*Rt];function w0(r,n,e,t){return Math.sqrt((r-e)*(r-e)+(n-t)*(n-t))}function E0(r,n,e){var t=r-e;n=n/2;var i=Math.floor(t/n),a=n*(i+(Math.abs(i%2)===1?1:0)),o=n*(i+(Math.abs(i%2)===1?0:1));return[a+e,o+e]}function q4(r,n,e){n===void 0&&(n=[1,1]),e===void 0&&(e=[0,0]);var t={},i=Ln.__read(n,2),a=i[0],o=i[1],s=Ln.__read(e,2),l=s[0],c=s[1];return r.forEach(function(u){var v,f,h=Ln.__read(u,2),g=h[0],p=h[1],d=Ln.__read(E0(g,a,l),2),m=d[0],y=d[1],E=Ln.__read(E0(p,o,c),2),S=E[0],$=E[1],x=w0(g,p,m,S),I=w0(g,p,y,$),C,L,O;x<I?(C="x"+m+"y"+S,v=Ln.__read([m,S],2),L=v[0],O=v[1]):(C="x"+y+"y"+$,f=Ln.__read([y,$],2),L=f[0],O=f[1]),t[C]=t[C]||{x:L,y:O,count:0},t[C].count++}),t}function ps(r,n){n=Ui.assign({},D4,n);var e=A4.getFields(n);if(!Ui.isArray(e)||e.length!==2)throw new TypeError("Invalid fields: it must be an array with 2 strings!");var t=Ln.__read(e,2),i=t[0],a=t[1],o=r.range(i),s=r.range(a),l=o[1]-o[0],c=s[1]-s[0],u=n.binWidth||[];if(u.length!==2){var v=Ln.__read(n.bins,2),f=v[0],h=v[1];if(f<=0||h<=0)throw new TypeError("Invalid bins: must be an array with two positive numbers (e.g. [ 30, 30 ])!");u=[l/f,c/h]}var g=Ln.__read(n.offset,2),p=g[0],d=g[1],m=3*u[0]/(y0*u[1]),y=r.rows.map(function(k){return[k[i],m*k[a]]}),E=q4(y,[u[0],m*u[1]],[p,m*d]),S=Ln.__read(n.as,3),$=S[0],x=S[1],I=S[2];if(!$||!x||!I)throw new TypeError('Invalid as: it must be an array with three elements (e.g. [ "x", "y", "count" ])!');var C=u[0]/y0,L=O4.map(function(k){return[Math.sin(k)*C,-Math.cos(k)*C]}),O=[],A=0;n.sizeByCount&&Ui.forIn(E,function(k){k.count>A&&(A=k.count)}),Ui.forIn(E,function(k){var U=k.x,_=k.y,w=k.count,b={};b[I]=w,n.sizeByCount?(b[$]=L.map(function(P){return U+k.count/A*P[0]}),b[x]=L.map(function(P){return(_+k.count/A*P[1])/m})):(b[$]=L.map(function(P){return U+P[0]}),b[x]=L.map(function(P){return(_+P[1])/m})),O.push(b)}),r.rows=O}gs.DataSet.registerTransform("bin.hexagon",ps);gs.DataSet.registerTransform("bin.hex",ps);gs.DataSet.registerTransform("hexbin",ps);var Mf=pr,kt=or,F4=Mf.__importDefault(Tn),Wh=ir,z4=Mr,B4={as:["x","count"],bins:void 0,offset:0,groupBy:[]};function G4(r,n,e){var t=r-e,i=Math.floor(t/n);return[i*n+e,(i+1)*n+e]}function U4(r){return Math.ceil(Math.log(r)/Math.LN2)+1}function Jh(r,n){n=kt.assign({},B4,n);var e=z4.getField(n);if(r.rows.length!==0){var t=r.range(e),i=t[1]-t[0],a=n.binWidth,o=n.bins;if(!a&&o){if(o<=0)throw new TypeError("Invalid bins: it must be a positive number!");a=i/o}if(!a&&!o){var s=U4(r.rows.length);a=i/s}var l=n.offset%a,c=[],u=n.groupBy,v=F4.default(r.rows,u);kt.forIn(v,function(f){var h={},g=f.map(function(E){return E[e]});g.forEach(function(E){var S=Mf.__read(G4(E,a,l),2),$=S[0],x=S[1],I=$+"-"+x;h[I]=h[I]||{x0:$,x1:x,count:0},h[I].count++});var p=Mf.__read(n.as,2),d=p[0],m=p[1];if(!d||!m)throw new TypeError('Invalid as: it must be an array with 2 elements (e.g. [ "x", "count" ])!');var y=kt.pick(f[0],u);kt.forIn(h,function(E){var S=kt.assign({},y);S[d]=[E.x0,E.x1],S[m]=E.count,c.push(S)})}),r.rows=c}}Wh.DataSet.registerTransform("bin.histogram",Jh);Wh.DataSet.registerTransform("bin.dot",Jh);var Kh=pr,Hi=or,H4=Dn.exports,Y4=Kh.__importDefault(Tn),V4=Kh.__importDefault(_o),X4=ir,j4=X4.DataSet.registerTransform,W4=Mr,J4={as:"_bin",groupBy:[],fraction:4};function K4(r,n){n=Hi.assign({},J4,n);var e=W4.getField(n),t=n.as;if(!Hi.isString(t))throw new TypeError('Invalid as: it must be a string (e.g. "_bin")!');var i=n.p,a=n.fraction;(!Hi.isArray(i)||i.length===0)&&(i=V4.default(a));var o=r.rows,s=n.groupBy,l=Y4.default(o,s),c=[];Hi.forIn(l,function(u){var v=u[0],f=u.map(function(g){return g[e]}),h=i.map(function(g){return H4.quantile(f,g)});v[t]=h,c.push(v)}),r.rows=c}j4("bin.quantile",K4);var xe=pr,Yi=or,Q4=ir,Qh=Q4.DataSet.registerTransform,Z4=Mr,r9={as:["x","y","count"],bins:[30,30],offset:[0,0],sizeByCount:!1};function S0(r,n,e){var t=r-e,i=Math.floor(t/n);return[i*n+e,(i+1)*n+e]}function Zh(r,n){n=Yi.assign({},r9,n);var e=xe.__read(Z4.getFields(n),2),t=e[0],i=e[1];if(!t||!i)throw new TypeError("Invalid fields: must be an array with 2 strings!");var a=r.range(t),o=r.range(i),s=a[1]-a[0],l=o[1]-o[0],c=n.binWidth||[];if(c.length!==2){var u=xe.__read(n.bins,2),v=u[0],f=u[1];if(v<=0||f<=0)throw new TypeError("Invalid bins: must be an array with 2 positive numbers (e.g. [ 30, 30 ])!");c=[s/v,l/f]}var h=r.rows.map(function(C){return[C[t],C[i]]}),g={},p=xe.__read(n.offset,2),d=p[0],m=p[1];h.forEach(function(C){var L=xe.__read(S0(C[0],c[0],d),2),O=L[0],A=L[1],k=xe.__read(S0(C[1],c[1],m),2),U=k[0],_=k[1],w=O+"-"+A+"-"+U+"-"+_;g[w]=g[w]||{x0:O,x1:A,y0:U,y1:_,count:0},g[w].count++});var y=[],E=xe.__read(n.as,3),S=E[0],$=E[1],x=E[2];if(!S||!$||!x)throw new TypeError('Invalid as: it must be an array with 3 strings (e.g. [ "x", "y", "count" ])!');if(!n.sizeByCount)Yi.forIn(g,function(C){var L={};L[S]=[C.x0,C.x1,C.x1,C.x0],L[$]=[C.y0,C.y0,C.y1,C.y1],L[x]=C.count,y.push(L)});else{var I=0;Yi.forIn(g,function(C){C.count>I&&(I=C.count)}),Yi.forIn(g,function(C){var L=C.x0,O=C.x1,A=C.y0,k=C.y1,U=C.count,_=U/I,w=xe.__read([(L+O)/2,(A+k)/2],2),b=w[0],P=w[1],D=(O-L)*_/2,rr=(k-A)*_/2,hr=b-D,ur=b+D,gr=P-rr,xr=P+rr,Ur={};Ur[S]=[hr,ur,ur,hr],Ur[$]=[gr,gr,xr,xr],Ur[x]=U,y.push(Ur)})}r.rows=y}Qh("bin.rectangle",Zh);Qh("bin.rect",Zh);var Eu=or,n9=ir,e9=n9.DataSet.registerTransform,t9=Mr,i9={as:["_centroid_x","_centroid_y"]};function a9(r,n){n=Eu.assign({},i9,n);var e=t9.getField(n),t=n.geoView||n.geoDataView;if(Eu.isString(t)&&r.dataSet&&(t=r.dataSet.getView(t)),!t||t.dataType!=="geo")throw new TypeError("Invalid geoView: must be a DataView of GEO dataType!");var i=n.as;if(!Eu.isArray(i)||i.length!==2)throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ "cX", "cY" ])!');var a=i[0],o=i[1];r.rows.forEach(function(s){var l=t.geoFeatureByName(s[e]);l&&(t._projectedAs?(s[a]=l[t._projectedAs[2]],s[o]=l[t._projectedAs[3]]):(s[a]=l.centroidX,s[o]=l.centroidY))})}e9("geo.centroid",a9);var ds=pr,_0=or,o9=hi,u9=ds.__importDefault(fh),f9=ir,l9=f9.DataSet.registerTransform,s9=ds.__importDefault(Eo),c9={as:["_x","_y","_centroid_x","_centroid_y"]};function v9(r,n){if(r.dataType!=="geo"&&r.dataType!=="geo-graticule")throw new TypeError("Invalid dataView: this transform is for Geo data only!");n=_0.assign({},c9,n);var e=n.projection;if(!e)throw new TypeError("Invalid projection!");e=s9.default(e);var t=o9.geoPath(e),i=n.as;if(!_0.isArray(i)||i.length!==4)throw new TypeError('Invalid as: it must be an array with 4 strings (e.g. [ "x", "y", "cX", "cY" ])!');r._projectedAs=i;var a=ds.__read(i,4),o=a[0],s=a[1],l=a[2],c=a[3];r.rows.forEach(function(u){u[o]=[],u[s]=[];var v=t(u);if(v){var f=u9.default(v);f._path.forEach(function(g){u[o].push(g[1]),u[s].push(g[2])});var h=t.centroid(u);u[l]=h[0],u[c]=h[1]}}),r.rows=r.rows.filter(function(u){return u[o].length!==0})}l9("geo.projection",v9);var Su=or,h9=ir,g9=h9.DataSet.registerTransform,p9=Mr,d9={as:["_x","_y"]};function m9(r,n){n=Su.assign({},d9,n);var e=p9.getField(n),t=n.geoView||n.geoDataView;if(Su.isString(t)&&(t=r.dataSet.getView(t)),!t||t.dataType!=="geo")throw new TypeError("Invalid geoView: must be a DataView of GEO dataType!");var i=n.as;if(!Su.isArray(i)||i.length!==2)throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ "x", "y" ])!');var a=i[0],o=i[1];r.rows.forEach(function(s){var l=t.geoFeatureByName(s[e]);l&&(t._projectedAs?(s[a]=l[t._projectedAs[0]],s[o]=l[t._projectedAs[1]]):(s[a]=l.longitude,s[o]=l.latitude))})}g9("geo.region",m9);var ht=or,rg=ir,y9={y:0,thickness:.05,weight:!1,marginRatio:.1,id:function(r){return r.id},source:function(r){return r.source},target:function(r){return r.target},sourceWeight:function(r){return r.value||1},targetWeight:function(r){return r.value||1},sortBy:null};function w9(r,n,e){return e===void 0&&(e={}),r.forEach(function(t){var i=n.edgeSource(t),a=n.edgeTarget(t);e[i]||(e[i]={id:i}),e[a]||(e[a]={id:a})}),ht.values(e)}function E9(r,n,e){ht.forIn(r,function(t,i){t.inEdges=n.filter(function(a){return""+e.target(a)==""+i}),t.outEdges=n.filter(function(a){return""+e.source(a)==""+i}),t.edges=t.outEdges.concat(t.inEdges),t.frequency=t.edges.length,t.value=0,t.inEdges.forEach(function(a){t.value+=e.targetWeight(a)}),t.outEdges.forEach(function(a){t.value+=e.sourceWeight(a)})})}function S9(r,n){var e={weight:function(i,a){return a.value-i.value},frequency:function(i,a){return a.frequency-i.frequency},id:function(i,a){return(""+n.id(i)).localeCompare(""+n.id(a))}},t=e[n.sortBy];!t&&ht.isFunction(n.sortBy)&&(t=n.sortBy),t&&r.sort(t)}function _9(r,n){var e=r.length;if(!e)throw new TypeError("Invalid nodes: it's empty!");if(n.weight){var t=n.marginRatio;if(t<0||t>=1)throw new TypeError("Invalid marginRatio: it must be in range [0, 1)!");var i=t/(2*e),a=n.thickness;if(a<=0||a>=1)throw new TypeError("Invalid thickness: it must be in range (0, 1)!");var o=0;r.forEach(function(l){o+=l.value}),r.forEach(function(l){l.weight=l.value/o,l.width=l.weight*(1-t),l.height=a}),r.forEach(function(l,c){for(var u=0,v=c-1;v>=0;v--)u+=r[v].width+2*i;var f=l.minX=i+u,h=l.maxX=l.minX+l.width,g=l.minY=n.y-a/2,p=l.maxY=g+a;l.x=[f,h,h,f],l.y=[g,g,p,p]})}else{var s=1/e;r.forEach(function(l,c){l.x=(c+.5)*s,l.y=n.y})}}function T9(r,n,e){if(e.weight){var t={};ht.forIn(r,function(i,a){t[a]=i.value}),n.forEach(function(i){var a=e.source(i),o=e.target(i),s=r[a],l=r[o];if(s&&l){var c=t[a],u=e.sourceWeight(i),v=s.minX+(s.value-c)/s.value*s.width,f=v+u/s.value*s.width;t[a]-=u;var h=t[o],g=e.targetWeight(i),p=l.minX+(l.value-h)/l.value*l.width,d=p+g/l.value*l.width;t[o]-=g;var m=e.y;i.x=[v,f,p,d],i.y=[m,m,m,m]}})}else n.forEach(function(i){var a=r[e.source(i)],o=r[e.target(i)];a&&o&&(i.x=[a.x,o.x],i.y=[a.y,o.y])})}function ng(r,n){n=ht.assign({},y9,n);var e={},t=r.nodes,i=r.edges;(!ht.isArray(t)||t.length===0)&&(t=w9(i,n,e)),t.forEach(function(a){var o=n.id(a);e[o]=a}),E9(e,i,n),S9(t,n),_9(t,n),T9(e,i,n),r.nodes=t,r.edges=i}rg.DataSet.registerTransform("diagram.arc",ng);rg.DataSet.registerTransform("arc",ng);var Ka;if(typeof o1=="function")try{Ka=require("graphlib")}catch{}Ka||(Ka=window.graphlib);var On=Ka,Qa;if(typeof o1=="function")try{Qa={cloneDeep:require("lodash/cloneDeep"),constant:require("lodash/constant"),defaults:require("lodash/defaults"),each:require("lodash/each"),filter:require("lodash/filter"),find:require("lodash/find"),flatten:require("lodash/flatten"),forEach:require("lodash/forEach"),forIn:require("lodash/forIn"),has:require("lodash/has"),isUndefined:require("lodash/isUndefined"),last:require("lodash/last"),map:require("lodash/map"),mapValues:require("lodash/mapValues"),max:require("lodash/max"),merge:require("lodash/merge"),min:require("lodash/min"),minBy:require("lodash/minBy"),now:require("lodash/now"),pick:require("lodash/pick"),range:require("lodash/range"),reduce:require("lodash/reduce"),sortBy:require("lodash/sortBy"),uniqueId:require("lodash/uniqueId"),values:require("lodash/values"),zipObject:require("lodash/zipObject")}}catch{}Qa||(Qa=window._);var Dr=Qa,$9=Po;function Po(){var r={};r._next=r._prev=r,this._sentinel=r}Po.prototype.dequeue=function(){var r=this._sentinel,n=r._prev;if(n!==r)return eg(n),n};Po.prototype.enqueue=function(r){var n=this._sentinel;r._prev&&r._next&&eg(r),r._next=n._next,n._next._prev=r,n._next=r,r._prev=n};Po.prototype.toString=function(){for(var r=[],n=this._sentinel,e=n._prev;e!==n;)r.push(JSON.stringify(e,M9)),e=e._prev;return"["+r.join(", ")+"]"};function eg(r){r._prev._next=r._next,r._next._prev=r._prev,delete r._next,delete r._prev}function M9(r,n){if(r!=="_next"&&r!=="_prev")return n}var Zn=Dr,x9=On.Graph,b9=$9,P9=k9,R9=Zn.constant(1);function k9(r,n){if(r.nodeCount()<=1)return[];var e=L9(r,n||R9),t=C9(e.graph,e.buckets,e.zeroIdx);return Zn.flatten(Zn.map(t,function(i){return r.outEdges(i.v,i.w)}),!0)}function C9(r,n,e){for(var t=[],i=n[n.length-1],a=n[0],o;r.nodeCount();){for(;o=a.dequeue();)_u(r,n,e,o);for(;o=i.dequeue();)_u(r,n,e,o);if(r.nodeCount()){for(var s=n.length-2;s>0;--s)if(o=n[s].dequeue(),o){t=t.concat(_u(r,n,e,o,!0));break}}}return t}function _u(r,n,e,t,i){var a=i?[]:void 0;return Zn.forEach(r.inEdges(t.v),function(o){var s=r.edge(o),l=r.node(o.v);i&&a.push({v:o.v,w:o.w}),l.out-=s,xf(n,e,l)}),Zn.forEach(r.outEdges(t.v),function(o){var s=r.edge(o),l=o.w,c=r.node(l);c.in-=s,xf(n,e,c)}),r.removeNode(t.v),a}function L9(r,n){var e=new x9,t=0,i=0;Zn.forEach(r.nodes(),function(s){e.setNode(s,{v:s,in:0,out:0})}),Zn.forEach(r.edges(),function(s){var l=e.edge(s.v,s.w)||0,c=n(s),u=l+c;e.setEdge(s.v,s.w,u),i=Math.max(i,e.node(s.v).out+=c),t=Math.max(t,e.node(s.w).in+=c)});var a=Zn.range(i+t+3).map(function(){return new b9}),o=t+1;return Zn.forEach(e.nodes(),function(s){xf(a,o,e.node(s))}),{graph:e,buckets:a,zeroIdx:o}}function xf(r,n,e){e.out?e.in?r[e.out-e.in+n].enqueue(e):r[r.length-1].enqueue(e):r[0].enqueue(e)}var Ne=Dr,I9=P9,N9={run:A9,undo:O9};function A9(r){var n=r.graph().acyclicer==="greedy"?I9(r,e(r)):D9(r);Ne.forEach(n,function(t){var i=r.edge(t);r.removeEdge(t),i.forwardName=t.name,i.reversed=!0,r.setEdge(t.w,t.v,i,Ne.uniqueId("rev"))});function e(t){return function(i){return t.edge(i).weight}}}function D9(r){var n=[],e={},t={};function i(a){Ne.has(t,a)||(t[a]=!0,e[a]=!0,Ne.forEach(r.outEdges(a),function(o){Ne.has(e,o.w)?n.push(o):i(o.w)}),delete e[a])}return Ne.forEach(r.nodes(),i),n}function O9(r){Ne.forEach(r.edges(),function(n){var e=r.edge(n);if(e.reversed){r.removeEdge(n);var t=e.forwardName;delete e.reversed,delete e.forwardName,r.setEdge(n.w,n.v,e,t)}})}var $r=Dr,tg=On.Graph,hn={addDummyNode:ig,simplify:q9,asNonCompoundGraph:F9,successorWeights:z9,predecessorWeights:B9,intersectRect:G9,buildLayerMatrix:U9,normalizeRanks:H9,removeEmptyRanks:Y9,addBorderNode:V9,maxRank:ag,partition:X9,time:j9,notime:W9};function ig(r,n,e,t){var i;do i=$r.uniqueId(t);while(r.hasNode(i));return e.dummy=n,r.setNode(i,e),i}function q9(r){var n=new tg().setGraph(r.graph());return $r.forEach(r.nodes(),function(e){n.setNode(e,r.node(e))}),$r.forEach(r.edges(),function(e){var t=n.edge(e.v,e.w)||{weight:0,minlen:1},i=r.edge(e);n.setEdge(e.v,e.w,{weight:t.weight+i.weight,minlen:Math.max(t.minlen,i.minlen)})}),n}function F9(r){var n=new tg({multigraph:r.isMultigraph()}).setGraph(r.graph());return $r.forEach(r.nodes(),function(e){r.children(e).length||n.setNode(e,r.node(e))}),$r.forEach(r.edges(),function(e){n.setEdge(e,r.edge(e))}),n}function z9(r){var n=$r.map(r.nodes(),function(e){var t={};return $r.forEach(r.outEdges(e),function(i){t[i.w]=(t[i.w]||0)+r.edge(i).weight}),t});return $r.zipObject(r.nodes(),n)}function B9(r){var n=$r.map(r.nodes(),function(e){var t={};return $r.forEach(r.inEdges(e),function(i){t[i.v]=(t[i.v]||0)+r.edge(i).weight}),t});return $r.zipObject(r.nodes(),n)}function G9(r,n){var e=r.x,t=r.y,i=n.x-e,a=n.y-t,o=r.width/2,s=r.height/2;if(!i&&!a)throw new Error("Not possible to find intersection inside of the rectangle");var l,c;return Math.abs(a)*o>Math.abs(i)*s?(a<0&&(s=-s),l=s*i/a,c=s):(i<0&&(o=-o),l=o,c=o*a/i),{x:e+l,y:t+c}}function U9(r){var n=$r.map($r.range(ag(r)+1),function(){return[]});return $r.forEach(r.nodes(),function(e){var t=r.node(e),i=t.rank;$r.isUndefined(i)||(n[i][t.order]=e)}),n}function H9(r){var n=$r.min($r.map(r.nodes(),function(e){return r.node(e).rank}));$r.forEach(r.nodes(),function(e){var t=r.node(e);$r.has(t,"rank")&&(t.rank-=n)})}function Y9(r){var n=$r.min($r.map(r.nodes(),function(a){return r.node(a).rank})),e=[];$r.forEach(r.nodes(),function(a){var o=r.node(a).rank-n;e[o]||(e[o]=[]),e[o].push(a)});var t=0,i=r.graph().nodeRankFactor;$r.forEach(e,function(a,o){$r.isUndefined(a)&&o%i!==0?--t:t&&$r.forEach(a,function(s){r.node(s).rank+=t})})}function V9(r,n,e,t){var i={width:0,height:0};return arguments.length>=4&&(i.rank=e,i.order=t),ig(r,"border",i,n)}function ag(r){return $r.max($r.map(r.nodes(),function(n){var e=r.node(n).rank;if(!$r.isUndefined(e))return e}))}function X9(r,n){var e={lhs:[],rhs:[]};return $r.forEach(r,function(t){n(t)?e.lhs.push(t):e.rhs.push(t)}),e}function j9(r,n){var e=$r.now();try{return n()}finally{console.log(r+" time: "+($r.now()-e)+"ms")}}function W9(r,n){return n()}var og=Dr,J9=hn,K9={run:Q9,undo:rE};function Q9(r){r.graph().dummyChains=[],og.forEach(r.edges(),function(n){Z9(r,n)})}function Z9(r,n){var e=n.v,t=r.node(e).rank,i=n.w,a=r.node(i).rank,o=n.name,s=r.edge(n),l=s.labelRank;if(a!==t+1){r.removeEdge(n);var c,u,v;for(v=0,++t;t<a;++v,++t)s.points=[],u={width:0,height:0,edgeLabel:s,edgeObj:n,rank:t},c=J9.addDummyNode(r,"edge",u,"_d"),t===l&&(u.width=s.width,u.height=s.height,u.dummy="edge-label",u.labelpos=s.labelpos),r.setEdge(e,c,{weight:s.weight},o),v===0&&r.graph().dummyChains.push(c),e=c;r.setEdge(e,i,{weight:s.weight},o)}}function rE(r){og.forEach(r.graph().dummyChains,function(n){var e=r.node(n),t=e.edgeLabel,i;for(r.setEdge(e.edgeObj,t);e.dummy;)i=r.successors(n)[0],r.removeNode(n),t.points.push({x:e.x,y:e.y}),e.dummy==="edge-label"&&(t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height),n=i,e=r.node(n)})}var Vi=Dr,Ro={longestPath:nE,slack:eE};function nE(r){var n={};function e(t){var i=r.node(t);if(Vi.has(n,t))return i.rank;n[t]=!0;var a=Vi.min(Vi.map(r.outEdges(t),function(o){return e(o.w)-r.edge(o).minlen}));return(a===Number.POSITIVE_INFINITY||a===void 0||a===null)&&(a=0),i.rank=a}Vi.forEach(r.sources(),e)}function eE(r,n){return r.node(n.w).rank-r.node(n.v).rank-r.edge(n).minlen}var Za=Dr,tE=On.Graph,ro=Ro.slack,ug=iE;function iE(r){var n=new tE({directed:!1}),e=r.nodes()[0],t=r.nodeCount();n.setNode(e,{});for(var i,a;aE(n,r)<t;)i=oE(n,r),a=n.hasNode(i.v)?ro(r,i):-ro(r,i),uE(n,r,a);return n}function aE(r,n){function e(t){Za.forEach(n.nodeEdges(t),function(i){var a=i.v,o=t===a?i.w:a;!r.hasNode(o)&&!ro(n,i)&&(r.setNode(o,{}),r.setEdge(t,o,{}),e(o))})}return Za.forEach(r.nodes(),e),r.nodeCount()}function oE(r,n){return Za.minBy(n.edges(),function(e){if(r.hasNode(e.v)!==r.hasNode(e.w))return ro(n,e)})}function uE(r,n,e){Za.forEach(r.nodes(),function(t){n.node(t).rank+=e})}var ae=Dr,fE=ug,lE=Ro.slack,sE=Ro.longestPath,cE=On.alg.preorder,vE=On.alg.postorder,hE=hn.simplify,gE=Ge;Ge.initLowLimValues=ys;Ge.initCutValues=ms;Ge.calcCutValue=fg;Ge.leaveEdge=sg;Ge.enterEdge=cg;Ge.exchangeEdges=vg;function Ge(r){r=hE(r),sE(r);var n=fE(r);ys(n),ms(n,r);for(var e,t;e=sg(n);)t=cg(n,r,e),vg(n,r,e,t)}function ms(r,n){var e=vE(r,r.nodes());e=e.slice(0,e.length-1),ae.forEach(e,function(t){pE(r,n,t)})}function pE(r,n,e){var t=r.node(e),i=t.parent;r.edge(e,i).cutvalue=fg(r,n,e)}function fg(r,n,e){var t=r.node(e),i=t.parent,a=!0,o=n.edge(e,i),s=0;return o||(a=!1,o=n.edge(i,e)),s=o.weight,ae.forEach(n.nodeEdges(e),function(l){var c=l.v===e,u=c?l.w:l.v;if(u!==i){var v=c===a,f=n.edge(l).weight;if(s+=v?f:-f,mE(r,e,u)){var h=r.edge(e,u).cutvalue;s+=v?-h:h}}}),s}function ys(r,n){arguments.length<2&&(n=r.nodes()[0]),lg(r,{},1,n)}function lg(r,n,e,t,i){var a=e,o=r.node(t);return n[t]=!0,ae.forEach(r.neighbors(t),function(s){ae.has(n,s)||(e=lg(r,n,e,s,t))}),o.low=a,o.lim=e++,i?o.parent=i:delete o.parent,e}function sg(r){return ae.find(r.edges(),function(n){return r.edge(n).cutvalue<0})}function cg(r,n,e){var t=e.v,i=e.w;n.hasEdge(t,i)||(t=e.w,i=e.v);var a=r.node(t),o=r.node(i),s=a,l=!1;a.lim>o.lim&&(s=o,l=!0);var c=ae.filter(n.edges(),function(u){return l===T0(r,r.node(u.v),s)&&l!==T0(r,r.node(u.w),s)});return ae.minBy(c,function(u){return lE(n,u)})}function vg(r,n,e,t){var i=e.v,a=e.w;r.removeEdge(i,a),r.setEdge(t.v,t.w,{}),ys(r),ms(r,n),dE(r,n)}function dE(r,n){var e=ae.find(r.nodes(),function(i){return!n.node(i).parent}),t=cE(r,e);t=t.slice(1),ae.forEach(t,function(i){var a=r.node(i).parent,o=n.edge(i,a),s=!1;o||(o=n.edge(a,i),s=!0),n.node(i).rank=n.node(a).rank+(s?o.minlen:-o.minlen)})}function mE(r,n,e){return r.hasEdge(n,e)}function T0(r,n,e){return e.low<=n.lim&&n.lim<=e.lim}var yE=Ro,hg=yE.longestPath,wE=ug,EE=gE,SE=_E;function _E(r){switch(r.graph().ranker){case"network-simplex":$0(r);break;case"tight-tree":$E(r);break;case"longest-path":TE(r);break;default:$0(r)}}var TE=hg;function $E(r){hg(r),wE(r)}function $0(r){EE(r)}var bf=Dr,ME=xE;function xE(r){var n=PE(r);bf.forEach(r.graph().dummyChains,function(e){for(var t=r.node(e),i=t.edgeObj,a=bE(r,n,i.v,i.w),o=a.path,s=a.lca,l=0,c=o[l],u=!0;e!==i.w;){if(t=r.node(e),u){for(;(c=o[l])!==s&&r.node(c).maxRank<t.rank;)l++;c===s&&(u=!1)}if(!u){for(;l<o.length-1&&r.node(c=o[l+1]).minRank<=t.rank;)l++;c=o[l]}r.setParent(e,c),e=r.successors(e)[0]}})}function bE(r,n,e,t){var i=[],a=[],o=Math.min(n[e].low,n[t].low),s=Math.max(n[e].lim,n[t].lim),l,c;l=e;do l=r.parent(l),i.push(l);while(l&&(n[l].low>o||s>n[l].lim));for(c=l,l=t;(l=r.parent(l))!==c;)a.push(l);return{path:i.concat(a.reverse()),lca:c}}function PE(r){var n={},e=0;function t(i){var a=e;bf.forEach(r.children(i),t),n[i]={low:a,lim:e++}}return bf.forEach(r.children(),t),n}var re=Dr,Pf=hn,RE={run:kE,cleanup:IE};function kE(r){var n=Pf.addDummyNode(r,"root",{},"_root"),e=CE(r),t=re.max(re.values(e))-1,i=2*t+1;r.graph().nestingRoot=n,re.forEach(r.edges(),function(o){r.edge(o).minlen*=i});var a=LE(r)+1;re.forEach(r.children(),function(o){gg(r,n,i,a,t,e,o)}),r.graph().nodeRankFactor=i}function gg(r,n,e,t,i,a,o){var s=r.children(o);if(!s.length){o!==n&&r.setEdge(n,o,{weight:0,minlen:e});return}var l=Pf.addBorderNode(r,"_bt"),c=Pf.addBorderNode(r,"_bb"),u=r.node(o);r.setParent(l,o),u.borderTop=l,r.setParent(c,o),u.borderBottom=c,re.forEach(s,function(v){gg(r,n,e,t,i,a,v);var f=r.node(v),h=f.borderTop?f.borderTop:v,g=f.borderBottom?f.borderBottom:v,p=f.borderTop?t:2*t,d=h!==g?1:i-a[o]+1;r.setEdge(l,h,{weight:p,minlen:d,nestingEdge:!0}),r.setEdge(g,c,{weight:p,minlen:d,nestingEdge:!0})}),r.parent(o)||r.setEdge(n,l,{weight:0,minlen:i+a[o]})}function CE(r){var n={};function e(t,i){var a=r.children(t);a&&a.length&&re.forEach(a,function(o){e(o,i+1)}),n[t]=i}return re.forEach(r.children(),function(t){e(t,1)}),n}function LE(r){return re.reduce(r.edges(),function(n,e){return n+r.edge(e).weight},0)}function IE(r){var n=r.graph();r.removeNode(n.nestingRoot),delete n.nestingRoot,re.forEach(r.edges(),function(e){var t=r.edge(e);t.nestingEdge&&r.removeEdge(e)})}var Tu=Dr,NE=hn,AE=DE;function DE(r){function n(e){var t=r.children(e),i=r.node(e);if(t.length&&Tu.forEach(t,n),Tu.has(i,"minRank")){i.borderLeft=[],i.borderRight=[];for(var a=i.minRank,o=i.maxRank+1;a<o;++a)M0(r,"borderLeft","_bl",e,i,a),M0(r,"borderRight","_br",e,i,a)}}Tu.forEach(r.children(),n)}function M0(r,n,e,t,i,a){var o={width:0,height:0,rank:a,borderType:n},s=i[n][a-1],l=NE.addDummyNode(r,"border",o,e);i[n][a]=l,r.setParent(l,t),s&&r.setEdge(s,l,{weight:1})}var Yn=Dr,OE={adjust:qE,undo:FE};function qE(r){var n=r.graph().rankdir.toLowerCase();(n==="lr"||n==="rl")&&pg(r)}function FE(r){var n=r.graph().rankdir.toLowerCase();(n==="bt"||n==="rl")&&zE(r),(n==="lr"||n==="rl")&&(BE(r),pg(r))}function pg(r){Yn.forEach(r.nodes(),function(n){x0(r.node(n))}),Yn.forEach(r.edges(),function(n){x0(r.edge(n))})}function x0(r){var n=r.width;r.width=r.height,r.height=n}function zE(r){Yn.forEach(r.nodes(),function(n){$u(r.node(n))}),Yn.forEach(r.edges(),function(n){var e=r.edge(n);Yn.forEach(e.points,$u),Yn.has(e,"y")&&$u(e)})}function $u(r){r.y=-r.y}function BE(r){Yn.forEach(r.nodes(),function(n){Mu(r.node(n))}),Yn.forEach(r.edges(),function(n){var e=r.edge(n);Yn.forEach(e.points,Mu),Yn.has(e,"x")&&Mu(e)})}function Mu(r){var n=r.x;r.x=r.y,r.y=n}var Jn=Dr,GE=UE;function UE(r){var n={},e=Jn.filter(r.nodes(),function(s){return!r.children(s).length}),t=Jn.max(Jn.map(e,function(s){return r.node(s).rank})),i=Jn.map(Jn.range(t+1),function(){return[]});function a(s){if(!Jn.has(n,s)){n[s]=!0;var l=r.node(s);i[l.rank].push(s),Jn.forEach(r.successors(s),a)}}var o=Jn.sortBy(e,function(s){return r.node(s).rank});return Jn.forEach(o,a),i}var le=Dr,HE=YE;function YE(r,n){for(var e=0,t=1;t<n.length;++t)e+=VE(r,n[t-1],n[t]);return e}function VE(r,n,e){for(var t=le.zipObject(e,le.map(e,function(c,u){return u})),i=le.flatten(le.map(n,function(c){return le.sortBy(le.map(r.outEdges(c),function(u){return{pos:t[u.w],weight:r.edge(u).weight}}),"pos")}),!0),a=1;a<e.length;)a<<=1;var o=2*a-1;a-=1;var s=le.map(new Array(o),function(){return 0}),l=0;return le.forEach(i.forEach(function(c){var u=c.pos+a;s[u]+=c.weight;for(var v=0;u>0;)u%2&&(v+=s[u+1]),u=u-1>>1,s[u]+=c.weight;l+=c.weight*v})),l}var b0=Dr,XE=jE;function jE(r,n){return b0.map(n,function(e){var t=r.inEdges(e);if(t.length){var i=b0.reduce(t,function(a,o){var s=r.edge(o),l=r.node(o.v);return{sum:a.sum+s.weight*l.order,weight:a.weight+s.weight}},{sum:0,weight:0});return{v:e,barycenter:i.sum/i.weight,weight:i.weight}}else return{v:e}})}var yn=Dr,WE=JE;function JE(r,n){var e={};yn.forEach(r,function(i,a){var o=e[i.v]={indegree:0,in:[],out:[],vs:[i.v],i:a};yn.isUndefined(i.barycenter)||(o.barycenter=i.barycenter,o.weight=i.weight)}),yn.forEach(n.edges(),function(i){var a=e[i.v],o=e[i.w];!yn.isUndefined(a)&&!yn.isUndefined(o)&&(o.indegree++,a.out.push(e[i.w]))});var t=yn.filter(e,function(i){return!i.indegree});return KE(t)}function KE(r){var n=[];function e(a){return function(o){o.merged||(yn.isUndefined(o.barycenter)||yn.isUndefined(a.barycenter)||o.barycenter>=a.barycenter)&&QE(a,o)}}function t(a){return function(o){o.in.push(a),--o.indegree===0&&r.push(o)}}for(;r.length;){var i=r.pop();n.push(i),yn.forEach(i.in.reverse(),e(i)),yn.forEach(i.out,t(i))}return yn.map(yn.filter(n,function(a){return!a.merged}),function(a){return yn.pick(a,["vs","i","barycenter","weight"])})}function QE(r,n){var e=0,t=0;r.weight&&(e+=r.barycenter*r.weight,t+=r.weight),n.weight&&(e+=n.barycenter*n.weight,t+=n.weight),r.vs=n.vs.concat(r.vs),r.barycenter=e/t,r.weight=t,r.i=Math.min(n.i,r.i),n.merged=!0}var Ut=Dr,ZE=hn,r6=n6;function n6(r,n){var e=ZE.partition(r,function(u){return Ut.has(u,"barycenter")}),t=e.lhs,i=Ut.sortBy(e.rhs,function(u){return-u.i}),a=[],o=0,s=0,l=0;t.sort(e6(!!n)),l=P0(a,i,l),Ut.forEach(t,function(u){l+=u.vs.length,a.push(u.vs),o+=u.barycenter*u.weight,s+=u.weight,l=P0(a,i,l)});var c={vs:Ut.flatten(a,!0)};return s&&(c.barycenter=o/s,c.weight=s),c}function P0(r,n,e){for(var t;n.length&&(t=Ut.last(n)).i<=e;)n.pop(),r.push(t.vs),e++;return e}function e6(r){return function(n,e){return n.barycenter<e.barycenter?-1:n.barycenter>e.barycenter?1:r?e.i-n.i:n.i-e.i}}var he=Dr,t6=XE,i6=WE,a6=r6,o6=dg;function dg(r,n,e,t){var i=r.children(n),a=r.node(n),o=a?a.borderLeft:void 0,s=a?a.borderRight:void 0,l={};o&&(i=he.filter(i,function(g){return g!==o&&g!==s}));var c=t6(r,i);he.forEach(c,function(g){if(r.children(g.v).length){var p=dg(r,g.v,e,t);l[g.v]=p,he.has(p,"barycenter")&&f6(g,p)}});var u=i6(c,e);u6(u,l);var v=a6(u,t);if(o&&(v.vs=he.flatten([o,v.vs,s],!0),r.predecessors(o).length)){var f=r.node(r.predecessors(o)[0]),h=r.node(r.predecessors(s)[0]);he.has(v,"barycenter")||(v.barycenter=0,v.weight=0),v.barycenter=(v.barycenter*v.weight+f.order+h.order)/(v.weight+2),v.weight+=2}return v}function u6(r,n){he.forEach(r,function(e){e.vs=he.flatten(e.vs.map(function(t){return n[t]?n[t].vs:t}),!0)})}function f6(r,n){he.isUndefined(r.barycenter)?(r.barycenter=n.barycenter,r.weight=n.weight):(r.barycenter=(r.barycenter*r.weight+n.barycenter*n.weight)/(r.weight+n.weight),r.weight+=n.weight)}var Ht=Dr,l6=On.Graph,s6=c6;function c6(r,n,e){var t=v6(r),i=new l6({compound:!0}).setGraph({root:t}).setDefaultNodeLabel(function(a){return r.node(a)});return Ht.forEach(r.nodes(),function(a){var o=r.node(a),s=r.parent(a);(o.rank===n||o.minRank<=n&&n<=o.maxRank)&&(i.setNode(a),i.setParent(a,s||t),Ht.forEach(r[e](a),function(l){var c=l.v===a?l.w:l.v,u=i.edge(c,a),v=Ht.isUndefined(u)?0:u.weight;i.setEdge(c,a,{weight:r.edge(l).weight+v})}),Ht.has(o,"minRank")&&i.setNode(a,{borderLeft:o.borderLeft[n],borderRight:o.borderRight[n]}))}),i}function v6(r){for(var n;r.hasNode(n=Ht.uniqueId("_root")););return n}var h6=Dr,g6=p6;function p6(r,n,e){var t={},i;h6.forEach(e,function(a){for(var o=r.parent(a),s,l;o;){if(s=r.parent(o),s?(l=t[s],t[s]=o):(l=i,i=o),l&&l!==o){n.setEdge(l,o);return}o=s}})}var de=Dr,d6=GE,m6=HE,y6=o6,w6=s6,E6=g6,S6=On.Graph,R0=hn,_6=T6;function T6(r){var n=R0.maxRank(r),e=k0(r,de.range(1,n+1),"inEdges"),t=k0(r,de.range(n-1,-1,-1),"outEdges"),i=d6(r);C0(r,i);for(var a=Number.POSITIVE_INFINITY,o,s=0,l=0;l<4;++s,++l){$6(s%2?e:t,s%4>=2),i=R0.buildLayerMatrix(r);var c=m6(r,i);c<a&&(l=0,o=de.cloneDeep(i),a=c)}C0(r,o)}function k0(r,n,e){return de.map(n,function(t){return w6(r,t,e)})}function $6(r,n){var e=new S6;de.forEach(r,function(t){var i=t.graph().root,a=y6(t,i,e,n);de.forEach(a.vs,function(o,s){t.node(o).order=s}),E6(t,e,a.vs)})}function C0(r,n){de.forEach(n,function(e){de.forEach(e,function(t,i){r.node(t).order=i})})}var fr=Dr,M6=On.Graph,x6=hn,b6={positionX:k6,findType1Conflicts:mg,findType2Conflicts:yg,addConflict:ws,hasConflict:wg,verticalAlignment:Eg,horizontalCompaction:Sg,alignCoordinates:Tg,findSmallestWidthAlignment:_g,balance:$g};function mg(r,n){var e={};function t(i,a){var o=0,s=0,l=i.length,c=fr.last(a);return fr.forEach(a,function(u,v){var f=P6(r,u),h=f?r.node(f).order:l;(f||u===c)&&(fr.forEach(a.slice(s,v+1),function(g){fr.forEach(r.predecessors(g),function(p){var d=r.node(p),m=d.order;(m<o||h<m)&&!(d.dummy&&r.node(g).dummy)&&ws(e,p,g)})}),s=v+1,o=h)}),a}return fr.reduce(n,t),e}function yg(r,n){var e={};function t(a,o,s,l,c){var u;fr.forEach(fr.range(o,s),function(v){u=a[v],r.node(u).dummy&&fr.forEach(r.predecessors(u),function(f){var h=r.node(f);h.dummy&&(h.order<l||h.order>c)&&ws(e,f,u)})})}function i(a,o){var s=-1,l,c=0;return fr.forEach(o,function(u,v){if(r.node(u).dummy==="border"){var f=r.predecessors(u);f.length&&(l=r.node(f[0]).order,t(o,c,v,s,l),c=v,s=l)}t(o,c,o.length,l,a.length)}),o}return fr.reduce(n,i),e}function P6(r,n){if(r.node(n).dummy)return fr.find(r.predecessors(n),function(e){return r.node(e).dummy})}function ws(r,n,e){if(n>e){var t=n;n=e,e=t}var i=r[n];i||(r[n]=i={}),i[e]=!0}function wg(r,n,e){if(n>e){var t=n;n=e,e=t}return fr.has(r[n],e)}function Eg(r,n,e,t){var i={},a={},o={};return fr.forEach(n,function(s){fr.forEach(s,function(l,c){i[l]=l,a[l]=l,o[l]=c})}),fr.forEach(n,function(s){var l=-1;fr.forEach(s,function(c){var u=t(c);if(u.length){u=fr.sortBy(u,function(p){return o[p]});for(var v=(u.length-1)/2,f=Math.floor(v),h=Math.ceil(v);f<=h;++f){var g=u[f];a[c]===c&&l<o[g]&&!wg(e,c,g)&&(a[g]=c,a[c]=i[c]=i[g],l=o[g])}}})}),{root:i,align:a}}function Sg(r,n,e,t,i){var a={},o=R6(r,n,e,i),s=i?"borderLeft":"borderRight";function l(v,f){for(var h=o.nodes(),g=h.pop(),p={};g;)p[g]?v(g):(p[g]=!0,h.push(g),h=h.concat(f(g))),g=h.pop()}function c(v){a[v]=o.inEdges(v).reduce(function(f,h){return Math.max(f,a[h.v]+o.edge(h))},0)}function u(v){var f=o.outEdges(v).reduce(function(g,p){return Math.min(g,a[p.w]-o.edge(p))},Number.POSITIVE_INFINITY),h=r.node(v);f!==Number.POSITIVE_INFINITY&&h.borderType!==s&&(a[v]=Math.max(a[v],f))}return l(c,o.predecessors.bind(o)),l(u,o.successors.bind(o)),fr.forEach(t,function(v){a[v]=a[e[v]]}),a}function R6(r,n,e,t){var i=new M6,a=r.graph(),o=C6(a.nodesep,a.edgesep,t);return fr.forEach(n,function(s){var l;fr.forEach(s,function(c){var u=e[c];if(i.setNode(u),l){var v=e[l],f=i.edge(v,u);i.setEdge(v,u,Math.max(o(r,c,l),f||0))}l=c})}),i}function _g(r,n){return fr.minBy(fr.values(n),function(e){var t=Number.NEGATIVE_INFINITY,i=Number.POSITIVE_INFINITY;return fr.forIn(e,function(a,o){var s=L6(r,o)/2;t=Math.max(a+s,t),i=Math.min(a-s,i)}),t-i})}function Tg(r,n){var e=fr.values(n),t=fr.min(e),i=fr.max(e);fr.forEach(["u","d"],function(a){fr.forEach(["l","r"],function(o){var s=a+o,l=r[s],c;if(l!==n){var u=fr.values(l);c=o==="l"?t-fr.min(u):i-fr.max(u),c&&(r[s]=fr.mapValues(l,function(v){return v+c}))}})})}function $g(r,n){return fr.mapValues(r.ul,function(e,t){if(n)return r[n.toLowerCase()][t];var i=fr.sortBy(fr.map(r,t));return(i[1]+i[2])/2})}function k6(r){var n=x6.buildLayerMatrix(r),e=fr.merge(mg(r,n),yg(r,n)),t={},i;fr.forEach(["u","d"],function(o){i=o==="u"?n:fr.values(n).reverse(),fr.forEach(["l","r"],function(s){s==="r"&&(i=fr.map(i,function(v){return fr.values(v).reverse()}));var l=(o==="u"?r.predecessors:r.successors).bind(r),c=Eg(r,i,e,l),u=Sg(r,i,c.root,c.align,s==="r");s==="r"&&(u=fr.mapValues(u,function(v){return-v})),t[o+s]=u})});var a=_g(r,t);return Tg(t,a),$g(t,r.graph().align)}function C6(r,n,e){return function(t,i,a){var o=t.node(i),s=t.node(a),l=0,c;if(l+=o.width/2,fr.has(o,"labelpos"))switch(o.labelpos.toLowerCase()){case"l":c=-o.width/2;break;case"r":c=o.width/2;break}if(c&&(l+=e?c:-c),c=0,l+=(o.dummy?n:r)/2,l+=(s.dummy?n:r)/2,l+=s.width/2,fr.has(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":c=s.width/2;break;case"r":c=-s.width/2;break}return c&&(l+=e?c:-c),c=0,l}}function L6(r,n){return r.node(n).width}var Yt=Dr,Mg=hn,I6=b6.positionX,N6=A6;function A6(r){r=Mg.asNonCompoundGraph(r),D6(r),Yt.forEach(I6(r),function(n,e){r.node(e).x=n})}function D6(r){var n=Mg.buildLayerMatrix(r),e=r.graph().ranksep,t=0;Yt.forEach(n,function(i){var a=Yt.max(Yt.map(i,function(o){return r.node(o).height}));Yt.forEach(i,function(o){r.node(o).y=t+a/2}),t+=a+e})}var vr=Dr,L0=N9,I0=K9,O6=SE,q6=hn.normalizeRanks,F6=ME,z6=hn.removeEmptyRanks,N0=RE,B6=AE,A0=OE,G6=_6,U6=N6,Ee=hn,H6=On.Graph,Y6=V6;function V6(r,n){var e=n&&n.debugTiming?Ee.time:Ee.notime;e("layout",function(){var t=e("  buildLayoutGraph",function(){return t8(r)});e("  runLayout",function(){X6(t,e)}),e("  updateInputGraph",function(){j6(r,t)})})}function X6(r,n){n("    makeSpaceForEdgeLabels",function(){i8(r)}),n("    removeSelfEdges",function(){h8(r)}),n("    acyclic",function(){L0.run(r)}),n("    nestingGraph.run",function(){N0.run(r)}),n("    rank",function(){O6(Ee.asNonCompoundGraph(r))}),n("    injectEdgeLabelProxies",function(){a8(r)}),n("    removeEmptyRanks",function(){z6(r)}),n("    nestingGraph.cleanup",function(){N0.cleanup(r)}),n("    normalizeRanks",function(){q6(r)}),n("    assignRankMinMax",function(){o8(r)}),n("    removeEdgeLabelProxies",function(){u8(r)}),n("    normalize.run",function(){I0.run(r)}),n("    parentDummyChains",function(){F6(r)}),n("    addBorderSegments",function(){B6(r)}),n("    order",function(){G6(r)}),n("    insertSelfEdges",function(){g8(r)}),n("    adjustCoordinateSystem",function(){A0.adjust(r)}),n("    position",function(){U6(r)}),n("    positionSelfEdges",function(){p8(r)}),n("    removeBorderNodes",function(){v8(r)}),n("    normalize.undo",function(){I0.undo(r)}),n("    fixupEdgeLabelCoords",function(){s8(r)}),n("    undoCoordinateSystem",function(){A0.undo(r)}),n("    translateGraph",function(){f8(r)}),n("    assignNodeIntersects",function(){l8(r)}),n("    reversePoints",function(){c8(r)}),n("    acyclic.undo",function(){L0.undo(r)})}function j6(r,n){vr.forEach(r.nodes(),function(e){var t=r.node(e),i=n.node(e);t&&(t.x=i.x,t.y=i.y,n.children(e).length&&(t.width=i.width,t.height=i.height))}),vr.forEach(r.edges(),function(e){var t=r.edge(e),i=n.edge(e);t.points=i.points,vr.has(i,"x")&&(t.x=i.x,t.y=i.y)}),r.graph().width=n.graph().width,r.graph().height=n.graph().height}var W6=["nodesep","edgesep","ranksep","marginx","marginy"],J6={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},K6=["acyclicer","ranker","rankdir","align"],Q6=["width","height"],Z6={width:0,height:0},r8=["minlen","weight","width","height","labeloffset"],n8={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},e8=["labelpos"];function t8(r){var n=new H6({multigraph:!0,compound:!0}),e=bu(r.graph());return n.setGraph(vr.merge({},J6,xu(e,W6),vr.pick(e,K6))),vr.forEach(r.nodes(),function(t){var i=bu(r.node(t));n.setNode(t,vr.defaults(xu(i,Q6),Z6)),n.setParent(t,r.parent(t))}),vr.forEach(r.edges(),function(t){var i=bu(r.edge(t));n.setEdge(t,vr.merge({},n8,xu(i,r8),vr.pick(i,e8)))}),n}function i8(r){var n=r.graph();n.ranksep/=2,vr.forEach(r.edges(),function(e){var t=r.edge(e);t.minlen*=2,t.labelpos.toLowerCase()!=="c"&&(n.rankdir==="TB"||n.rankdir==="BT"?t.width+=t.labeloffset:t.height+=t.labeloffset)})}function a8(r){vr.forEach(r.edges(),function(n){var e=r.edge(n);if(e.width&&e.height){var t=r.node(n.v),i=r.node(n.w),a={rank:(i.rank-t.rank)/2+t.rank,e:n};Ee.addDummyNode(r,"edge-proxy",a,"_ep")}})}function o8(r){var n=0;vr.forEach(r.nodes(),function(e){var t=r.node(e);t.borderTop&&(t.minRank=r.node(t.borderTop).rank,t.maxRank=r.node(t.borderBottom).rank,n=vr.max(n,t.maxRank))}),r.graph().maxRank=n}function u8(r){vr.forEach(r.nodes(),function(n){var e=r.node(n);e.dummy==="edge-proxy"&&(r.edge(e.e).labelRank=e.rank,r.removeNode(n))})}function f8(r){var n=Number.POSITIVE_INFINITY,e=0,t=Number.POSITIVE_INFINITY,i=0,a=r.graph(),o=a.marginx||0,s=a.marginy||0;function l(c){var u=c.x,v=c.y,f=c.width,h=c.height;n=Math.min(n,u-f/2),e=Math.max(e,u+f/2),t=Math.min(t,v-h/2),i=Math.max(i,v+h/2)}vr.forEach(r.nodes(),function(c){l(r.node(c))}),vr.forEach(r.edges(),function(c){var u=r.edge(c);vr.has(u,"x")&&l(u)}),n-=o,t-=s,vr.forEach(r.nodes(),function(c){var u=r.node(c);u.x-=n,u.y-=t}),vr.forEach(r.edges(),function(c){var u=r.edge(c);vr.forEach(u.points,function(v){v.x-=n,v.y-=t}),vr.has(u,"x")&&(u.x-=n),vr.has(u,"y")&&(u.y-=t)}),a.width=e-n+o,a.height=i-t+s}function l8(r){vr.forEach(r.edges(),function(n){var e=r.edge(n),t=r.node(n.v),i=r.node(n.w),a,o;e.points?(a=e.points[0],o=e.points[e.points.length-1]):(e.points=[],a=i,o=t),e.points.unshift(Ee.intersectRect(t,a)),e.points.push(Ee.intersectRect(i,o))})}function s8(r){vr.forEach(r.edges(),function(n){var e=r.edge(n);if(vr.has(e,"x"))switch((e.labelpos==="l"||e.labelpos==="r")&&(e.width-=e.labeloffset),e.labelpos){case"l":e.x-=e.width/2+e.labeloffset;break;case"r":e.x+=e.width/2+e.labeloffset;break}})}function c8(r){vr.forEach(r.edges(),function(n){var e=r.edge(n);e.reversed&&e.points.reverse()})}function v8(r){vr.forEach(r.nodes(),function(n){if(r.children(n).length){var e=r.node(n),t=r.node(e.borderTop),i=r.node(e.borderBottom),a=r.node(vr.last(e.borderLeft)),o=r.node(vr.last(e.borderRight));e.width=Math.abs(o.x-a.x),e.height=Math.abs(i.y-t.y),e.x=a.x+e.width/2,e.y=t.y+e.height/2}}),vr.forEach(r.nodes(),function(n){r.node(n).dummy==="border"&&r.removeNode(n)})}function h8(r){vr.forEach(r.edges(),function(n){if(n.v===n.w){var e=r.node(n.v);e.selfEdges||(e.selfEdges=[]),e.selfEdges.push({e:n,label:r.edge(n)}),r.removeEdge(n)}})}function g8(r){var n=Ee.buildLayerMatrix(r);vr.forEach(n,function(e){var t=0;vr.forEach(e,function(i,a){var o=r.node(i);o.order=a+t,vr.forEach(o.selfEdges,function(s){Ee.addDummyNode(r,"selfedge",{width:s.label.width,height:s.label.height,rank:o.rank,order:a+ ++t,e:s.e,label:s.label},"_se")}),delete o.selfEdges})})}function p8(r){vr.forEach(r.nodes(),function(n){var e=r.node(n);if(e.dummy==="selfedge"){var t=r.node(e.e.v),i=t.x+t.width/2,a=t.y,o=e.x-i,s=t.height/2;r.setEdge(e.e,e.label),r.removeNode(n),e.label.points=[{x:i+2*o/3,y:a-s},{x:i+5*o/6,y:a-s},{x:i+o,y:a},{x:i+5*o/6,y:a+s},{x:i+2*o/3,y:a+s}],e.label.x=e.x,e.label.y=e.y}})}function xu(r,n){return vr.mapValues(vr.pick(r,n),Number)}function bu(r){var n={};return vr.forEach(r,function(e,t){n[t.toLowerCase()]=e}),n}var Xi=Dr,d8=hn,m8=On.Graph,y8={debugOrdering:w8};function w8(r){var n=d8.buildLayerMatrix(r),e=new m8({compound:!0,multigraph:!0}).setGraph({});return Xi.forEach(r.nodes(),function(t){e.setNode(t,{label:t}),e.setParent(t,"layer"+r.node(t).rank)}),Xi.forEach(r.edges(),function(t){e.setEdge(t.v,t.w,{},t.name)}),Xi.forEach(n,function(t,i){var a="layer"+i;e.setNode(a,{rank:"same"}),Xi.reduce(t,function(o,s){return e.setEdge(o,s,{style:"invis"}),s})}),e}var E8="0.8.5",S8={graphlib:On,layout:Y6,debug:y8,util:{time:hn.time,notime:hn.notime},version:E8},_8=pr,T8=or,D0=_8.__importDefault(S8),xg=ir,$8={rankdir:"TB",align:"TB",nodesep:50,edgesep:10,ranksep:50,source:function(r){return r.source},target:function(r){return r.target}};function bg(r,n){n=T8.assign({},$8,n);var e=new D0.default.graphlib.Graph;e.setGraph({}),e.setDefaultEdgeLabel(function(){return{}}),r.nodes.forEach(function(a){var o=n.nodeId?n.nodeId(a):a.id;!a.height&&!a.width&&(a.height=a.width=n.edgesep),e.setNode(o,a)}),r.edges.forEach(function(a){e.setEdge(n.source(a),n.target(a))}),D0.default.layout(e);var t=[],i=[];e.nodes().forEach(function(a){var o=e.node(a),s=o.x,l=o.y,c=o.height,u=o.width;o.x=[s-u/2,s+u/2,s+u/2,s-u/2],o.y=[l+c/2,l+c/2,l-c/2,l-c/2],t.push(o)}),e.edges().forEach(function(a){var o=e.edge(a).points,s={};s.x=o.map(function(l){return l.x}),s.y=o.map(function(l){return l.y}),i.push(s)}),r.nodes=t,r.edges=i}xg.DataSet.registerTransform("diagram.dagre",bg);xg.DataSet.registerTransform("dagre",bg);var $n="$";function no(){}no.prototype=wi.prototype={constructor:no,has:function(r){return $n+r in this},get:function(r){return this[$n+r]},set:function(r,n){return this[$n+r]=n,this},remove:function(r){var n=$n+r;return n in this&&delete this[n]},clear:function(){for(var r in this)r[0]===$n&&delete this[r]},keys:function(){var r=[];for(var n in this)n[0]===$n&&r.push(n.slice(1));return r},values:function(){var r=[];for(var n in this)n[0]===$n&&r.push(this[n]);return r},entries:function(){var r=[];for(var n in this)n[0]===$n&&r.push({key:n.slice(1),value:this[n]});return r},size:function(){var r=0;for(var n in this)n[0]===$n&&++r;return r},empty:function(){for(var r in this)if(r[0]===$n)return!1;return!0},each:function(r){for(var n in this)n[0]===$n&&r(this[n],n.slice(1),this)}};function wi(r,n){var e=new no;if(r instanceof no)r.each(function(s,l){e.set(l,s)});else if(Array.isArray(r)){var t=-1,i=r.length,a;if(n==null)for(;++t<i;)e.set(t,r[t]);else for(;++t<i;)e.set(n(a=r[t],t,r),a)}else if(r)for(var o in r)e.set(o,r[o]);return e}function M8(){var r=[],n=[],e,t,i;function a(s,l,c,u){if(l>=r.length)return e!=null&&s.sort(e),t!=null?t(s):s;for(var v=-1,f=s.length,h=r[l++],g,p,d=wi(),m,y=c();++v<f;)(m=d.get(g=h(p=s[v])+""))?m.push(p):d.set(g,[p]);return d.each(function(E,S){u(y,S,a(E,l,c,u))}),y}function o(s,l){if(++l>r.length)return s;var c,u=n[l-1];return t!=null&&l>=r.length?c=s.entries():(c=[],s.each(function(v,f){c.push({key:f,values:o(v,l)})})),u!=null?c.sort(function(v,f){return u(v.key,f.key)}):c}return i={object:function(s){return a(s,0,x8,b8)},map:function(s){return a(s,0,O0,q0)},entries:function(s){return o(a(s,0,O0,q0),0)},key:function(s){return r.push(s),i},sortKeys:function(s){return n[r.length-1]=s,i},sortValues:function(s){return e=s,i},rollup:function(s){return t=s,i}}}function x8(){return{}}function b8(r,n,e){r[n]=e}function O0(){return wi()}function q0(r,n,e){r.set(n,e)}function F0(){}var be=wi.prototype;F0.prototype={constructor:F0,has:be.has,add:function(r){return r+="",this[$n+r]=r,this},remove:be.remove,clear:be.clear,values:be.keys,size:be.size,empty:be.empty,each:be.each};function P8(r){return r.target.depth}function R8(r){return r.depth}function k8(r,n){return n-1-r.height}function Pg(r,n){return r.sourceLinks.length?r.depth:n-1}function C8(r){return r.targetLinks.length?r.depth:r.sourceLinks.length?rn(r.sourceLinks,P8)-1:0}function ji(r){return function(){return r}}function z0(r,n){return Es(r.source,n.source)||r.index-n.index}function B0(r,n){return Es(r.target,n.target)||r.index-n.index}function Es(r,n){return r.y0-n.y0}function Pu(r){return r.value}function L8(r){return r.index}function I8(r){return r.nodes}function N8(r){return r.links}function G0(r,n){var e=r.get(n);if(!e)throw new Error("missing: "+n);return e}function A8(){var r=0,n=0,e=1,t=1,i=24,a=8,o=L8,s=Pg,l,c=I8,u=N8,v=6;function f(){var y={nodes:c.apply(null,arguments),links:u.apply(null,arguments)};return h(y),g(y),p(y),d(y),m(y),y}f.update=function(y){return m(y),y},f.nodeId=function(y){return arguments.length?(o=typeof y=="function"?y:ji(y),f):o},f.nodeAlign=function(y){return arguments.length?(s=typeof y=="function"?y:ji(y),f):s},f.nodeSort=function(y){return arguments.length?(l=y,f):l},f.nodeWidth=function(y){return arguments.length?(i=+y,f):i},f.nodePadding=function(y){return arguments.length?(a=+y,f):a},f.nodes=function(y){return arguments.length?(c=typeof y=="function"?y:ji(y),f):c},f.links=function(y){return arguments.length?(u=typeof y=="function"?y:ji(y),f):u},f.size=function(y){return arguments.length?(r=n=0,e=+y[0],t=+y[1],f):[e-r,t-n]},f.extent=function(y){return arguments.length?(r=+y[0][0],e=+y[1][0],n=+y[0][1],t=+y[1][1],f):[[r,n],[e,t]]},f.iterations=function(y){return arguments.length?(v=+y,f):v};function h(y){y.nodes.forEach(function(S,$){S.index=$,S.sourceLinks=[],S.targetLinks=[]});var E=wi(y.nodes,o);y.links.forEach(function(S,$){S.index=$;var x=S.source,I=S.target;typeof x!="object"&&(x=S.source=G0(E,x)),typeof I!="object"&&(I=S.target=G0(E,I)),x.sourceLinks.push(S),I.targetLinks.push(S)})}function g(y){y.nodes.forEach(function(E){E.value=Math.max(zo(E.sourceLinks,Pu),zo(E.targetLinks,Pu))})}function p(y){var E,S,$,x=y.nodes.length;for(E=y.nodes,S=[],$=0;E.length;++$,E=S,S=[]){if($>x)throw new Error("circular link");E.forEach(function(C){C.depth=$,C.sourceLinks.forEach(function(L){S.indexOf(L.target)<0&&S.push(L.target)})})}for(E=y.nodes,S=[],$=0;E.length;++$,E=S,S=[]){if($>x)throw new Error("circular link");E.forEach(function(C){C.height=$,C.targetLinks.forEach(function(L){S.indexOf(L.source)<0&&S.push(L.source)})})}var I=(e-r-i)/($-1);y.nodes.forEach(function(C){C.x1=(C.x0=r+Math.max(0,Math.min($-1,Math.floor(s.call(null,C,$))))*I)+i})}function d(y){var E=M8().key(function(O){return O.x0}).sortKeys(zf).entries(y.nodes).map(function(O){return O.values});x(),L();for(var S=.9,$=v;$>0;--$,S*=.9)C(S),L(),I(S),L();function x(){var O=rn(E,function(A){return(t-n-(A.length-1)*a)/zo(A,Pu)});E.forEach(function(A){l!=null&&A.sort(l),A.forEach(function(k,U){k.y1=(k.y0=U)+k.value*O})}),y.links.forEach(function(A){A.width=A.value*O})}function I(O){E.forEach(function(A){A.forEach(function(k){let U=k.y0;for(const{target:_,width:w,value:b}of k.sourceLinks.sort(B0)){if(b>0){let P=0;for(const{source:D,width:rr}of _.targetLinks){if(D===k)break;P+=rr+a/2}P=(U-P-_.y0)*O*(b/Math.min(k.value,_.value)),_.y0+=P,_.y1+=P}U+=w+a/2}})})}function C(O){E.slice().reverse().forEach(function(A){A.forEach(function(k){let U=k.y0;for(const{source:_,width:w,value:b}of k.targetLinks.sort(z0)){if(b>0){let P=0;for(const{target:D,width:rr}of _.sourceLinks){if(D===k)break;P+=rr+a/2}P=(U-P-_.y0)*O*(b/Math.min(k.value,_.value)),_.y0+=P,_.y1+=P}U+=w+a/2}})})}function L(){E.forEach(function(O){var A,k,U=n,_=O.length,w;for(l===void 0&&O.sort(Es),w=0;w<_;++w)A=O[w],k=U-A.y0,k>0&&(A.y0+=k,A.y1+=k),U=A.y1+a;if(k=U-a-t,k>0)for(U=A.y0-=k,A.y1-=k,w=_-2;w>=0;--w)A=O[w],k=A.y1+a-U,k>0&&(A.y0-=k,A.y1-=k),U=A.y0})}}function m(y){y.nodes.forEach(function(E){E.sourceLinks.sort(B0),E.targetLinks.sort(z0)}),y.nodes.forEach(function(E){var S=E.y0,$=S;E.sourceLinks.forEach(function(x){x.y0=S+x.width/2,S+=x.width}),E.targetLinks.forEach(function(x){x.y1=$+x.width/2,$+=x.width})})}return f}var Rf=Math.PI,kf=2*Rf,Le=1e-6,D8=kf-Le;function Cf(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function Rg(){return new Cf}Cf.prototype=Rg.prototype={constructor:Cf,moveTo:function(r,n){this._+="M"+(this._x0=this._x1=+r)+","+(this._y0=this._y1=+n)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(r,n){this._+="L"+(this._x1=+r)+","+(this._y1=+n)},quadraticCurveTo:function(r,n,e,t){this._+="Q"+ +r+","+ +n+","+(this._x1=+e)+","+(this._y1=+t)},bezierCurveTo:function(r,n,e,t,i,a){this._+="C"+ +r+","+ +n+","+ +e+","+ +t+","+(this._x1=+i)+","+(this._y1=+a)},arcTo:function(r,n,e,t,i){r=+r,n=+n,e=+e,t=+t,i=+i;var a=this._x1,o=this._y1,s=e-r,l=t-n,c=a-r,u=o-n,v=c*c+u*u;if(i<0)throw new Error("negative radius: "+i);if(this._x1===null)this._+="M"+(this._x1=r)+","+(this._y1=n);else if(v>Le)if(!(Math.abs(u*s-l*c)>Le)||!i)this._+="L"+(this._x1=r)+","+(this._y1=n);else{var f=e-a,h=t-o,g=s*s+l*l,p=f*f+h*h,d=Math.sqrt(g),m=Math.sqrt(v),y=i*Math.tan((Rf-Math.acos((g+v-p)/(2*d*m)))/2),E=y/m,S=y/d;Math.abs(E-1)>Le&&(this._+="L"+(r+E*c)+","+(n+E*u)),this._+="A"+i+","+i+",0,0,"+ +(u*f>c*h)+","+(this._x1=r+S*s)+","+(this._y1=n+S*l)}},arc:function(r,n,e,t,i,a){r=+r,n=+n,e=+e,a=!!a;var o=e*Math.cos(t),s=e*Math.sin(t),l=r+o,c=n+s,u=1^a,v=a?t-i:i-t;if(e<0)throw new Error("negative radius: "+e);this._x1===null?this._+="M"+l+","+c:(Math.abs(this._x1-l)>Le||Math.abs(this._y1-c)>Le)&&(this._+="L"+l+","+c),e&&(v<0&&(v=v%kf+kf),v>D8?this._+="A"+e+","+e+",0,1,"+u+","+(r-o)+","+(n-s)+"A"+e+","+e+",0,1,"+u+","+(this._x1=l)+","+(this._y1=c):v>Le&&(this._+="A"+e+","+e+",0,"+ +(v>=Rf)+","+u+","+(this._x1=r+e*Math.cos(i))+","+(this._y1=n+e*Math.sin(i))))},rect:function(r,n,e,t){this._+="M"+(this._x0=this._x1=+r)+","+(this._y0=this._y1=+n)+"h"+ +e+"v"+ +t+"h"+-e+"Z"},toString:function(){return this._}};function U0(r){return function(){return r}}function O8(r){return r[0]}function q8(r){return r[1]}var F8=Array.prototype.slice;function z8(r){return r.source}function B8(r){return r.target}function G8(r){var n=z8,e=B8,t=O8,i=q8,a=null;function o(){var s,l=F8.call(arguments),c=n.apply(this,l),u=e.apply(this,l);if(a||(a=s=Rg()),r(a,+t.apply(this,(l[0]=c,l)),+i.apply(this,l),+t.apply(this,(l[0]=u,l)),+i.apply(this,l)),s)return a=null,s+""||null}return o.source=function(s){return arguments.length?(n=s,o):n},o.target=function(s){return arguments.length?(e=s,o):e},o.x=function(s){return arguments.length?(t=typeof s=="function"?s:U0(+s),o):t},o.y=function(s){return arguments.length?(i=typeof s=="function"?s:U0(+s),o):i},o.context=function(s){return arguments.length?(a=s==null?null:s,o):a},o}function U8(r,n,e,t,i){r.moveTo(n,e),r.bezierCurveTo(n=(n+t)/2,e,n,i,t,i)}function H8(){return G8(U8)}function Y8(r){return[r.source.x1,r.y0]}function V8(r){return[r.target.x0,r.y1]}function X8(){return H8().source(Y8).target(V8)}var j8=Object.freeze(Object.defineProperty({__proto__:null,sankey:A8,sankeyCenter:C8,sankeyLeft:R8,sankeyRight:k8,sankeyJustify:Pg,sankeyLinkHorizontal:X8},Symbol.toStringTag,{value:"Module"})),W8=oe(j8),Wi=or,Vt=W8,kg=ir,J8={sankeyLeft:Vt.sankeyLeft,sankeyRight:Vt.sankeyRight,sankeyCenter:Vt.sankeyCenter,sankeyJustify:Vt.sankeyJustify},K8={value:function(r){return r.value},source:function(r){return r.source},target:function(r){return r.target},nodeAlign:"sankeyJustify",nodeWidth:.02,nodePadding:.02,sort:void 0};function Cg(r,n){n=Wi.assign({},K8,n);var e=null;Wi.isString(n.nodeAlign)?e=J8[n.nodeAlign]:Wi.isFunction(n.nodeAlign)&&(e=n.nodeAlign);var t=Vt.sankey().nodeSort(n.sort).links(function(i){return i.edges}).nodeWidth(n.nodeWidth).nodePadding(n.nodePadding).extent([[0,0],[1,1]]);Wi.isFunction(n.nodeId)&&t.nodeId(n.nodeId),e&&t.nodeAlign(e),t(r),r.nodes.forEach(function(i){var a=i.x0,o=i.x1,s=i.y0,l=i.y1;i.x=[a,o,o,a],i.y=[s,s,l,l]}),r.edges.forEach(function(i){var a=i.source,o=i.target,s=a.x1,l=o.x0;i.x=[s,s,l,l];var c=i.width/2;i.y=[i.y0+c,i.y0-c,i.y1+c,i.y1-c]})}kg.DataSet.registerTransform("diagram.sankey",Cg);kg.DataSet.registerTransform("sankey",Cg);function H0(r){return function(){return r}}function Q8(r){return r[0]}function Z8(r){return r[1]}function eo(){this._=null}function ko(r){r.U=r.C=r.L=r.R=r.P=r.N=null}eo.prototype={constructor:eo,insert:function(r,n){var e,t,i;if(r){if(n.P=r,n.N=r.N,r.N&&(r.N.P=n),r.N=n,r.R){for(r=r.R;r.L;)r=r.L;r.L=n}else r.R=n;e=r}else this._?(r=Y0(this._),n.P=null,n.N=r,r.P=r.L=n,e=r):(n.P=n.N=null,this._=n,e=null);for(n.L=n.R=null,n.U=e,n.C=!0,r=n;e&&e.C;)t=e.U,e===t.L?(i=t.R,i&&i.C?(e.C=i.C=!1,t.C=!0,r=t):(r===e.R&&(Ct(this,e),r=e,e=r.U),e.C=!1,t.C=!0,Lt(this,t))):(i=t.L,i&&i.C?(e.C=i.C=!1,t.C=!0,r=t):(r===e.L&&(Lt(this,e),r=e,e=r.U),e.C=!1,t.C=!0,Ct(this,t))),e=r.U;this._.C=!1},remove:function(r){r.N&&(r.N.P=r.P),r.P&&(r.P.N=r.N),r.N=r.P=null;var n=r.U,e,t=r.L,i=r.R,a,o;if(t?i?a=Y0(i):a=t:a=i,n?n.L===r?n.L=a:n.R=a:this._=a,t&&i?(o=a.C,a.C=r.C,a.L=t,t.U=a,a!==i?(n=a.U,a.U=r.U,r=a.R,n.L=r,a.R=i,i.U=a):(a.U=n,n=a,r=a.R)):(o=r.C,r=a),r&&(r.U=n),!o){if(r&&r.C){r.C=!1;return}do{if(r===this._)break;if(r===n.L){if(e=n.R,e.C&&(e.C=!1,n.C=!0,Ct(this,n),e=n.R),e.L&&e.L.C||e.R&&e.R.C){(!e.R||!e.R.C)&&(e.L.C=!1,e.C=!0,Lt(this,e),e=n.R),e.C=n.C,n.C=e.R.C=!1,Ct(this,n),r=this._;break}}else if(e=n.L,e.C&&(e.C=!1,n.C=!0,Lt(this,n),e=n.L),e.L&&e.L.C||e.R&&e.R.C){(!e.L||!e.L.C)&&(e.R.C=!1,e.C=!0,Ct(this,e),e=n.L),e.C=n.C,n.C=e.L.C=!1,Lt(this,n),r=this._;break}e.C=!0,r=n,n=n.U}while(!r.C);r&&(r.C=!1)}}};function Ct(r,n){var e=n,t=n.R,i=e.U;i?i.L===e?i.L=t:i.R=t:r._=t,t.U=i,e.U=t,e.R=t.L,e.R&&(e.R.U=e),t.L=e}function Lt(r,n){var e=n,t=n.L,i=e.U;i?i.L===e?i.L=t:i.R=t:r._=t,t.U=i,e.U=t,e.L=t.R,e.L&&(e.L.U=e),t.R=e}function Y0(r){for(;r.L;)r=r.L;return r}function Xt(r,n,e,t){var i=[null,null],a=Qr.push(i)-1;return i.left=r,i.right=n,e&&to(i,r,n,e),t&&to(i,n,r,t),En[r.index].halfedges.push(a),En[n.index].halfedges.push(a),i}function It(r,n,e){var t=[n,e];return t.left=r,t}function to(r,n,e,t){!r[0]&&!r[1]?(r[0]=t,r.left=n,r.right=e):r.left===e?r[1]=t:r[0]=t}function rS(r,n,e,t,i){var a=r[0],o=r[1],s=a[0],l=a[1],c=o[0],u=o[1],v=0,f=1,h=c-s,g=u-l,p;if(p=n-s,!(!h&&p>0)){if(p/=h,h<0){if(p<v)return;p<f&&(f=p)}else if(h>0){if(p>f)return;p>v&&(v=p)}if(p=t-s,!(!h&&p<0)){if(p/=h,h<0){if(p>f)return;p>v&&(v=p)}else if(h>0){if(p<v)return;p<f&&(f=p)}if(p=e-l,!(!g&&p>0)){if(p/=g,g<0){if(p<v)return;p<f&&(f=p)}else if(g>0){if(p>f)return;p>v&&(v=p)}if(p=i-l,!(!g&&p<0)){if(p/=g,g<0){if(p>f)return;p>v&&(v=p)}else if(g>0){if(p<v)return;p<f&&(f=p)}return!(v>0)&&!(f<1)||(v>0&&(r[0]=[s+v*h,l+v*g]),f<1&&(r[1]=[s+f*h,l+f*g])),!0}}}}}function nS(r,n,e,t,i){var a=r[1];if(a)return!0;var o=r[0],s=r.left,l=r.right,c=s[0],u=s[1],v=l[0],f=l[1],h=(c+v)/2,g=(u+f)/2,p,d;if(f===u){if(h<n||h>=t)return;if(c>v){if(!o)o=[h,e];else if(o[1]>=i)return;a=[h,i]}else{if(!o)o=[h,i];else if(o[1]<e)return;a=[h,e]}}else if(p=(c-v)/(f-u),d=g-p*h,p<-1||p>1)if(c>v){if(!o)o=[(e-d)/p,e];else if(o[1]>=i)return;a=[(i-d)/p,i]}else{if(!o)o=[(i-d)/p,i];else if(o[1]<e)return;a=[(e-d)/p,e]}else if(u<f){if(!o)o=[n,p*n+d];else if(o[0]>=t)return;a=[t,p*t+d]}else{if(!o)o=[t,p*t+d];else if(o[0]<n)return;a=[n,p*n+d]}return r[0]=o,r[1]=a,!0}function eS(r,n,e,t){for(var i=Qr.length,a;i--;)(!nS(a=Qr[i],r,n,e,t)||!rS(a,r,n,e,t)||!(Math.abs(a[0][0]-a[1][0])>kr||Math.abs(a[0][1]-a[1][1])>kr))&&delete Qr[i]}function tS(r){return En[r.index]={site:r,halfedges:[]}}function iS(r,n){var e=r.site,t=n.left,i=n.right;return e===i&&(i=t,t=e),i?Math.atan2(i[1]-t[1],i[0]-t[0]):(e===t?(t=n[1],i=n[0]):(t=n[0],i=n[1]),Math.atan2(t[0]-i[0],i[1]-t[1]))}function Lg(r,n){return n[+(n.left!==r.site)]}function aS(r,n){return n[+(n.left===r.site)]}function oS(){for(var r=0,n=En.length,e,t,i,a;r<n;++r)if((e=En[r])&&(a=(t=e.halfedges).length)){var o=new Array(a),s=new Array(a);for(i=0;i<a;++i)o[i]=i,s[i]=iS(e,Qr[t[i]]);for(o.sort(function(l,c){return s[c]-s[l]}),i=0;i<a;++i)s[i]=t[o[i]];for(i=0;i<a;++i)t[i]=s[i]}}function uS(r,n,e,t){var i=En.length,a,o,s,l,c,u,v,f,h,g,p,d,m=!0;for(a=0;a<i;++a)if(o=En[a]){for(s=o.site,c=o.halfedges,l=c.length;l--;)Qr[c[l]]||c.splice(l,1);for(l=0,u=c.length;l<u;)g=aS(o,Qr[c[l]]),p=g[0],d=g[1],v=Lg(o,Qr[c[++l%u]]),f=v[0],h=v[1],(Math.abs(p-f)>kr||Math.abs(d-h)>kr)&&(c.splice(l,0,Qr.push(It(s,g,Math.abs(p-r)<kr&&t-d>kr?[r,Math.abs(f-r)<kr?h:t]:Math.abs(d-t)<kr&&e-p>kr?[Math.abs(h-t)<kr?f:e,t]:Math.abs(p-e)<kr&&d-n>kr?[e,Math.abs(f-e)<kr?h:n]:Math.abs(d-n)<kr&&p-r>kr?[Math.abs(h-n)<kr?f:r,n]:null))-1),++u);u&&(m=!1)}if(m){var y,E,S,$=1/0;for(a=0,m=null;a<i;++a)(o=En[a])&&(s=o.site,y=s[0]-r,E=s[1]-n,S=y*y+E*E,S<$&&($=S,m=o));if(m){var x=[r,n],I=[r,t],C=[e,t],L=[e,n];m.halfedges.push(Qr.push(It(s=m.site,x,I))-1,Qr.push(It(s,I,C))-1,Qr.push(It(s,C,L))-1,Qr.push(It(s,L,x))-1)}}for(a=0;a<i;++a)(o=En[a])&&(o.halfedges.length||delete En[a])}var Ig=[],Ss;function fS(){ko(this),this.x=this.y=this.arc=this.site=this.cy=null}function We(r){var n=r.P,e=r.N;if(!(!n||!e)){var t=n.site,i=r.site,a=e.site;if(t!==a){var o=i[0],s=i[1],l=t[0]-o,c=t[1]-s,u=a[0]-o,v=a[1]-s,f=2*(l*v-c*u);if(!(f>=-hS)){var h=l*l+c*c,g=u*u+v*v,p=(v*h-c*g)/f,d=(l*g-u*h)/f,m=Ig.pop()||new fS;m.arc=r,m.site=i,m.x=p+o,m.y=(m.cy=d+s)+Math.sqrt(p*p+d*d),r.circle=m;for(var y=null,E=li._;E;)if(m.y<E.y||m.y===E.y&&m.x<=E.x)if(E.L)E=E.L;else{y=E.P;break}else if(E.R)E=E.R;else{y=E;break}li.insert(y,m),y||(Ss=m)}}}}function rt(r){var n=r.circle;n&&(n.P||(Ss=n.N),li.remove(n),Ig.push(n),ko(n),r.circle=null)}var Ng=[];function lS(){ko(this),this.edge=this.site=this.circle=null}function V0(r){var n=Ng.pop()||new lS;return n.site=r,n}function Ru(r){rt(r),nt.remove(r),Ng.push(r),ko(r)}function sS(r){var n=r.circle,e=n.x,t=n.cy,i=[e,t],a=r.P,o=r.N,s=[r];Ru(r);for(var l=a;l.circle&&Math.abs(e-l.circle.x)<kr&&Math.abs(t-l.circle.cy)<kr;)a=l.P,s.unshift(l),Ru(l),l=a;s.unshift(l),rt(l);for(var c=o;c.circle&&Math.abs(e-c.circle.x)<kr&&Math.abs(t-c.circle.cy)<kr;)o=c.N,s.push(c),Ru(c),c=o;s.push(c),rt(c);var u=s.length,v;for(v=1;v<u;++v)c=s[v],l=s[v-1],to(c.edge,l.site,c.site,i);l=s[0],c=s[u-1],c.edge=Xt(l.site,c.site,null,i),We(l),We(c)}function cS(r){for(var n=r[0],e=r[1],t,i,a,o,s=nt._;s;)if(a=Ag(s,e)-n,a>kr)s=s.L;else if(o=n-vS(s,e),o>kr){if(!s.R){t=s;break}s=s.R}else{a>-kr?(t=s.P,i=s):o>-kr?(t=s,i=s.N):t=i=s;break}tS(r);var l=V0(r);if(nt.insert(t,l),!(!t&&!i)){if(t===i){rt(t),i=V0(t.site),nt.insert(l,i),l.edge=i.edge=Xt(t.site,l.site),We(t),We(i);return}if(!i){l.edge=Xt(t.site,l.site);return}rt(t),rt(i);var c=t.site,u=c[0],v=c[1],f=r[0]-u,h=r[1]-v,g=i.site,p=g[0]-u,d=g[1]-v,m=2*(f*d-h*p),y=f*f+h*h,E=p*p+d*d,S=[(d*y-h*E)/m+u,(f*E-p*y)/m+v];to(i.edge,c,g,S),l.edge=Xt(c,r,null,S),i.edge=Xt(r,g,null,S),We(t),We(i)}}function Ag(r,n){var e=r.site,t=e[0],i=e[1],a=i-n;if(!a)return t;var o=r.P;if(!o)return-1/0;e=o.site;var s=e[0],l=e[1],c=l-n;if(!c)return s;var u=s-t,v=1/a-1/c,f=u/c;return v?(-f+Math.sqrt(f*f-2*v*(u*u/(-2*c)-l+c/2+i-a/2)))/v+t:(t+s)/2}function vS(r,n){var e=r.N;if(e)return Ag(e,n);var t=r.site;return t[1]===n?t[0]:1/0}var kr=1e-6,hS=1e-12,nt,En,li,Qr;function gS(r,n,e){return(r[0]-e[0])*(n[1]-r[1])-(r[0]-n[0])*(e[1]-r[1])}function pS(r,n){return n[1]-r[1]||n[0]-r[0]}function Lf(r,n){var e=r.sort(pS).pop(),t,i,a;for(Qr=[],En=new Array(r.length),nt=new eo,li=new eo;;)if(a=Ss,e&&(!a||e[1]<a.y||e[1]===a.y&&e[0]<a.x))(e[0]!==t||e[1]!==i)&&(cS(e),t=e[0],i=e[1]),e=r.pop();else if(a)sS(a.arc);else break;if(oS(),n){var o=+n[0][0],s=+n[0][1],l=+n[1][0],c=+n[1][1];eS(o,s,l,c),uS(o,s,l,c)}this.edges=Qr,this.cells=En,nt=li=Qr=En=null}Lf.prototype={constructor:Lf,polygons:function(){var r=this.edges;return this.cells.map(function(n){var e=n.halfedges.map(function(t){return Lg(n,r[t])});return e.data=n.site.data,e})},triangles:function(){var r=[],n=this.edges;return this.cells.forEach(function(e,t){if(!!(s=(a=e.halfedges).length))for(var i=e.site,a,o=-1,s,l,c=n[a[s-1]],u=c.left===i?c.right:c.left;++o<s;)l=u,c=n[a[o]],u=c.left===i?c.right:c.left,l&&u&&t<l.index&&t<u.index&&gS(i,l,u)<0&&r.push([i.data,l.data,u.data])}),r},links:function(){return this.edges.filter(function(r){return r.right}).map(function(r){return{source:r.left.data,target:r.right.data}})},find:function(r,n,e){for(var t=this,i,a=t._found||0,o=t.cells.length,s;!(s=t.cells[a]);)if(++a>=o)return null;var l=r-s.site[0],c=n-s.site[1],u=l*l+c*c;do s=t.cells[i=a],a=null,s.halfedges.forEach(function(v){var f=t.edges[v],h=f.left;if(!((h===s.site||!h)&&!(h=f.right))){var g=r-h[0],p=n-h[1],d=g*g+p*p;d<u&&(u=d,a=h.index)}});while(a!==null);return t._found=i,e==null||u<=e*e?s.site:null}};function dS(){var r=Q8,n=Z8,e=null;function t(i){return new Lf(i.map(function(a,o){var s=[Math.round(r(a,o,i)/kr)*kr,Math.round(n(a,o,i)/kr)*kr];return s.index=o,s.data=a,s}),e)}return t.polygons=function(i){return t(i).polygons()},t.links=function(i){return t(i).links()},t.triangles=function(i){return t(i).triangles()},t.x=function(i){return arguments.length?(r=typeof i=="function"?i:H0(+i),t):r},t.y=function(i){return arguments.length?(n=typeof i=="function"?i:H0(+i),t):n},t.extent=function(i){return arguments.length?(e=i==null?null:[[+i[0][0],+i[0][1]],[+i[1][0],+i[1][1]]],t):e&&[[e[0][0],e[0][1]],[e[1][0],e[1][1]]]},t.size=function(i){return arguments.length?(e=i==null?null:[[0,0],[+i[0],+i[1]]],t):e&&[e[1][0]-e[0][0],e[1][1]-e[0][1]]},t}var mS=Object.freeze(Object.defineProperty({__proto__:null,voronoi:dS},Symbol.toStringTag,{value:"Module"})),yS=oe(mS),wS=pr,ES=wS.__importStar(yS),ku=or,SS=ir,Dg=SS.DataSet.registerTransform,_S=Mr,TS={as:["_x","_y"]};function Og(r,n){n=ku.assign({},TS,n);var e=n.as;if(!ku.isArray(e)||e.length!==2)throw new TypeError("Invalid as: must be an array with two strings!");var t=e[0],i=e[1],a=_S.getFields(n);if(!ku.isArray(a)||a.length!==2)throw new TypeError("Invalid fields: must be an array with two strings!");var o=a[0],s=a[1],l=r.rows,c=l.map(function(f){return[f[o],f[s]]}),u=ES.voronoi();n.extend&&u.extent(n.extend),n.size&&u.size(n.size);var v=u(c).polygons();l.forEach(function(f,h){var g=v[h].filter(function(p){return!!p});f[t]=g.map(function(p){return p[0]}),f[i]=g.map(function(p){return p[1]})})}Dg("diagram.voronoi",Og);Dg("voronoi",Og);var $S=pr,MS=$S.__importStar(wt),X0=or,_s=ir,xS=Mr,bS={field:"value",size:[1,1],nodeSize:null,separation:null,as:["x","y"]};function qg(r,n){if(r.dataType!==_s.DataSet.CONSTANTS.HIERARCHY||!r.root)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");var e=r.root;n=X0.assign({},bS,n);var t=n.as;if(!X0.isArray(t)||t.length!==2)throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ "x", "y" ])!');var i=void 0;try{i=xS.getField(n)}catch(l){console.warn(l)}i&&e.sum(function(l){return l[i]});var a=MS.cluster();a.size(n.size),n.nodeSize&&a.nodeSize(n.nodeSize),n.separation&&a.separation(n.separation),a(e);var o=t[0],s=t[1];e.each(function(l){l[o]=l.x,l[s]=l.y})}_s.DataSet.registerTransform("hierarchy.cluster",qg);_s.DataSet.registerTransform("dendrogram",qg);var Co={exports:{}};(function(r,n){(function(t,i){r.exports=i()})(typeof self!="undefined"?self:ao,function(){return function(e){var t={};function i(a){if(t[a])return t[a].exports;var o=t[a]={i:a,l:!1,exports:{}};return e[a].call(o.exports,o,o.exports,i),o.l=!0,o.exports}return i.m=e,i.c=t,i.d=function(a,o,s){i.o(a,o)||Object.defineProperty(a,o,{configurable:!1,enumerable:!0,get:s})},i.n=function(a){var o=a&&a.__esModule?function(){return a.default}:function(){return a};return i.d(o,"a",o),o},i.o=function(a,o){return Object.prototype.hasOwnProperty.call(a,o)},i.p="",i(i.s=5)}([function(e,t){function i(a,o,s,l){return l===void 0&&(l="height"),s==="center"?(a[l]+o[l])/2:a.height}e.exports={assign:Object.assign,getHeight:i}},function(e,t,i){var a=i(3),o=function(){function s(c,u){u===void 0&&(u={});var v=this;v.options=u,v.rootNode=a(c,u)}var l=s.prototype;return l.execute=function(){throw new Error("please override this method")},s}();e.exports=o},function(e,t,i){var a=i(4),o=["LR","RL","TB","BT","H","V"],s=["LR","RL","H"],l=function(f){return s.indexOf(f)>-1},c=o[0];e.exports=function(v,f,h){var g=f.direction||c;if(f.isHorizontal=l(g),g&&o.indexOf(g)===-1)throw new TypeError("Invalid direction: "+g);if(g===o[0])h(v,f);else if(g===o[1])h(v,f),v.right2left();else if(g===o[2])h(v,f);else if(g===o[3])h(v,f),v.bottom2top();else if(g===o[4]||g===o[5]){var p=a(v,f),d=p.left,m=p.right;h(d,f),h(m,f),f.isHorizontal?d.right2left():d.bottom2top(),m.translate(d.x-m.x,d.y-m.y),v.x=d.x,v.y=m.y;var y=v.getBoundingBox();f.isHorizontal?y.top<0&&v.translate(0,-y.top):y.left<0&&v.translate(-y.left,0)}var E=f.fixedRoot;return E===void 0&&(E=!0),E&&v.translate(-(v.x+v.width/2+v.hgap),-(v.y+v.height/2+v.vgap)),u(v,f),v};function u(v,f){if(f.radial){var h=f.isHorizontal?["x","y"]:["y","x"],g=h[0],p=h[1],d={x:1/0,y:1/0},m={x:-1/0,y:-1/0},y=0;v.DFTraverse(function($){y++;var x=$.x,I=$.y;d.x=Math.min(d.x,x),d.y=Math.min(d.y,I),m.x=Math.max(m.x,x),m.y=Math.max(m.y,I)});var E=m[p]-d[p];if(E===0)return;var S=Math.PI*2/y;v.DFTraverse(function($){var x=($[p]-d[p])/E*(Math.PI*2-S)+S,I=$[g]-v[g];$.x=Math.cos(x)*I,$.y=Math.sin(x)*I})}}},function(e,t,i){var a=i(0),o=18,s=o*2,l=o,c={getId:function(h){return h.id||h.name},getPreH:function(h){return h.preH||0},getPreV:function(h){return h.preV||0},getHGap:function(h){return h.hgap||l},getVGap:function(h){return h.vgap||l},getChildren:function(h){return h.children},getHeight:function(h){return h.height||s},getWidth:function(h){var g=h.label||" ";return h.width||g.split("").length*o}};function u(f,h){var g=this;if(g.vgap=g.hgap=0,f instanceof u)return f;g.data=f;var p=h.getHGap(f),d=h.getVGap(f);return g.preH=h.getPreH(f),g.preV=h.getPreV(f),g.width=h.getWidth(f),g.height=h.getHeight(f),g.width+=g.preH,g.height+=g.preV,g.id=h.getId(f),g.x=g.y=0,g.depth=0,g.children||(g.children=[]),g.addGap(p,d),g}a.assign(u.prototype,{isRoot:function(){return this.depth===0},isLeaf:function(){return this.children.length===0},addGap:function(h,g){var p=this;p.hgap+=h,p.vgap+=g,p.width+=2*h,p.height+=2*g},eachNode:function(h){for(var g=this,p=[g],d;d=p.shift();)h(d),p=d.children.concat(p)},DFTraverse:function(h){this.eachNode(h)},BFTraverse:function(h){for(var g=this,p=[g],d;d=p.shift();)h(d),p=p.concat(d.children)},getBoundingBox:function(){var h={left:Number.MAX_VALUE,top:Number.MAX_VALUE,width:0,height:0};return this.eachNode(function(g){h.left=Math.min(h.left,g.x),h.top=Math.min(h.top,g.y),h.width=Math.max(h.width,g.x+g.width),h.height=Math.max(h.height,g.y+g.height)}),h},translate:function(h,g){h===void 0&&(h=0),g===void 0&&(g=0),this.eachNode(function(p){p.x+=h,p.y+=g,p.x+=p.preH,p.y+=p.preV})},right2left:function(){var h=this,g=h.getBoundingBox();h.eachNode(function(p){p.x=p.x-(p.x-g.left)*2-p.width}),h.translate(g.width,0)},bottom2top:function(){var h=this,g=h.getBoundingBox();h.eachNode(function(p){p.y=p.y-(p.y-g.top)*2-p.height}),h.translate(0,g.height)}});function v(f,h,g){h===void 0&&(h={}),h=a.assign({},c,h);var p=new u(f,h),d=[p],m;if(!g&&!f.collapsed){for(;m=d.shift();)if(!m.data.collapsed){var y=h.getChildren(m.data),E=y?y.length:0;if(m.children=new Array(E),y&&E)for(var S=0;S<E;S++){var $=new u(y[S],h);m.children[S]=$,d.push($),$.parent=m,$.depth=m.depth+1}}}return p}e.exports=v},function(e,t,i){var a=i(3);e.exports=function(o,s){for(var l=a(o.data,s,!0),c=a(o.data,s,!0),u=o.children.length,v=Math.round(u/2),f=s.getSide||function(d,m){return m<v?"right":"left"},h=0;h<u;h++){var g=o.children[h],p=f(g,h);p==="right"?c.children.push(g):l.children.push(g)}return l.eachNode(function(d){d.isRoot()||(d.side="left")}),c.eachNode(function(d){d.isRoot()||(d.side="right")}),{left:l,right:c}}},function(e,t,i){var a={compactBox:i(6),dendrogram:i(8),indented:i(10),mindmap:i(12)};e.exports=a},function(e,t,i){function a(g,p){g.prototype=Object.create(p.prototype),g.prototype.constructor=g,o(g,p)}function o(g,p){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(d,m){return d.__proto__=m,d},o(g,p)}var s=i(1),l=i(7),c=i(2),u=i(0),v=function(g){function p(){return g.apply(this,arguments)||this}a(p,g);var d=p.prototype;return d.execute=function(){var y=this;return c(y.rootNode,y.options,l)},p}(s),f={};function h(g,p){return p=u.assign({},f,p),new v(g,p).execute()}e.exports=h},function(e,t){function i(u,v,f,h){h===void 0&&(h=[]);var g=this;g.w=u||0,g.h=v||0,g.y=f||0,g.x=0,g.c=h||[],g.cs=h.length,g.prelim=0,g.mod=0,g.shift=0,g.change=0,g.tl=null,g.tr=null,g.el=null,g.er=null,g.msel=0,g.mser=0}i.fromNode=function(u,v){if(!u)return null;var f=[];return u.children.forEach(function(h){f.push(i.fromNode(h,v))}),v?new i(u.height,u.width,u.x,f):new i(u.width,u.height,u.y,f)};function a(u,v,f){f?u.y+=v:u.x+=v,u.children.forEach(function(h){a(h,v,f)})}function o(u,v){var f=v?u.y:u.x;return u.children.forEach(function(h){f=Math.min(o(h,v),f)}),f}function s(u,v){var f=o(u,v);a(u,-f,v)}function l(u,v,f){f?v.y=u.x:v.x=u.x,u.c.forEach(function(h,g){l(h,v.children[g],f)})}function c(u,v,f){f===void 0&&(f=0),v?(u.x=f,f+=u.width):(u.y=f,f+=u.height),u.children.forEach(function(h){c(h,v,f)})}e.exports=function(u,v){v===void 0&&(v={});var f=v.isHorizontal;function h(k){if(k.cs===0){g(k);return}h(k.c[0]);for(var U=O(E(k.c[0].el),0,null),_=1;_<k.cs;++_){h(k.c[_]);var w=E(k.c[_].er);p(k,_,U),U=O(w,_,U)}x(k),g(k)}function g(k){k.cs===0?(k.el=k,k.er=k,k.msel=k.mser=0):(k.el=k.c[0].el,k.msel=k.c[0].msel,k.er=k.c[k.cs-1].er,k.mser=k.c[k.cs-1].mser)}function p(k,U,_){for(var w=k.c[U-1],b=w.mod,P=k.c[U],D=P.mod;w!==null&&P!==null;){E(w)>_.low&&(_=_.nxt);var rr=b+w.prelim+w.w-(D+P.prelim);rr>0&&(D+=rr,d(k,U,_.index,rr));var hr=E(w),ur=E(P);hr<=ur&&(w=y(w),w!==null&&(b+=w.mod)),hr>=ur&&(P=m(P),P!==null&&(D+=P.mod))}!w&&!!P?S(k,U,P,D):!!w&&!P&&$(k,U,w,b)}function d(k,U,_,w){k.c[U].mod+=w,k.c[U].msel+=w,k.c[U].mser+=w,C(k,U,_,w)}function m(k){return k.cs===0?k.tl:k.c[0]}function y(k){return k.cs===0?k.tr:k.c[k.cs-1]}function E(k){return k.y+k.h}function S(k,U,_,w){var b=k.c[0].el;b.tl=_;var P=w-_.mod-k.c[0].msel;b.mod+=P,b.prelim-=P,k.c[0].el=k.c[U].el,k.c[0].msel=k.c[U].msel}function $(k,U,_,w){var b=k.c[U].er;b.tr=_;var P=w-_.mod-k.c[U].mser;b.mod+=P,b.prelim-=P,k.c[U].er=k.c[U-1].er,k.c[U].mser=k.c[U-1].mser}function x(k){k.prelim=(k.c[0].prelim+k.c[0].mod+k.c[k.cs-1].mod+k.c[k.cs-1].prelim+k.c[k.cs-1].w)/2-k.w/2}function I(k,U){U+=k.mod,k.x=k.prelim+U,L(k);for(var _=0;_<k.cs;_++)I(k.c[_],U)}function C(k,U,_,w){if(_!==U-1){var b=U-_;k.c[_+1].shift+=w/b,k.c[U].shift-=w/b,k.c[U].change-=w-w/b}}function L(k){for(var U=0,_=0,w=0;w<k.cs;w++)U+=k.c[w].shift,_+=U+k.c[w].change,k.c[w].mod+=_}function O(k,U,_){for(;_!==null&&k>=_.low;)_=_.nxt;return{low:k,index:U,nxt:_}}c(u,f);var A=i.fromNode(u,f);return h(A),I(A,0),l(A,u,f),s(u,f),u}},function(e,t,i){function a(g,p){g.prototype=Object.create(p.prototype),g.prototype.constructor=g,o(g,p)}function o(g,p){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(d,m){return d.__proto__=m,d},o(g,p)}var s=i(1),l=i(9),c=i(2),u=i(0),v=function(g){function p(){return g.apply(this,arguments)||this}a(p,g);var d=p.prototype;return d.execute=function(){var y=this;return y.rootNode.width=0,c(y.rootNode,y.options,l)},p}(s),f={};function h(g,p){return p=u.assign({},f,p),new v(g,p).execute()}e.exports=h},function(e,t,i){var a=i(0);function o(c,u){u===void 0&&(u=[]);var v=this;v.x=v.y=0,v.leftChild=v.rightChild=null,v.height=0,v.children=u}var s={isHorizontal:!0,nodeSep:20,nodeSize:20,rankSep:200,subTreeSep:10};function l(c,u,v){v?(u.x=c.x,u.y=c.y):(u.x=c.y,u.y=c.x),c.children.forEach(function(f,h){l(f,u.children[h],v)})}e.exports=function(c,u){u===void 0&&(u={}),u=a.assign({},s,u);var v=0;function f(m){if(!m)return null;m.width=0,m.depth&&m.depth>v&&(v=m.depth);var y=m.children,E=y.length,S=new o(m.height,[]);return y.forEach(function($,x){var I=f($);S.children.push(I),x===0&&(S.leftChild=I),x===E-1&&(S.rightChild=I)}),S.originNode=m,S.isLeaf=m.isLeaf(),S}function h(m){if(m.isLeaf||m.children.length===0)m.drawingDepth=v;else{var y=m.children.map(function(S){return h(S)}),E=Math.min.apply(null,y);m.drawingDepth=E-1}return m.drawingDepth}var g;function p(m){m.x=m.drawingDepth*u.rankSep,m.isLeaf?(m.y=0,g&&(m.y=g.y+g.height+u.nodeSep,m.originNode.parent!==g.originNode.parent&&(m.y+=u.subTreeSep)),g=m):(m.children.forEach(function(y){p(y)}),m.y=(m.leftChild.y+m.rightChild.y)/2)}var d=f(c);return h(d),p(d),l(d,c,u.isHorizontal),c}},function(e,t,i){function a(d,m){d.prototype=Object.create(m.prototype),d.prototype.constructor=d,o(d,m)}function o(d,m){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(y,E){return y.__proto__=E,y},o(d,m)}var s=i(1),l=i(11),c=i(4),u=i(0),v=["LR","RL","H"],f=v[0],h=function(d){function m(){return d.apply(this,arguments)||this}a(m,d);var y=m.prototype;return y.execute=function(){var S=this,$=S.options,x=S.rootNode;$.isHorizontal=!0;var I=$.indent,C=I===void 0?20:I,L=$.dropCap,O=L===void 0?!0:L,A=$.direction,k=A===void 0?f:A,U=$.align;if(k&&v.indexOf(k)===-1)throw new TypeError("Invalid direction: "+k);if(k===v[0])l(x,C,O,U);else if(k===v[1])l(x,C,O,U),x.right2left();else if(k===v[2]){var _=c(x,$),w=_.left,b=_.right;l(w,C,O,U),w.right2left(),l(b,C,O,U);var P=w.getBoundingBox();b.translate(P.width,0),x.x=b.x-x.width/2}return x},m}(s),g={};function p(d,m){return m=u.assign({},g,m),new h(d,m).execute()}e.exports=p},function(e,t,i){var a=i(0);function o(s,l,c,u,v){var f=(typeof c=="function"?c(s):c)*s.depth;if(!u)try{if(s.id===s.parent.children[0].id){s.x+=f,s.y=l?l.y:0;return}}catch{}if(s.x+=f,l){if(s.y=l.y+a.getHeight(l,s,v),l.parent&&s.parent.id!==l.parent.id){var h=l.parent,g=h.y+a.getHeight(h,s,v);s.y=g>s.y?g:s.y}}else s.y=0}e.exports=function(s,l,c,u){var v=null;s.eachNode(function(f){o(f,v,l,c,u),v=f})}},function(e,t,i){function a(g,p){g.prototype=Object.create(p.prototype),g.prototype.constructor=g,o(g,p)}function o(g,p){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(d,m){return d.__proto__=m,d},o(g,p)}var s=i(1),l=i(13),c=i(2),u=i(0),v=function(g){function p(){return g.apply(this,arguments)||this}a(p,g);var d=p.prototype;return d.execute=function(){var y=this;return c(y.rootNode,y.options,l)},p}(s),f={};function h(g,p){return p=u.assign({},f,p),new v(g,p).execute()}e.exports=h},function(e,t,i){var a=i(0);function o(c,u){var v=0;return c.children.length?c.children.forEach(function(f){v+=o(f,u)}):v=c.height,c._subTreeSep=u.getSubTreeSep(c.data),c.totalHeight=Math.max(c.height,v)+2*c._subTreeSep,c.totalHeight}function s(c){var u=c.children,v=u.length;if(v){u.forEach(function(m){s(m)});var f=u[0],h=u[v-1],g=h.y-f.y+h.height,p=0;if(u.forEach(function(m){p+=m.totalHeight}),g>c.height)c.y=f.y+g/2-c.height/2;else if(u.length!==1||c.height>p){var d=c.y+(c.height-g)/2-f.y;u.forEach(function(m){m.translate(0,d)})}else c.y=(f.y+f.height/2+h.y+h.height/2)/2-c.height/2}}var l={getSubTreeSep:function(){return 0}};e.exports=function(c,u){u===void 0&&(u={}),u=a.assign({},l,u),c.parent={x:0,width:0,height:0,y:0},c.BFTraverse(function(v){v.x=v.parent.x+v.parent.width}),c.parent=null,o(c,u),c.startY=0,c.y=c.totalHeight/2-c.height/2,c.eachNode(function(v){var f=v.children,h=f.length;if(h){var g=f[0];if(g.startY=v.startY+v._subTreeSep,h===1)g.y=v.y+v.height/2-g.height/2;else{g.y=g.startY+g.totalHeight/2-g.height/2;for(var p=1;p<h;p++){var d=f[p];d.startY=f[p-1].startY+f[p-1].totalHeight,d.y=d.startY+d.totalHeight/2-d.height/2}}}}),s(c)}}])})})(Co);var PS=pr,RS=PS.__importDefault(Co.exports),Ei=ir,kS={};function Lo(r,n){var e=r.root;if(n=Object.assign({},kS,n),r.dataType!==Ei.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");r.root=RS.default.compactBox(e,n)}Ei.DataSet.registerTransform("hierarchy.compact-box",Lo);Ei.DataSet.registerTransform("compact-box-tree",Lo);Ei.DataSet.registerTransform("non-layered-tidy-tree",Lo);Ei.DataSet.registerTransform("mindmap-logical",Lo);var CS=pr,LS=CS.__importDefault(Co.exports),Ts=ir,IS={};function Fg(r,n){var e=r.root;if(n=Object.assign({},IS,n),r.dataType!==Ts.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");r.root=LS.default.dendrogram(e,n)}Ts.DataSet.registerTransform("hierarchy.dendrogram",Fg);Ts.DataSet.registerTransform("dendrogram",Fg);var NS=pr,AS=NS.__importDefault(Co.exports),$s=ir,DS={};function zg(r,n){var e=r.root;if(n=Object.assign({},DS,n),r.dataType!==$s.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");r.root=AS.default.indented(e,n)}$s.DataSet.registerTransform("hierarchy.indented",zg);$s.DataSet.registerTransform("indented-tree",zg);var OS=pr,j0=or,qS=OS.__importStar(wt),Io=ir,FS=Mr,zS={field:"value",size:[1,1],padding:0,as:["x","y","r"]};function Ms(r,n){if(r.dataType!==Io.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");var e=r.root;n=j0.assign({},zS,n);var t=n.as;if(!j0.isArray(t)||t.length!==3)throw new TypeError('Invalid as: it must be an array with 3 strings (e.g. [ "x", "y", "r" ])!');var i;try{i=FS.getField(n)}catch(c){console.warn(c)}i&&e.sum(function(c){return c[i]}).sort(function(c,u){return u[i]-c[i]});var a=qS.pack();a.size(n.size),n.padding&&a.padding(n.padding),a(e);var o=t[0],s=t[1],l=t[2];e.each(function(c){c[o]=c.x,c[s]=c.y,c[l]=c.r})}Io.DataSet.registerTransform("hierarchy.pack",Ms);Io.DataSet.registerTransform("hierarchy.circle-packing",Ms);Io.DataSet.registerTransform("circle-packing",Ms);var BS=pr,GS=BS.__importStar(wt),W0=or,xs=ir,US=Mr,HS={field:"value",size:[1,1],round:!1,padding:0,sort:!0,as:["x","y"]};function Bg(r,n){if(r.dataType!==xs.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");var e=r.root;n=W0.assign({},HS,n);var t=n.as;if(!W0.isArray(t)||t.length!==2)throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ "x", "y" ])!');var i;try{i=US.getField(n)}catch(l){console.warn(l)}i&&e.sum(function(l){return l[i]});var a=GS.partition();a.size(n.size).round(n.round).padding(n.padding),a(e);var o=t[0],s=t[1];e.each(function(l){l[o]=[l.x0,l.x1,l.x1,l.x0],l[s]=[l.y1,l.y1,l.y0,l.y0],["x0","x1","y0","y1"].forEach(function(c){t.indexOf(c)===-1&&delete l[c]})})}xs.DataSet.registerTransform("hierarchy.partition",Bg);xs.DataSet.registerTransform("adjacency",Bg);var YS=pr,VS=YS.__importStar(wt),J0=or,bs=ir,XS=Mr,jS={field:"value",size:[1,1],nodeSize:null,separation:null,as:["x","y"]};function Gg(r,n){if(r.dataType!==bs.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");var e=r.root;n=J0.assign({},jS,n);var t=n.as;if(!J0.isArray(t)||t.length!==2)throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ "x", "y" ])!');var i;try{i=XS.getField(n)}catch(l){console.warn(l)}i&&e.sum(function(l){return l[i]});var a=VS.tree();a.size(n.size),n.nodeSize&&a.nodeSize(n.nodeSize),n.separation&&a.separation(n.separation),a(e);var o=t[0],s=t[1];e.each(function(l){l[o]=l.x,l[s]=l.y})}bs.DataSet.registerTransform("hierarchy.tree",Gg);bs.DataSet.registerTransform("tree",Gg);var WS=pr,K0=WS.__importStar(wt),Q0=or,Ps=ir,JS=Mr,KS={field:"value",tile:"treemapSquarify",size:[1,1],round:!1,padding:0,paddingInner:0,paddingOuter:0,paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0,as:["x","y"]};function Ug(r,n){if(r.dataType!==Ps.DataSet.CONSTANTS.HIERARCHY)throw new TypeError("Invalid DataView: This transform is for Hierarchy data only!");var e=r.root;n=Q0.assign({},KS,n);var t=n.as;if(!Q0.isArray(t)||t.length!==2)throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ "x", "y" ])!');var i;try{i=JS.getField(n)}catch(l){console.warn(l)}i&&e.sum(function(l){return l[i]});var a=K0.treemap();a.tile(K0[n.tile]).size(n.size).round(n.round).padding(n.padding).paddingInner(n.paddingInner).paddingOuter(n.paddingOuter).paddingTop(n.paddingTop).paddingRight(n.paddingRight).paddingBottom(n.paddingBottom).paddingLeft(n.paddingLeft),a(e);var o=t[0],s=t[1];e.each(function(l){l[o]=[l.x0,l.x1,l.x1,l.x0],l[s]=[l.y1,l.y1,l.y0,l.y0],["x0","x1","y0","y1"].forEach(function(c){t.indexOf(c)===-1&&delete l[c]})})}Ps.DataSet.registerTransform("hierarchy.treemap",Ug);Ps.DataSet.registerTransform("treemap",Ug);var Rs={};Object.defineProperty(Rs,"__esModule",{value:!0});var Z0=pr,Cu=Math.PI/180,jt=1<<11>>5,fa=1<<11;function QS(r){return r.text}function ZS(){return"serif"}function r1(){return"normal"}function r7(r){return r.value}function n7(){return~~(Math.random()*2)*90}function e7(){return 1}function t7(r,n,e,t){if(!n.sprite){var i=r.context,a=r.ratio;i.clearRect(0,0,(jt<<5)/a,fa/a);var o=0,s=0,l=0,c=e.length;for(--t;++t<c;){n=e[t],i.save(),i.font=n.style+" "+n.weight+" "+~~((n.size+1)/a)+"px "+n.font;var u=i.measureText(n.text+"m").width*a,v=n.size<<1;if(n.rotate){var f=Math.sin(n.rotate*Cu),h=Math.cos(n.rotate*Cu),g=u*h,p=u*f,d=v*h,m=v*f;u=Math.max(Math.abs(g+m),Math.abs(g-m))+31>>5<<5,v=~~Math.max(Math.abs(p+d),Math.abs(p-d))}else u=u+31>>5<<5;if(v>l&&(l=v),o+u>=jt<<5&&(o=0,s+=l,l=0),s+v>=fa)break;i.translate((o+(u>>1))/a,(s+(v>>1))/a),n.rotate&&i.rotate(n.rotate*Cu),i.fillText(n.text,0,0),n.padding&&(i.lineWidth=2*n.padding,i.strokeText(n.text,0,0)),i.restore(),n.width=u,n.height=v,n.xoff=o,n.yoff=s,n.x1=u>>1,n.y1=v>>1,n.x0=-n.x1,n.y0=-n.y1,n.hasText=!0,o+=u}for(var y=i.getImageData(0,0,(jt<<5)/a,fa/a).data,E=[];--t>=0;)if(n=e[t],!!n.hasText){for(var u=n.width,S=u>>5,v=n.y1-n.y0,$=0;$<v*S;$++)E[$]=0;if(o=n.xoff,o==null)return;s=n.yoff;for(var x=0,I=-1,C=0;C<v;C++){for(var $=0;$<u;$++){var L=S*C+($>>5),O=y[(s+C)*(jt<<5)+(o+$)<<2]?1<<31-$%32:0;E[L]|=O,x|=O}x?I=C:(n.y0++,v--,C--,s++)}n.y1=n.y0+I,n.sprite=E.slice(0,(n.y1-n.y0)*S)}}}function i7(r,n,e){e>>=5;for(var t=r.sprite,i=r.width>>5,a=r.x-(i<<4),o=a&127,s=32-o,l=r.y1-r.y0,c=(r.y+r.y0)*e+(a>>5),u,v=0;v<l;v++){u=0;for(var f=0;f<=i;f++)if((u<<s|(f<i?(u=t[v*i+f])>>>o:0))&n[c+f])return!0;c+=e}return!1}function a7(r,n){var e=r[0],t=r[1];n.x+n.x0<e.x&&(e.x=n.x+n.x0),n.y+n.y0<e.y&&(e.y=n.y+n.y0),n.x+n.x1>t.x&&(t.x=n.x+n.x1),n.y+n.y1>t.y&&(t.y=n.y+n.y1)}function o7(r,n){return r.x+r.x1>n[0].x&&r.x+r.x0<n[1].x&&r.y+r.y1>n[0].y&&r.y+r.y0<n[1].y}function Hg(r){var n=r[0]/r[1];return function(e){return[n*(e*=.1)*Math.cos(e),e*Math.sin(e)]}}function u7(r){var n=4,e=n*r[0]/r[1],t=0,i=0;return function(a){var o=a<0?-1:1;switch(Math.sqrt(1+4*o*a)-o&3){case 0:t+=e;break;case 1:i+=n;break;case 2:t-=e;break;default:i-=n;break}return[t,i]}}function n1(r){for(var n=[],e=-1;++e<r;)n[e]=0;return n}function f7(){return document.createElement("canvas")}function se(r){return typeof r=="function"?r:function(){return r}}var l7={archimedean:Hg,rectangular:u7};function s7(){var r=[256,256],n=QS,e=ZS,t=r7,i=r1,a=r1,o=n7,s=e7,l=Hg,c=[],u=1/0,v=Math.random,f=f7,h={};h.canvas=function(d){return arguments.length?(f=se(d),h):f},h.start=function(){var d=Z0.__read(r,2),m=d[0],y=d[1],E=g(f()),S=h.board?h.board:n1((r[0]>>5)*r[1]),$=c.length,x=[],I=c.map(function(A,k){return A.text=n.call(this,A,k),A.font=e.call(this,A,k),A.style=i.call(this,A,k),A.weight=a.call(this,A,k),A.rotate=o.call(this,A,k),A.size=~~t.call(this,A,k),A.padding=s.call(this,A,k),A}).sort(function(A,k){return k.size-A.size}),C=-1,L=h.board?[{x:0,y:0},{x:m,y}]:null;O();function O(){for(var A=Date.now();Date.now()-A<u&&++C<$;){var k=I[C];k.x=m*(v()+.5)>>1,k.y=y*(v()+.5)>>1,t7(E,k,I,C),k.hasText&&p(S,k,L)&&(x.push(k),L?h.hasImage||a7(L,k):L=[{x:k.x+k.x0,y:k.y+k.y0},{x:k.x+k.x1,y:k.y+k.y1}],k.x-=r[0]>>1,k.y-=r[1]>>1)}h._tags=x,h._bounds=L}return h};function g(d){d.width=d.height=1;var m=Math.sqrt(d.getContext("2d").getImageData(0,0,1,1).data.length>>2);d.width=(jt<<5)/m,d.height=fa/m;var y=d.getContext("2d");return y.fillStyle=y.strokeStyle="red",y.textAlign="center",{context:y,ratio:m}}function p(d,m,y){for(var E=m.x,S=m.y,$=Math.sqrt(r[0]*r[0]+r[1]*r[1]),x=l(r),I=v()<.5?1:-1,C,L=-I,O,A;(C=x(L+=I))&&(O=~~C[0],A=~~C[1],!(Math.min(Math.abs(O),Math.abs(A))>=$));)if(m.x=E+O,m.y=S+A,!(m.x+m.x0<0||m.y+m.y0<0||m.x+m.x1>r[0]||m.y+m.y1>r[1])&&(!y||!i7(m,d,r[0]))&&(!y||o7(m,y))){for(var k=m.sprite,U=m.width>>5,_=r[0]>>5,w=m.x-(U<<4),b=w&127,P=32-b,D=m.y1-m.y0,rr=void 0,hr=(m.y+m.y0)*_+(w>>5),ur=0;ur<D;ur++){rr=0;for(var gr=0;gr<=U;gr++)d[hr+gr]|=rr<<P|(gr<U?(rr=k[ur*U+gr])>>>b:0);hr+=_}return delete m.sprite,!0}return!1}return h.createMask=function(d){var m=document.createElement("canvas"),y=Z0.__read(r,2),E=y[0],S=y[1];if(!(!E||!S)){var $=E>>5,x=n1((E>>5)*S);m.width=E,m.height=S;var I=m.getContext("2d");I.drawImage(d,0,0,d.width,d.height,0,0,E,S);for(var C=I.getImageData(0,0,E,S).data,L=0;L<S;L++)for(var O=0;O<E;O++){var A=$*L+(O>>5),k=L*E+O<<2,U=C[k]>=250&&C[k+1]>=250&&C[k+2]>=250,_=U?1<<31-O%32:0;x[A]|=_}h.board=x,h.hasImage=!0}},h.timeInterval=function(d){return arguments.length?(u=d==null?1/0:d,h):u},h.words=function(d){return arguments.length?(c=d,h):c},h.size=function(d){return arguments.length?(r=[+d[0],+d[1]],h):r},h.font=function(d){return arguments.length?(e=se(d),h):e},h.fontStyle=function(d){return arguments.length?(i=se(d),h):i},h.fontWeight=function(d){return arguments.length?(a=se(d),h):a},h.rotate=function(d){return arguments.length?(o=se(d),h):o},h.text=function(d){return arguments.length?(n=se(d),h):n},h.spiral=function(d){return arguments.length?(l=l7[d]||d,h):l},h.fontSize=function(d){return arguments.length?(t=se(d),h):t},h.padding=function(d){return arguments.length?(s=se(d),h):s},h.random=function(d){return arguments.length?(v=d,h):v},h}Rs.default=s7;var If=pr,Lu=or,Yg=ir,c7=If.__importDefault(Rs),v7=Mr,h7={fields:["text","value"],font:function(){return"serif"},padding:1,size:[500,500],spiral:"archimedean",timeInterval:500};function Vg(r,n){n=Lu.assign({},h7,n);var e=c7.default();if(!n.size[0]||!n.size[1]){r.rows=[],r._tagCloud=e;return}["font","fontSize","fontWeight","padding","rotate","size","spiral","timeInterval"].forEach(function(p){n[p]&&e[p](n[p])});var t=v7.getFields(n),i=If.__read(t,2),a=i[0],o=i[1];if(!Lu.isString(a)||!Lu.isString(o))throw new TypeError('Invalid fields: must be an array with 2 strings (e.g. [ "text", "value" ])!');var s=r.rows.map(function(p){return p.text=p[a],p.value=p[o],p});e.words(s),n.imageMask&&e.createMask(n.imageMask);var l=e.start(),c=l._tags,u=l._bounds||[{x:0,y:0},{x:n.size[0],y:n.size[1]}];c.forEach(function(p){p.x+=n.size[0]/2,p.y+=n.size[1]/2});var v=If.__read(n.size,2),f=v[0],h=v[1],g=l.hasImage;c.push({text:"",value:0,x:g?0:u[0].x,y:g?0:u[0].y,opacity:0}),c.push({text:"",value:0,x:g?f:u[1].x,y:g?h:u[1].y,opacity:0}),r.rows=c,r._tagCloud=l}Yg.DataSet.registerTransform("tag-cloud",Vg);Yg.DataSet.registerTransform("word-cloud",Vg);var la=pr,Pe=or,g7=Dn.exports,p7=la.__importDefault(Tn),d7=ir,m7=Mr,y7={fields:["name","value"],rows:5,size:[1,1],scale:1,groupBy:[],maxCount:1e3,gapRatio:.1,as:["x","y"]};function w7(r,n){n=Pe.assign({},y7,n);var e=m7.getFields(n),t=la.__read(e,2),i=t[0],a=t[1],o=la.__read(n.as,2),s=o[0],l=o[1],c=n.groupBy,u=p7.default(r.rows,c),v=Pe.keys(u),f=la.__read(n.size,2),h=f[0],g=f[1],p=n.maxCount,d=v.length,m=g/d,y=n.rows,E=n.gapRatio,S=[],$=n.scale,x=0,I=0;Pe.forIn(u,function(C){var L=g7.sum(Pe.map(C,function(A){return A[a]})),O=Math.ceil(L*$/y);L*$>p&&($=p/L,O=Math.ceil(L*$/y)),I=h/O}),Pe.forIn(u,function(C){var L=[x*m,(x+1)*m],O=L[1]-L[0],A=O*(1-E)/y,k=0,U=0;Pe.each(C,function(_){for(var w=_[a],b=Math.round(w*$),P=0;P<b;P++){U===y&&(U=0,k++);var D=Pe.pick(_,[i,a].concat(c));D[s]=k*I+I/2,D[l]=U*A+A/2+L[0],D._wStep=I,D._hStep=A,U++,S.push(D)}}),x+=1}),r.rows=S}d7.DataSet.registerTransform("waffle",w7);var Xg={};Object.defineProperty(Xg,"__esModule",{value:!0});var Je=pr,ce=or,e1=Je.__importDefault(Et),jg=Je.__importDefault(yi),Wg=ir,E7=Mr,t1=ie,S7={as:["x","y","z"],method:"gaussian"},Nf=ce.keys(jg.default);function Jg(r,n){var e,t;n=ce.assign({},S7,n);var i=E7.getFields(n);if(!ce.isArray(i)||i.length!==2)throw new TypeError("invalid fields: must be an array of 2 strings!");var a=Je.__read(n.as,3),o=a[0],s=a[1],l=a[2];if(!ce.isString(o)||!ce.isString(s)||!ce.isString(l))throw new TypeError("invalid as: must be an array of 3 strings!");var c;if(ce.isString(n.method)){if(Nf.indexOf(n.method)===-1)throw new TypeError("invalid method: "+n.method+". Must be one of "+Nf.join(", "));c=jg.default[n.method]}var u=Je.__read(i,2),v=u[0],f=u[1],h=n.extent,g=n.bandwidth,p,d;h&&Array.isArray(h)&&Array.isArray(h[0])&&Array.isArray(h[1])?(e=Je.__read(h,2),p=e[0],d=e[1]):(p=r.range(v),d=r.range(f));var m,y;g&&Array.isArray(g)&&g.slice(0,2).every(ce.isNumber)&&g.slice(0,2).every(function(w){return w>0})?(t=Je.__read(g,2),m=t[0],y=t[1]):(m=t1.silverman(r.getColumn(v)),y=t1.silverman(r.getColumn(f)));for(var E=e1.default(p,m),S=e1.default(d,y),$=r.rows.length,x=[],I=0;I<E.length;I++)for(var C=0;C<S.length;C++){for(var L=0,O=E[I],A=S[C],k=0;k<$;k++)L+=c((O-r.rows[k][v])/m)*c((A-r.rows[k][f])/y);var U=1/($*m*y)*L,_={};_[o]=O,_[s]=A,_[l]=U,x.push(_)}r.rows=x}Wg.DataSet.registerTransform("kernel-smooth.density",Jg);Wg.DataSet.registerTransform("kernel.density",Jg);Xg.default={KERNEL_METHODS:Nf};var Kg={};Object.defineProperty(Kg,"__esModule",{value:!0});var io=pr,Fn=or,_7=io.__importDefault(Et),Qg=io.__importDefault(yi),Zg=ir,Iu=Dn.exports,T7=Mr,$7=ie,M7={as:["x","y"],method:"gaussian"},Af=Fn.keys(Qg.default);function x7(r,n,e,t){var i=(t-e)/n;return r(i)}function i1(r){return function(n){return Fn.isArray(n)?n.map(function(e){return r(e)}):r(n)}}function rp(r,n){n=Fn.assign({},M7,n);var e=T7.getFields(n);if(!Fn.isArray(e)||!(e.length===1||e.length===2))throw new TypeError("invalid fields: must be an array of 1 or 2 strings!");var t=io.__read(n.as,2),i=t[0],a=t[1];if(!Fn.isString(i)||!Fn.isString(a))throw new TypeError("invalid as: must be an array of 2 strings!");var o,s=n.method;if(Fn.isString(s)){if(Af.indexOf(s)===-1)throw new TypeError("invalid method: "+s+". Must be one of "+Af.join(", "));o=Qg.default[s]}var l=io.__read(e,2),c=l[0],u=l[1],v=r.getColumn(c),f=n.extent;(f||!Fn.isArray(f))&&(f=r.range(c));var h=n.bandwidth;(!h||!Fn.isNumber(h)||h<=0)&&(h=$7.silverman(v));var g=_7.default(f,h),p=v.length,d=x7.bind(null,o,h),m;if(Fn.isNil(u))m=i1(function(S){var $=v.map(function(C){return d(S,C)}),x=Iu.sum($),I=p*h;return!x||!I?0:x/I});else{var y=r.getColumn(u);m=i1(function(S){var $=v.map(function(C){return d(S,C)}),x=Iu.sum($.map(function(C,L){return C*y[L]})),I=Iu.sum($);return!x||!I?0:x/I})}var E=g.map(function(S){var $={};return $[i]=S,$[a]=m(S),$});r.rows=E}Zg.DataSet.registerTransform("kernel-smooth.regression",rp);Zg.DataSet.registerTransform("kernel.regression",rp);Kg.default={KERNEL_METHODS:Af};var b7=ir,P7=b7.DataSet;function R7(r){const{data:n,loading:e,fields:t,height:i}=r,{DataView:a}=P7,o=new a().source(n);return o.transform({type:"fold",fields:t,key:"category",value:"score"}),br(a1,{loading:e,style:{width:"100%"},children:zn(Df,{height:i||400,padding:0,data:o.rows,autoFit:!0,scale:{score:{min:0,max:80}},interactions:["legend-highlight"],className:"chart-wrapper",children:[br(u1,{type:"polar",radius:.8}),br(f1,{shared:!0,children:(s,l)=>br(c1,{title:s,data:l})}),br(cp,{position:"item*score",size:"2",color:["category",["#313CA9","#21CCFF","#249EFF"]]}),br(vp,{position:"item*score",tooltip:!1,color:["category",["rgba(49, 60, 169, 0.4)","rgba(33, 204, 255, 0.4)","rgba(36, 158, 255, 0.4)"]]}),br(l1,{name:"score",label:!1}),br(s1,{position:"right",marker:(s,l)=>({symbol:"circle",style:{r:4,lineWidth:0,fill:["#313CA9","#21CCFF","#249EFF"][l]}}),name:"category"})]})})}function k7(r){return zn(Df,{theme:gp(),forceUpdate:!0,autoFit:!0,data:r.data,height:r.height||400,padding:[0,0,10,0],children:[br(s1,{visible:!0}),br(hp,{fields:["category"],type:"rect",showTitle:!1,eachView:(n,e)=>{const t=e.data;n.coordinate({type:"theta",cfg:{radius:.8,innerRadius:.7}}),n.interval().adjust("stack").position("value").color("type",["#249eff","#846BCE","#21CCFF"," #86DF6C","#0E42D2"]).label("value",{content:i=>`${(i.value*100).toFixed(2)} %`}),n.annotation().text({position:["50%","46%"],content:t[0].category,style:{fontSize:14,fontWeight:500,textAlign:"center"},offsetY:10}),n.interaction("element-single-selected")}})]})}const{Row:Nu,Col:Ji}=up,{Title:Ki}=fp;function H7(){const r=ap(pp),[n,e]=Me.exports.useState(!1),[t,i]=Me.exports.useState([]),[a,o]=Me.exports.useState(!1),[s,l]=Me.exports.useState({list:[],fields:[]}),[c,u]=Me.exports.useState(!1),[v,f]=Me.exports.useState([]),h=async()=>{e(!0);const{data:d}=await Oo.get("/api/multi-dimension/activity").finally(()=>{e(!1)});i(d)},g=async()=>{o(!0);const{data:d}=await Oo.get("/api/multi-dimension/polar").finally(()=>o(!1));l(d)},p=async()=>{u(!0);const{data:d}=await Oo.get("/api/multi-dimension/content-source").finally(()=>{u(!1)});f(d)};return Me.exports.useEffect(()=>{h(),g(),p()},[]),zn(op,{size:16,direction:"vertical",style:{width:"100%"},children:[zn(Nu,{gutter:20,children:[br(Ji,{span:16,children:zn(Si,{children:[br(Ki,{heading:6,children:r["multiDAnalysis.card.title.dataOverview"]}),br(dp,{})]})}),zn(Ji,{span:8,children:[zn(Si,{children:[br(Ki,{heading:6,children:r["multiDAnalysis.card.title.todayActivity"]}),br(yp,{data:t,loading:n,height:160})]}),zn(Si,{children:[br(Ki,{heading:6,children:r["multiDAnalysis.card.title.contentTheme"]}),br(R7,{data:s.list,fields:s.fields,height:197,loading:a})]})]})]}),br(Nu,{children:br(Ji,{span:24,children:br(mp,{})})}),br(Nu,{children:br(Ji,{span:24,children:zn(Si,{children:[br(Ki,{heading:6,children:r["multiDAnalysis.card.title.contentSource"]}),br(k7,{loading:c,data:v,height:240})]})})})]})}export{H7 as default};
