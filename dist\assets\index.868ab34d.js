import{b as a,a as t,j as o,h as r}from"./index.7dafa16d.js";import m from"./chat-panel.e87f81da.js";import l from"./studio.c8c6c399.js";import c from"./data-statistic.522db5ca.js";import d from"./studio-status.8b12e1f6.js";import n from"./quick-operation.43b9f59f.js";import p from"./studio-information.7cf983dc.js";import{s as i}from"./index.module.1dd0e4f6.js";import"./index.662e0e1f.js";import"./index.16dc0c3f.js";import"./item.3a5c9904.js";import"./index.5ef0222d.js";import"./index.305c74fb.js";import"./index.519f9d90.js";import"./index.503eee4e.js";import"./b-tween.es.d368a2a1.js";import"./data-statistic-list.7ace1de2.js";import"./index.69569894.js";function O(){const e=a(s=>s.userInfo);return t("div",{children:o("div",{className:i.layout,children:[t("div",{className:i["layout-left-side"],children:t(m,{})}),t("div",{className:i["layout-content"],children:o(r,{size:16,direction:"vertical",style:{width:"100%"},children:[t(l,{userInfo:e}),t(c,{})]})}),t("div",{className:i["layout-right-side"],children:o(r,{size:16,direction:"vertical",style:{width:"100%"},children:[t(d,{}),t(n,{}),t(p,{})]})})]})})}export{O as default};
